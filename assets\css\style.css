@charset "UTF-8";
/*
Template Name: Cigma
Template URL: https://themehour.com/html/cigma/
Description: Business Agency HTML Template
Author: themehour
Author URI: https://themeforest.net/user/themehour
Version: 1.0.0
*/
/*=================================
    CSS Index Here
==================================*/
/*

01. Theme Base
    1.1. Mixin
    1.2. Function
    1.3. Variable
    1.4. Typography
    1.5. Wordpress Default
02. Reset
    2.1. Container
    2.2. Grid
    2.3. Input
    2.4. Slider
    2.5. Mobile Menu
03. Utilities
    3.1. Preloader
    3.2. Buttons
    3.3. Titles
    3.4. Common
    3.6. Font
    3.7. Background
    3.8. Text Color
    3.9. Overlay
    3.10. Animation

04. Template Style
    4.1. Widget
    4.2. Header
    4.3. Footer
    4.4. Breadcumb
    4.5. Pagination
    4.6. Blog
    4.7. Comments
    4.8. Hero Area
    4.9. Error    
    4.00. Popup Search
    4.00. Popup Side Menu
    4.00. Wocommerce
    4.00. Products
    4.00. Cart
    4.00. Checkout
    4.00. Wishlist
    4.00. Contact
    4.00. About
    4.00. Team
    4.00. Testimonial
    4.00. Counter
    4.00. Blog
    4.00. Brand
    4.00. Simple Sections
    4.00. Why
    4.00. Faq
    4.00. Service
    4.00. Process
    4.00. Pricing
    4.00. Feature
    4.00. Project
    4.00. Category
    4.00. Color Scheme
    4.00. CTA
    4.00. Appointment
    4.00. Video
    4.00. Tab Menu
    4.00. Schedule

05. Spacing

*/
/*=================================
    CSS Index End
==================================*/
/*=================================
   01. Theme Base
==================================*/
/*------------------- 1.1. Mixin -------------------*/
/*------------------- 1.2. Function -------------------*/
/*------------------- 1.3. Variable-------------------*/
:root {
  --theme-color: #FF4F38;
  --theme-color2: #FFEC40;
  --title-color: #13182B;
  --body-color: #57585F;
  --smoke-color: #F8FAFE;
  --smoke-color2: #FFF7F6;
  --smoke-color3: #FFF3E0;
  --black-color: #000000;
  --black-color2: #202844;
  --th-border-color: #E1E4E5;
  --th-border-color2: #EEEFF2;
  --th-border-color3: rgba(87, 88, 95, 0.5);
  --th-border-color4: rgba(198, 201, 212, 0.5);
  --light-color: #C6C9D4;
  --gray-color: #D7D7D7;
  --gray-color2: #F4F3E6;
  --black-color3: #051311;
  --black-color4: #00110E;
  --black-color5: #22272E;
  --black-color6: #2D333D;
  --black-color7: #0E0E13;
  --white-color: #ffffff;
  --yellow-color: #FFB539;
  --success-color: #28a745;
  --error-color: #dc3545;
  --th-border-color5: #49515C;
  --th-border-color6: #383D46;
  --title-font: "DM Sans", sans-serif;
  --body-font: "Inter", sans-serif;
  --icon-font: "Font Awesome 6 Pro";
  --main-container: 1680px;
  --container-gutters: 24px;
  --section-space: 150px;
  --section-space-mobile: 80px;
  --section-title-space: 60px;
  --ripple-ani-duration: 5s;
  --th-body-background: #ffffff;
}

/*------------------- 1.4. Typography -------------------*/
html,
body {
  scroll-behavior: auto !important;
}

body {
  font-family: var(--body-font);
  font-size: 16px;
  font-weight: 400;
  color: var(--body-color);
  line-height: 26px;
  overflow-x: hidden;
  background: var(--th-body-background);
  -webkit-font-smoothing: antialiased;
  /***scroll-bar***/
}
body::-webkit-scrollbar {
  width: 10px;
  height: 10px;
  border-radius: 0px;
}
body::-webkit-scrollbar-track {
  background: rgba(252, 0, 18, 0.1);
  box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
  -webkit-box-shadow: inset 0 0 6px rgba(0, 0, 0, 0.3);
  border-radius: 0px;
}
body::-webkit-scrollbar-thumb {
  background-color: var(--theme-color);
  background-image: -webkit-linear-gradient(45deg, rgba(255, 255, 255, 0.3) 25%, transparent 20%, transparent 50%, rgba(255, 255, 255, 0.3) 50%, rgba(255, 255, 255, 0.3) 75%, transparent 75%, transparent);
  border-radius: 0px;
}

.theme-yellow {
  --theme-color: #FFEC40;
  --theme-color2: #FF4F38;
  --th-body-background: #13182B;
}
.theme-yellow .color-scheme-wrap .switchIcon {
  color: var(--black-color2);
}
.theme-yellow .preloader .th-btn {
  color: var(--title-color);
}
.theme-yellow .preloader .th-btn:hover {
  color: var(--white-color);
}
.theme-yellow .slider-drag-cursor {
  color: var(--title-color);
}
.theme-yellow .cursor-follower {
  mix-blend-mode: difference;
}

.th-bg-dark {
  --th-body-background: #13182B;
}

.theme-yellow2 {
  --theme-color: #FFEC40;
  --theme-color2: #3282FB;
}
.theme-yellow2 .color-scheme-wrap .switchIcon {
  color: var(--black-color2);
}
.theme-yellow2 .preloader .th-btn {
  color: var(--title-color);
}
.theme-yellow2 .preloader .th-btn:hover {
  color: var(--white-color);
}
.theme-yellow2 .slider-drag-cursor {
  color: var(--title-color);
}
.theme-yellow2 .cursor-follower {
  mix-blend-mode: difference;
}

iframe {
  border: none;
  width: 100%;
}

.slick-slide:focus,
button:focus,
a:focus,
a:active,
input,
input:hover,
input:focus,
input:active,
textarea,
textarea:hover,
textarea:focus,
textarea:active {
  outline: none;
}

input:focus {
  outline: none;
  box-shadow: none;
}

img:not([draggable]),
embed,
object,
video {
  max-width: 100%;
  height: auto;
}

ul {
  list-style-type: disc;
}

ol {
  list-style-type: decimal;
}

table {
  margin: 0 0 1.5em;
  width: 100%;
  border-collapse: collapse;
  border-spacing: 0;
  border: 1px solid var(--th-border-color);
}

th {
  font-weight: 700;
  color: var(--title-color);
}

td,
th {
  border: 1px solid var(--th-border-color);
  padding: 9px 12px;
}

a {
  color: var(--theme-color);
  text-decoration: none;
  outline: 0;
  transition: all ease 0.4s;
}
a:hover {
  color: var(--title-color);
}
a:active, a:focus, a:hover, a:visited {
  text-decoration: none;
  outline: 0;
}

button {
  transition: all ease 0.4s;
}

img {
  border: none;
  max-width: 100%;
}

ins {
  text-decoration: none;
}

pre {
  font-family: var(--body-font);
  background: #f5f5f5;
  color: #666;
  font-size: 14px;
  margin: 20px 0;
  overflow: auto;
  padding: 20px;
  white-space: pre-wrap;
  word-wrap: break-word;
}

span.ajax-loader:empty,
p:empty {
  display: none;
}

p {
  font-family: var(--body-font);
  margin: 0 0 18px 0;
  color: var(--body-color);
  line-height: 1.75;
}

h1 a,
h2 a,
h3 a,
h4 a,
h5 a,
h6 a,
p a,
span a {
  font-size: inherit;
  font-family: inherit;
  font-weight: inherit;
  line-height: inherit;
}

.h1,
h1,
.h2,
h2,
.h3,
h3,
.h4,
h4,
.h5,
h5,
.h6,
h6 {
  font-family: var(--title-font);
  color: var(--title-color);
  text-transform: none;
  font-weight: 700;
  line-height: 1.4;
  margin: 0 0 15px 0;
}

.h1,
h1 {
  font-size: 72px;
  line-height: 1.18;
}

.h2,
h2 {
  font-size: 56px;
  line-height: 1.227;
}

.h3,
h3 {
  font-size: 36px;
  line-height: 1.278;
}

.h4,
h4 {
  font-size: 30px;
  line-height: 1.333;
}

.h5,
h5 {
  font-size: 24px;
  line-height: 1.417;
}

.h6,
h6 {
  font-size: 20px;
  line-height: 1.5;
}

/* Medium Large devices */
@media (max-width: 1399px) {
  .h1,
  h1 {
    font-size: 64px;
    line-height: 1.3;
  }
}
/* Medium Large devices */
@media (max-width: 1299px) {
  .h1,
  h1 {
    font-size: 60px;
  }
}
/* Large devices */
@media (max-width: 1199px) {
  .h1,
  h1 {
    font-size: 55px;
    line-height: 1.3;
  }
  .h2,
  h2 {
    font-size: 36px;
    line-height: 1.3;
  }
  .h3,
  h3 {
    font-size: 30px;
  }
  .h4,
  h4 {
    font-size: 24px;
  }
  .h5,
  h5 {
    font-size: 20px;
  }
  .h6,
  h6 {
    font-size: 16px;
  }
}
/* Small devices */
@media (max-width: 767px) {
  .h1,
  h1 {
    font-size: 40px;
  }
  .h2,
  h2 {
    font-size: 34px;
    line-height: 1.3;
  }
  .h3,
  h3 {
    font-size: 26px;
  }
  .h4,
  h4 {
    font-size: 22px;
  }
  .h5,
  h5 {
    font-size: 18px;
  }
  .h6,
  h6 {
    font-size: 16px;
  }
}
/* Extra small devices */
@media (max-width: 575px) {
  .h1,
  h1 {
    font-size: 34px;
    line-height: 1.35;
  }
  .h2,
  h2 {
    font-size: 28px;
  }
}
/* Extra small devices */
@media (max-width: 375px) {
  .h1,
  h1 {
    font-size: 32px;
  }
}
.cursor-follower {
  position: fixed;
  background: var(--title-color);
  border: 1px solid var(--title-color);
  width: 15px;
  height: 15px;
  border-radius: 100%;
  z-index: 999999;
  -webkit-transition: 0.2s cubic-bezier(0.75, -0.27, 0.3, 1.33) opacity, 0.2s cubic-bezier(0.75, -0.27, 0.3, 1.33) background, 0.6s cubic-bezier(0.75, -1.27, 0.3, 2.33) -webkit-transform;
  transition: 0.2s cubic-bezier(0.75, -0.27, 0.3, 1.33) opacity, 0.2s cubic-bezier(0.75, -0.27, 0.3, 1.33) background, 0.6s cubic-bezier(0.75, -1.27, 0.3, 2.33) -webkit-transform;
  transition: 0.6s cubic-bezier(0.75, -1.27, 0.3, 2.33) transform, 0.2s cubic-bezier(0.75, -0.27, 0.3, 1.33) opacity, 0.2s cubic-bezier(0.75, -0.27, 0.3, 1.33) background;
  transition: 0.6s cubic-bezier(0.75, -1.27, 0.3, 2.33) transform, 0.2s cubic-bezier(0.75, -0.27, 0.3, 1.33) opacity, 0.2s cubic-bezier(0.75, -0.27, 0.3, 1.33) background, 0.6s cubic-bezier(0.75, -1.27, 0.3, 2.33) -webkit-transform;
  user-select: none;
  pointer-events: none;
  transform: translate(2px, 2px);
  opacity: 1;
  mix-blend-mode: multiply;
}
.cursor-follower.cursor-follower-big {
  transform: translate(2px, 2px) scale(3);
}

/*------------------- 1.5. Wordpress Default -------------------*/
p.has-drop-cap {
  margin-bottom: 20px;
}

.page--item p:last-child .alignright {
  clear: right;
}

.blog-title,
.pagi-title,
.breadcumb-title {
  word-break: break-word;
}

.blocks-gallery-caption,
.wp-block-embed figcaption,
.wp-block-image figcaption {
  color: var(--body-color);
}

.bypostauthor,
.gallery-caption {
  display: block;
}

.page-links,
.clearfix {
  clear: both;
}

.page--item {
  margin-bottom: 30px;
}
.page--item p {
  line-height: 1.8;
}

.content-none-search {
  margin-top: 30px;
}

.wp-block-button.aligncenter {
  text-align: center;
}

.alignleft {
  display: inline;
  float: left;
  margin-bottom: 10px;
  margin-right: 1.5em;
}

.alignright {
  display: inline;
  float: right;
  margin-bottom: 10px;
  margin-left: 1.5em;
  margin-right: 1em;
}

.aligncenter {
  clear: both;
  display: block;
  margin-left: auto;
  margin-right: auto;
  max-width: 100%;
}

.gallery {
  margin-bottom: 1.5em;
  width: 100%;
}

.gallery-item {
  display: inline-block;
  text-align: center;
  vertical-align: top;
  width: 100%;
  padding: 0 5px;
}

.wp-block-columns {
  margin-bottom: 1em;
}

figure.gallery-item {
  margin-bottom: 10px;
  display: inline-block;
}

figure.wp-block-gallery {
  margin-bottom: 14px;
}

.gallery-columns-2 .gallery-item {
  max-width: 50%;
}

.gallery-columns-3 .gallery-item {
  max-width: 33.33%;
}

.gallery-columns-4 .gallery-item {
  max-width: 25%;
}

.gallery-columns-5 .gallery-item {
  max-width: 20%;
}

.gallery-columns-6 .gallery-item {
  max-width: 16.66%;
}

.gallery-columns-7 .gallery-item {
  max-width: 14.28%;
}

.gallery-columns-8 .gallery-item {
  max-width: 12.5%;
}

.gallery-columns-9 .gallery-item {
  max-width: 11.11%;
}

.gallery-caption {
  display: block;
  font-size: 12px;
  color: var(--body-color);
  line-height: 1.5;
  padding: 0.5em 0;
}

.wp-block-cover p:not(.has-text-color),
.wp-block-cover-image-text,
.wp-block-cover-text {
  color: var(--white-color);
}

.wp-block-cover {
  margin-bottom: 15px;
}

.wp-caption-text {
  text-align: center;
}

.wp-caption {
  margin-bottom: 1.5em;
  max-width: 100%;
}
.wp-caption .wp-caption-text {
  margin: 0.5em 0;
  font-size: 14px;
}

.wp-block-media-text,
.wp-block-media-text.alignwide,
figure.wp-block-gallery {
  margin-bottom: 30px;
}

.wp-block-media-text.alignwide {
  background-color: var(--smoke-color);
}

.editor-styles-wrapper .has-large-font-size,
.has-large-font-size {
  line-height: 1.4;
}

.wp-block-latest-comments a {
  color: inherit;
}

.wp-block-button {
  margin-bottom: 10px;
}
.wp-block-button:last-child {
  margin-bottom: 0;
}
.wp-block-button .wp-block-button__link {
  color: #fff;
}
.wp-block-button .wp-block-button__link:hover {
  color: #fff;
  background-color: var(--theme-color);
}
.wp-block-button.is-style-outline .wp-block-button__link {
  background-color: transparent;
  border-color: var(--title-color);
  color: var(--title-color);
}
.wp-block-button.is-style-outline .wp-block-button__link:hover {
  color: #fff;
  background-color: var(--theme-color);
  border-color: var(--theme-color);
}
.wp-block-button.is-style-squared .wp-block-button__link {
  border-radius: 0;
}

ol.wp-block-latest-comments li {
  margin: 15px 0;
}

ul.wp-block-latest-posts {
  padding: 0;
  margin: 0;
  margin-bottom: 15px;
}
ul.wp-block-latest-posts a {
  color: inherit;
}
ul.wp-block-latest-posts a:hover {
  color: var(--theme-color);
}
ul.wp-block-latest-posts li {
  margin: 15px 0;
}

.wp-block-search__inside-wrapper {
  position: relative;
}

.wp-block-search {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 30px;
}
.wp-block-search:has(button) .wp-block-search__input {
  padding-right: 130px;
}
.wp-block-search .wp-block-search__input {
  width: 100%;
  max-width: 100%;
  border: 1px solid transparent;
  padding-left: 25px;
  padding-right: 40px;
  border: 1px solid transparent;
  box-shadow: 0px 13px 25px rgba(0, 0, 0, 0.04);
  border-radius: 50px;
}
.wp-block-search .wp-block-search__input:focus {
  border-color: var(--theme-color);
}
.wp-block-search .wp-block-search__button {
  margin: 0;
  min-width: 110px;
  height: 100%;
  border: none;
  color: #fff;
  background-color: var(--theme-color);
  border-radius: 0px;
  position: absolute;
  top: 0;
  right: 0;
  border-radius: 50px;
}
.wp-block-search .wp-block-search__button.has-icon {
  min-width: 56px;
}
.wp-block-search .wp-block-search__button:hover {
  background-color: var(--title-color);
}

.wp-block-search.wp-block-search__button-inside .wp-block-search__inside-wrapper {
  padding: 0;
  border: none;
}
.wp-block-search.wp-block-search__button-inside .wp-block-search__inside-wrapper:has(button) .wp-block-search__input {
  padding: 0 130px 0 25px;
}

ul.wp-block-rss a {
  color: inherit;
}

.wp-block-group.has-background {
  padding: 15px 15px 1px;
  margin-bottom: 30px;
}

.wp-block-table td,
.wp-block-table th {
  border-color: rgba(0, 0, 0, 0.1);
}

.wp-block-table.is-style-stripes {
  border: 1px solid rgba(0, 0, 0, 0.1);
  margin-bottom: 30px;
}

.wp-block-table.is-style-stripes {
  border: 0;
  margin-bottom: 30px;
  border-bottom: 0;
}
.wp-block-table.is-style-stripes th,
.wp-block-table.is-style-stripes td {
  border-color: var(--th-border-color);
}

.logged-in .sticky-wrapper.sticky,
.logged-in .preloader .th-btn {
  top: 32px;
}
@media (max-width: 782px) {
  .logged-in .sticky-wrapper.sticky,
  .logged-in .preloader .th-btn {
    top: 46px;
  }
}
@media (max-width: 600px) {
  .logged-in .sticky-wrapper.sticky,
  .logged-in .preloader .th-btn {
    top: 0;
  }
}

.post-password-form {
  margin-bottom: 30px;
  margin-top: 20px;
}
.post-password-form p {
  display: flex;
  position: relative;
  gap: 15px;
  /* Extra small devices */
}
@media (max-width: 575px) {
  .post-password-form p {
    flex-wrap: wrap;
  }
}
.post-password-form label {
  display: flex;
  align-items: center;
  flex: auto;
  margin-bottom: 0;
  line-height: 1;
  margin-top: 0;
  gap: 15px;
  /* Extra small devices */
}
@media (max-width: 575px) {
  .post-password-form label {
    flex-wrap: wrap;
  }
}
.post-password-form input {
  width: 100%;
  border: none;
  height: 55px;
  padding-left: 25px;
  color: var(--body-color);
  border: 1px solid var(--th-border-color);
}
.post-password-form input[type=submit] {
  padding-left: 0;
  padding-right: 0;
  margin: 0;
  width: 140px;
  border: none;
  color: #fff;
  background-color: var(--theme-color);
  text-align: center;
}
.post-password-form input[type=submit]:hover {
  background-color: var(--title-color);
}

.page-links {
  clear: both;
  margin: 0 0 1.5em;
  padding-top: 1em;
}
.page-links > .page-links-title {
  margin-right: 10px;
}
.page-links > span:not(.page-links-title):not(.screen-reader-text),
.page-links > a {
  display: inline-block;
  padding: 5px 13px;
  background-color: var(--white-color);
  color: var(--title-color);
  border: 1px solid rgba(0, 0, 0, 0.08);
  margin-right: 10px;
}
.page-links > span:not(.page-links-title):not(.screen-reader-text):hover,
.page-links > a:hover {
  opacity: 0.8;
  color: var(--white-color);
  background-color: var(--theme-color);
  border-color: transparent;
}
.page-links > span:not(.page-links-title):not(.screen-reader-text).current,
.page-links > a.current {
  background-color: var(--theme-color);
  color: var(--white-color);
  border-color: transparent;
}
.page-links span.screen-reader-text {
  display: none;
}

.blog-single .wp-block-archives-dropdown {
  margin-bottom: 30px;
}
.blog-single.format-quote, .blog-single.format-link, .blog-single.tag-sticky-2, .blog-single.sticky {
  border-color: transparent;
  position: relative;
}
.blog-single.format-quote .blog-content:before, .blog-single.format-link .blog-content:before, .blog-single.tag-sticky-2 .blog-content:before, .blog-single.sticky .blog-content:before {
  display: none;
}
.blog-single.format-quote:before, .blog-single.format-link:before, .blog-single.tag-sticky-2:before, .blog-single.sticky:before {
  content: "\f0c1";
  position: absolute;
  font-family: var(--icon-font);
  font-size: 16px;
  font-weight: 500;
  opacity: 1;
  right: 0;
  top: 0;
  color: var(--white-color);
  background-color: var(--theme-color);
  z-index: 1;
  height: 44px;
  width: 44px;
  line-height: 44px;
  text-align: center;
  border-radius: 0 20px 0 20px;
  /* Small devices */
}
@media (max-width: 767px) {
  .blog-single.format-quote:before, .blog-single.format-link:before, .blog-single.tag-sticky-2:before, .blog-single.sticky:before {
    border-radius: 0 10px 0 10px;
  }
}
.blog-single.tag-sticky-2::before, .blog-single.sticky::before {
  content: "\f08d";
}
.blog-single.format-quote:before {
  content: "\f10e";
}
.blog-single .blog-content .wp-block-categories-dropdown.wp-block-categories,
.blog-single .blog-content .wp-block-archives-dropdown {
  display: block;
  margin-bottom: 30px;
}

.blog-details .blog-single:before {
  display: none;
}
.blog-details .blog-single .blog-content {
  background-color: transparent;
  overflow: hidden;
}
.blog-details .blog-single.format-chat .blog-meta {
  margin-bottom: 20px;
}
.blog-details .blog-single.format-chat .blog-content > p:nth-child(2n) {
  background: var(--smoke-color);
  padding: 5px 20px;
}
.blog-details .blog-single.tag-sticky-2, .blog-details .blog-single.sticky, .blog-details .blog-single.format-quote, .blog-details .blog-single.format-link {
  background-color: transparent;
}
.blog-details .blog-single.tag-sticky-2:before, .blog-details .blog-single.sticky:before, .blog-details .blog-single.format-quote:before, .blog-details .blog-single.format-link:before {
  display: none;
}

.nof-title {
  margin-top: -0.24em;
}

.th-search {
  background-color: var(--smoke-color2);
  margin-bottom: 30px;
  border-radius: 15px;
  overflow: hidden;
}
.th-search .search-grid-content {
  padding: 30px;
  /* Small devices */
}
@media (max-width: 767px) {
  .th-search .search-grid-content {
    padding: 20px;
  }
}
.th-search .search-grid-title {
  font-size: 20px;
  margin-bottom: 5px;
  margin-top: 0;
}
.th-search .search-grid-title a {
  color: inherit;
}
.th-search .search-grid-title a:hover {
  color: var(--theme-color);
}
.th-search .search-grid-meta > * {
  display: inline-block;
  margin-right: 15px;
  font-size: 14px;
}
.th-search .search-grid-meta > *:last-child {
  margin-right: 0;
}
.th-search .search-grid-meta a,
.th-search .search-grid-meta span {
  color: var(--body-color);
}

/* Small devices */
@media (max-width: 767px) {
  .blog-single.format-quote:before, .blog-single.format-link:before, .blog-single.tag-sticky-2:before, .blog-single.sticky:before {
    font-size: 14px;
    width: 40px;
    height: 40px;
    line-height: 40px;
  }
}
@media (max-width: 768px) {
  .wp-block-latest-comments {
    padding-left: 10px;
  }
  .page--content.clearfix + .th-comment-form {
    margin-top: 24px;
  }
}
/*blog-navigation*****************/
.blog-navigation {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-top: 1px solid var(--th-border-color);
  padding-top: 60px;
  margin-top: 60px;
  gap: 20px;
  /* Large devices */
  /* Extra small devices */
}
.blog-navigation > div {
  min-width: 220px;
}
.blog-navigation .center-icon {
  font-size: 30px;
  color: var(--light-color);
}
.blog-navigation .nav-text {
  font-size: 18px;
  font-family: var(--body-font);
  color: var(--title-color);
  display: inline-block;
  font-weight: 600;
}
.blog-navigation .nav-text .text {
  font-size: 16px;
  font-weight: 500;
  color: var(--body-color);
  margin-top: -0.4em;
  margin-bottom: 4px;
}
.blog-navigation .nav-text .title {
  font-size: 18px;
  font-weight: 600;
  font-family: var(--title-font);
  margin-bottom: -0.3em;
  max-width: 218px;
  transition: 0.4s;
}
.blog-navigation .nav-btn {
  display: flex;
  gap: 16px;
}
.blog-navigation .nav-btn .thumb {
  position: relative;
  flex: none;
  border-radius: 10px;
  overflow: hidden;
  display: inline-block;
}
.blog-navigation .nav-btn .thumb:after {
  content: "";
  position: absolute;
  inset: 0;
  background: var(--title-color);
  opacity: 0;
  transition: 0.4s;
}
.blog-navigation .nav-btn .thumb i {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%) scale(0);
  z-index: 1;
  font-size: 20px;
  color: var(--white-color);
  transition: 0.4s;
}
.blog-navigation .nav-btn img {
  border-radius: 10px;
  height: 100%;
  object-fit: cover;
}
.blog-navigation .nav-btn.next {
  flex-direction: row-reverse;
  text-align: right;
}
.blog-navigation .nav-btn:hover .thumb:after {
  opacity: 0.5;
}
.blog-navigation .nav-btn:hover .thumb i {
  transform: translate(-50%, -50%) scale(1);
}
.blog-navigation .nav-btn:hover .nav-text {
  color: var(--theme-color);
}
.blog-navigation .nav-btn:hover .nav-text .title {
  color: var(--theme-color);
}
.blog-navigation .blog-btn {
  font-size: 38px;
  color: var(--light-color);
}
.blog-navigation .blog-btn:hover {
  color: var(--theme-color);
}
@media (max-width: 1199px) {
  .blog-navigation {
    flex-wrap: wrap;
  }
  .blog-navigation .nav-btn {
    display: block;
  }
  .blog-navigation .nav-btn .thumb {
    margin-bottom: 20px;
  }
}
@media (max-width: 575px) {
  .blog-navigation {
    justify-content: center;
    gap: 40px;
  }
  .blog-navigation .nav-btn {
    width: 100%;
    text-align: center;
    max-width: none;
  }
  .blog-navigation .nav-btn.next {
    text-align: center;
  }
  .blog-navigation .nav-btn .title {
    max-width: none;
  }
}

/* Extra small devices */
@media (max-width: 575px) {
  .blog-navigation > div {
    min-width: 150px;
  }
  .blog-navigation .nav-img {
    width: 50px;
  }
  .blog-navigation .nav-btn {
    gap: 8px;
  }
  .blog-navigation .nav-btn img {
    width: 50px !important;
    border-radius: 5px;
  }
  .blog-navigation .nav-text {
    font-size: 14px;
  }
  .blog-navigation .blog-btn {
    display: none;
  }
}
.wp-block-latest-comments__comment-excerpt p {
  margin-bottom: 0;
}

.wp-block-social-links {
  margin-bottom: 0;
}

.woosw-list table.woosw-items .woosw-item .woosw-item--name a:hover {
  color: var(--theme-color);
}

.woosw-list table.woosw-items tr td {
  padding: 20px;
}

.woosw-list table.woosw-items .woosw-item .woosw-item--name a {
  font-size: 20px;
  color: var(--title-color);
}

.woosw-list table.woosw-items tr .woosw-item--actions {
  width: 88px;
}

/*=================================
    02. Reset
==================================*/
/*------------------- 2.1. Container -------------------*/
/* Medium Large devices */
@media (max-width: 1399px) {
  :root {
    --main-container: 1250px;
  }
}
@media (min-width: 1560px) {
  .th-container2 {
    width: 100%;
    max-width: 1560px;
    margin-left: auto;
    margin-right: 0;
    padding-left: 0;
    padding-right: 0;
  }
  .th-container2 .container {
    --main-container: 1220px;
  }
  .th-container3 {
    width: 100%;
    max-width: 1560px;
    margin-left: 0;
    margin-right: auto;
    padding-left: 0;
    padding-right: 0;
  }
  .th-container3 .container {
    --main-container: 1220px;
  }
}
/* Hight Resoulation devices */
@media (min-width: 1922px) {
  .th-container3,
  .th-container2 {
    margin-right: auto;
  }
}
@media only screen and (min-width: 1300px) {
  .container-xxl,
  .container-xl,
  .container-lg,
  .container-md,
  .container-sm,
  .container {
    max-width: calc(var(--main-container) + var(--container-gutters));
    padding-left: calc(var(--container-gutters) / 2);
    padding-right: calc(var(--container-gutters) / 2);
  }
  .container-xxl.px-0,
  .container-xl.px-0,
  .container-lg.px-0,
  .container-md.px-0,
  .container-sm.px-0,
  .container.px-0 {
    max-width: var(--main-container);
  }
}
@media only screen and (min-width: 1300px) {
  .th-container {
    --main-container: 1396px;
  }
}
@media only screen and (max-width: 1600px) {
  .container-fluid.px-0 {
    padding-left: 15px !important;
    padding-right: 15px !important;
  }
  .container-fluid.px-0 .row {
    margin-left: 0 !important;
    margin-right: 0 !important;
  }
}
.container-gallery {
  max-width: 1840px;
}

/*------------------- 2.2. Grid -------------------*/
.slick-track > [class*=col] {
  flex-shrink: 0;
  width: 100%;
  max-width: 100%;
  padding-right: calc(var(--bs-gutter-x) / 2);
  padding-left: calc(var(--bs-gutter-x) / 2);
  margin-top: var(--bs-gutter-y);
}

@media (min-width: 1300px) {
  .row {
    --bs-gutter-x: 24px;
  }
}
.gy-30 {
  --bs-gutter-y: 30px;
}

.g-0 {
  --bs-gutter-y: 0;
  --bs-gutter-x: 0;
}

.gx-0 {
  --bs-gutter-x: 0;
}

.gy-40 {
  --bs-gutter-y: 40px;
}

.gy-50 {
  --bs-gutter-y: 50px;
}

.gy-60 {
  --bs-gutter-y: 60px;
}

.gy-80 {
  --bs-gutter-y: 80px;
}

.gx-10 {
  --bs-gutter-x: 10px;
}

.gy-10 {
  --bs-gutter-y: 10px;
}

@media (min-width: 1500px) {
  .gx-100 {
    --bs-gutter-x: 100px;
  }
}
@media (min-width: 1299px) {
  .gx-60 {
    --bs-gutter-x: 60px;
  }
  .gx-70 {
    --bs-gutter-x: 70px;
  }
  .gx-80 {
    --bs-gutter-x: 80px;
  }
}
@media (min-width: 1399px) {
  .gx-30 {
    --bs-gutter-x: 30px;
  }
  .gx-25 {
    --bs-gutter-x: 25px;
  }
  .gx-40 {
    --bs-gutter-x: 40px;
  }
}
/* Medium devices */
@media (max-width: 991px) {
  .gy-50 {
    --bs-gutter-y: 40px;
  }
  .gy-80 {
    --bs-gutter-y: 60px;
  }
}
/*------------------- 2.3. Input -------------------*/
select,
.form-control,
.form-select,
textarea,
input {
  height: 48px;
  padding: 0 25px 0 25px;
  padding-right: 45px;
  border: 1px solid transparent;
  color: var(--body-color);
  background-color: var(--smoke-color2);
  border-radius: 5px;
  font-size: 16px;
  width: 100%;
  font-family: var(--body-font);
  transition: 0.4s ease-in-out;
}
select:focus,
.form-control:focus,
.form-select:focus,
textarea:focus,
input:focus {
  outline: 0;
  box-shadow: none;
  border-color: var(--th-border-color);
  background-color: var(--smoke-color2);
}
select::-moz-placeholder,
.form-control::-moz-placeholder,
.form-select::-moz-placeholder,
textarea::-moz-placeholder,
input::-moz-placeholder {
  color: var(--body-color);
}
select::-webkit-input-placeholder,
.form-control::-webkit-input-placeholder,
.form-select::-webkit-input-placeholder,
textarea::-webkit-input-placeholder,
input::-webkit-input-placeholder {
  color: var(--body-color);
}
select:-ms-input-placeholder,
.form-control:-ms-input-placeholder,
.form-select:-ms-input-placeholder,
textarea:-ms-input-placeholder,
input:-ms-input-placeholder {
  color: var(--body-color);
}
select::placeholder,
.form-control::placeholder,
.form-select::placeholder,
textarea::placeholder,
input::placeholder {
  color: var(--body-color);
}

input[type=date] {
  padding: 0 25px 0 25px;
  position: relative;
}

input[type=date]::-webkit-calendar-picker-indicator {
  background: transparent;
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  z-index: 2;
  cursor: pointer;
}

input[type=time] {
  padding: 0 30px;
  position: relative;
}

input[type=time]::-webkit-calendar-picker-indicator {
  background: transparent;
  position: absolute;
  left: 0px;
  height: 100%;
  width: 100%;
  z-index: 1;
  cursor: pointer;
}

.form-select,
select {
  display: block;
  width: 100%;
  line-height: 1.5;
  vertical-align: middle;
  background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 16 16'%3E%3Cpath fill='none' stroke='%23343a40' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M2 5l6 6 6-6'/%3E%3C/svg%3E");
  background-position: right 26px center;
  background-repeat: no-repeat;
  background-size: 16px 12px;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  cursor: pointer;
}
.form-select.style2,
select.style2 {
  background-image: url("data:image/svg+xml,%3Csvg width='11' height='6' viewBox='0 0 11 6' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M9.87109 1.71094L5.71484 5.62109C5.56901 5.7487 5.41406 5.8125 5.25 5.8125C5.08594 5.8125 4.9401 5.7487 4.8125 5.62109L0.65625 1.71094C0.382812 1.40104 0.373698 1.09115 0.628906 0.78125C0.920573 0.507812 1.23047 0.498698 1.55859 0.753906L5.25 4.25391L8.96875 0.753906C9.27865 0.498698 9.57943 0.498698 9.87109 0.753906C10.1263 1.08203 10.1263 1.40104 9.87109 1.71094Z' fill='%238B929C'/%3E%3C/svg%3E");
}

textarea.form-control,
textarea {
  min-height: 178px;
  padding-top: 16px;
  padding-bottom: 17px;
  border-radius: 5px;
}
textarea.form-control.style2,
textarea.style2 {
  min-height: 100px;
}

.form-group {
  margin-bottom: var(--bs-gutter-x);
  position: relative;
}
.form-group > i {
  display: inline-block;
  position: absolute;
  right: 25px;
  top: 16px;
  font-size: 16px;
  color: var(--body-color);
}
.form-group > i.fa-chevron-down {
  width: 17px;
  background-color: var(--smoke-color2);
}
.form-group.has-label > i {
  top: 50px;
}
.form-group.style2 textarea,
.form-group.style2 input {
  background: var(--black-color2);
  color: var(--white-color);
  height: 60px;
  border-radius: 8px;
}
.form-group.style2 textarea:active, .form-group.style2 textarea:focus,
.form-group.style2 input:active,
.form-group.style2 input:focus {
  border-color: var(--theme-color);
}
.form-group.style2 textarea::placeholder,
.form-group.style2 input::placeholder {
  color: var(--white-color);
}
.form-group.style2 select {
  background: var(--black-color2);
  color: var(--white-color);
  height: 60px;
  border-radius: 8px;
}
.form-group.style2 > i {
  top: 24px;
  color: var(--white-color);
}
.form-group.style-border textarea,
.form-group.style-border input {
  background: transparent;
  border: 1px solid var(--light-color);
}
.form-group.style-border textarea:active, .form-group.style-border textarea:focus,
.form-group.style-border input:active,
.form-group.style-border input:focus {
  border-color: var(--theme-color);
}
.form-group.style-border select {
  background-color: transparent;
  border: 1px solid var(--light-color);
}
.form-group.style-border2 textarea,
.form-group.style-border2 input {
  background: transparent;
  border: 1px solid var(--th-border-color2);
  color: var(--body-color);
}
.form-group.style-border2 textarea::placeholder,
.form-group.style-border2 input::placeholder {
  color: var(--body-color);
}
.form-group.style-border2 select {
  background: transparent;
  border: 1px solid var(--th-border-color2);
}
.form-group.style-border2 i {
  color: var(--theme-color);
  top: 22px;
}
.form-group.style-border3 textarea,
.form-group.style-border3 input {
  background: var(--white-color);
  border: 1px solid var(--th-border-color);
  color: var(--body-color);
}
.form-group.style-border3 textarea::placeholder,
.form-group.style-border3 input::placeholder {
  color: var(--body-color);
}
.form-group.style-border3 select {
  background: var(--white-color);
  border: 1px solid var(--th-border-color);
}
.form-group.style-border3 i {
  color: var(--body-color);
  top: 20px;
  background: transparent;
}
.form-group.style-white .form-select,
.form-group.style-white .form-control {
  background: var(--white-color);
}
.form-group.style-white > i {
  top: 20px;
  background: transparent;
  color: var(--theme-color);
}
.form-group.style-dark {
  position: relative;
  z-index: 1;
  align-self: self-start;
}
.form-group.style-dark:after {
  content: "";
  position: absolute;
  top: 0;
  left: var(--bs-gutter-x)/2;
  width: calc(100% - var(--bs-gutter-x) / 1);
  height: 100%;
  background: var(--theme-color);
  opacity: 0.4;
  border-radius: 50px;
  z-index: -1;
}
.form-group.style-dark .form-select,
.form-group.style-dark .form-control {
  background: transparent;
  border: 1px solid rgba(255, 255, 255, 0.2);
  backdrop-filter: blur(30px);
  color: var(--white-color);
}
.form-group.style-dark .form-select::placeholder,
.form-group.style-dark .form-control::placeholder {
  color: var(--th-border-color);
}
.form-group.style-dark > i {
  top: 20px;
  background: transparent;
  color: var(--th-border-color);
}
.form-group.style-dark2 .form-select,
.form-group.style-dark2 .form-control {
  background: var(--black-color3);
  border: 1px solid var(--th-border-color2);
  color: #6B7586;
  border-radius: 0;
}
.form-group.style-dark2 .form-select::placeholder,
.form-group.style-dark2 .form-control::placeholder {
  color: #6B7586;
}
.form-group.style-dark2 > i {
  top: 20px;
  background: transparent;
  color: #6B7586;
}
.form-group.style-dark3 .form-select,
.form-group.style-dark3 .form-control {
  background: var(--title-color);
  border: 1px solid var(--black-color4);
  color: var(--white-color);
  border-radius: 0;
}
.form-group.style-dark3 > i {
  top: 20px;
  background: transparent;
  color: var(--theme-color);
}
.form-group.style-radius-0 .form-select,
.form-group.style-radius-0 .form-control {
  border-radius: 0px;
}
.form-group.style-shadow .form-select,
.form-group.style-shadow .form-control {
  box-shadow: 0px 2px 4px rgba(2, 29, 53, 0.1);
}

[class*=col-].form-group > i {
  right: calc(var(--bs-gutter-x) / 2 + 25px);
}

.form-rounded-10 .form-control,
.form-rounded-10 .form-select {
  border-radius: 10px !important;
}
.form-rounded-10 .th-btn {
  border-radius: 10px;
}

option:checked, option:focus, option:hover {
  background-color: var(--theme-color);
  color: var(--white-color);
}

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

/* Firefox */
input[type=number] {
  -moz-appearance: textfield;
}

input[type=checkbox] {
  visibility: hidden;
  opacity: 0;
  display: inline-block;
  vertical-align: middle;
  width: 0;
  height: 0;
  display: none;
}
input[type=checkbox]:checked ~ label:before {
  content: "\f00c";
  color: var(--white-color);
  background-color: var(--theme-color);
  border-color: var(--theme-color);
}
input[type=checkbox] ~ label {
  position: relative;
  padding-left: 30px;
  cursor: pointer;
  display: block;
}
input[type=checkbox] ~ label:before {
  content: "";
  font-family: var(--icon-font);
  font-weight: 700;
  position: absolute;
  left: 0px;
  top: 3.5px;
  background-color: var(--white-color);
  border: 1px solid var(--th-border-color);
  height: 18px;
  width: 18px;
  line-height: 18px;
  text-align: center;
  font-size: 12px;
}
input[type=checkbox].style2 ~ label {
  color: #8B929C;
  padding-left: 23px;
  margin-bottom: -0.5em;
}
input[type=checkbox].style2 ~ label:before {
  background-color: rgba(255, 255, 255, 0.1);
  border: 1px solid #8B929C;
  height: 14px;
  width: 14px;
  line-height: 14px;
  border-radius: 3px;
  top: 6px;
}
input[type=checkbox].style2:checked ~ label:before {
  color: var(--theme-color);
}

input[type=radio] {
  visibility: hidden;
  opacity: 0;
  display: inline-block;
  vertical-align: middle;
  width: 0;
  height: 0;
  display: none;
}
input[type=radio] ~ label {
  position: relative;
  padding-left: 30px;
  cursor: pointer;
  line-height: 1;
  display: inline-block;
  font-weight: 600;
  margin-bottom: 0;
}
input[type=radio] ~ label::before {
  content: "\f111";
  position: absolute;
  font-family: var(--icon-font);
  left: 0;
  top: -2px;
  width: 20px;
  height: 20px;
  padding-left: 0;
  font-size: 0.6em;
  line-height: 19px;
  text-align: center;
  border: 1px solid var(--theme-color);
  border-radius: 100%;
  font-weight: 700;
  background: var(--white-color);
  color: transparent;
  transition: all 0.2s ease;
}
input[type=radio]:checked ~ label::before {
  border-color: var(--theme-color);
  background-color: var(--theme-color);
  color: var(--white-color);
}

label {
  margin-bottom: 0.5em;
  margin-top: -0.3em;
  display: block;
  color: var(--title-color);
  font-family: var(--body-font);
  font-size: 16px;
}

textarea.is-invalid,
select.is-invalid,
input.is-invalid,
.was-validated input:invalid {
  border: 1px solid var(--error-color) !important;
  background-position: right calc(0.375em + 0.8875rem) center;
  background-image: none;
}
textarea.is-invalid:focus,
select.is-invalid:focus,
input.is-invalid:focus,
.was-validated input:invalid:focus {
  outline: 0;
  box-shadow: none;
}

textarea.is-invalid {
  background-position: top calc(0.375em + 0.5875rem) right calc(0.375em + 0.8875rem);
}

.row.no-gutters > .form-group {
  margin-bottom: 0;
}

.form-messages {
  display: none;
}
.form-messages.mb-0 * {
  margin-bottom: 0;
}
.form-messages.success {
  color: var(--success-color);
  display: block;
}
.form-messages.error {
  color: var(--error-color);
  display: block;
}
.form-messages pre {
  padding: 0;
  background-color: transparent;
  color: inherit;
}

/*------------------- 2.4. Slick Slider -------------------*/
.swiper-wrapper.row {
  flex-wrap: nowrap;
}

.th-slider.has-shadow {
  padding-left: 12px;
  padding-right: 12px;
  margin: -25px -12px;
}
.th-slider.has-shadow .swiper-wrapper {
  padding: 25px 0;
}

.swiper-fade .swiper-slide {
  transition: 0.6s ease-in-out;
}
.swiper-fade .swiper-slide-prev {
  opacity: 0;
}

.swiper-pagination-bullets {
  position: relative;
  z-index: 3;
  text-align: center;
  margin: 50px 0 0px 0;
  line-height: 30px;
  height: 30px;
}
.swiper-pagination-bullets .swiper-pagination-bullet {
  display: inline-block;
  --swiper-pagination-bullet-size: 14px;
  --swiper-pagination-bullet-horizontal-gap: 13px;
  margin: 5px 7px;
  opacity: 1;
  background-color: var(--th-border-color6);
  position: relative;
  transition: 0.4s;
  cursor: pointer;
}
.swiper-pagination-bullets .swiper-pagination-bullet:before {
  content: "";
  position: absolute;
  inset: -8px;
  border: 1px solid var(--th-border-color6);
  transform: scale(0.2);
  border-radius: inherit;
  transition: 0.4s ease-in-out;
}
.swiper-pagination-bullets .swiper-pagination-bullet.swiper-pagination-bullet-active {
  background-color: var(--theme-color);
}
.swiper-pagination-bullets .swiper-pagination-bullet.swiper-pagination-bullet-active:before {
  transform: scale(1);
  border-color: var(--theme-color);
}

.slider-pagination.swiper-pagination-progressbar {
  width: -webkit-fill-available;
  position: relative;
  height: 1px;
  background: #D9D9D9;
  top: auto;
  bottom: 0px;
  margin: 70px 0px 0;
}
.slider-pagination.swiper-pagination-progressbar .swiper-pagination-progressbar-fill {
  height: 1px;
  top: auto;
  bottom: 0;
  background: var(--theme-color);
}

.slider-pagination {
  --swiper-pagination-bottom: 0;
  bottom: var(--swiper-pagination-bottom, 0px);
}
.slider-pagination.style2 .swiper-pagination-bullet {
  background: transparent;
  border: 1px solid var(--white-color);
  transition: 0.4s;
}
.slider-pagination.style2 .swiper-pagination-bullet.swiper-pagination-bullet-active {
  background: var(--theme-color);
  border-color: var(--theme-color);
}
.slider-pagination.style3 .swiper-pagination-bullet {
  background: var(--light-color);
  transition: 0.4s;
  --swiper-pagination-bullet-size: 8px;
}
.slider-pagination.style3 .swiper-pagination-bullet:before {
  border: 0;
}
.slider-pagination.style3 .swiper-pagination-bullet.swiper-pagination-bullet-active {
  background: transparent;
}
.slider-pagination.style3 .swiper-pagination-bullet.swiper-pagination-bullet-active:before {
  border: 1px solid var(--title-color);
}
.slider-pagination.style4 .swiper-pagination-bullet {
  background: transparent;
  border: 1px solid var(--title-color);
  transition: 0.4s;
}
.slider-pagination.style4 .swiper-pagination-bullet:before {
  opacity: 0;
  inset: -14px;
}
.slider-pagination.style4 .swiper-pagination-bullet.swiper-pagination-bullet-active {
  background: var(--theme-color);
  border-color: var(--theme-color);
}
.slider-pagination.style4 .swiper-pagination-bullet.swiper-pagination-bullet-active:before {
  opacity: 1;
}

.slider-area {
  position: relative;
}

.slider-arrow {
  display: inline-block;
  padding: 0;
  background-color: var(--theme-color);
  color: var(--white-color);
  position: absolute;
  top: 50%;
  border: none;
  left: var(--pos-x, -90px);
  width: var(--icon-size, 56px);
  height: var(--icon-size, 56px);
  line-height: var(--icon-size, 56px);
  font-size: var(--icon-font-size, 18px);
  margin-top: calc(var(--icon-size, 56px) / -2);
  z-index: 3;
  border-radius: 99px;
}
.slider-arrow.default {
  position: relative;
  --pos-x: 0;
  margin-top: 0;
}
.slider-arrow.slider-next {
  right: var(--pos-x, -90px);
  left: auto;
}
.slider-arrow:hover {
  background-color: var(--title-color);
  color: var(--white-color);
}
.slider-arrow.style2 {
  background: var(--white-color);
  color: var(--title-color);
  line-height: 58px;
  border-radius: 0;
}
.slider-arrow.style2:hover {
  background: var(--theme-color);
}
.slider-arrow.style3 {
  background: var(--black-color2);
}
.slider-arrow.style3:hover {
  background: var(--theme-color);
  color: var(--white-color);
}
.slider-arrow.style-border {
  background: transparent;
  border: 1px solid var(--title-color);
  color: var(--title-color);
}
.slider-arrow.style-border:hover {
  background: var(--theme-color);
  border-color: var(--theme-color);
  color: var(--white-color);
}
.slider-arrow.style-border2 {
  background: transparent;
  border: 1px solid var(--light-color);
  color: var(--title-color);
}
.slider-arrow.style-border2:hover {
  background: var(--theme-color);
  border-color: var(--theme-color);
  color: var(--white-color);
}

.arrow-margin .slider-arrow {
  top: calc(50% - 30px);
}

.arrow-wrap {
  position: relative;
}
.arrow-wrap .slider-arrow {
  opacity: 0;
  visibility: hidden;
  transform: scale(0.4);
}
.arrow-wrap:hover .slider-arrow {
  opacity: 1;
  visibility: visible;
  transform: scale(1);
}

/* Extra large devices */
@media (max-width: 1500px) {
  .slider-arrow {
    --arrow-horizontal: -20px;
    --pos-x: -30px;
  }
}
/* Medium Large devices */
@media (max-width: 1399px) {
  .slider-arrow {
    --arrow-horizontal: 40px;
    --pos-x: -30px;
  }
}
/* Medium devices */
@media (max-width: 991px) {
  .slider-arrow {
    display: none;
  }
}
.icon-box {
  /* Medium devices */
}
.icon-box .slider-arrow {
  opacity: 1;
  visibility: visible;
  transform: none;
}
.icon-box .slider-arrow:not(:last-child) {
  margin-right: 8px;
}
@media (max-width: 991px) {
  .icon-box .slider-arrow {
    display: inline-block !important;
  }
}

/* Medium devices */
@media (max-width: 991px) {
  .slider-arrow {
    --icon-size: 40px;
    line-height: 38px;
    margin-right: 40px;
    font-size: 14px;
  }
  .slider-arrow.slider-next {
    margin-right: 0;
  }
  .slick-dots {
    margin: 40px 0 0 0;
  }
  .icon-box .slider-arrow {
    margin-right: 0;
  }
}
.slider-scrollbar {
  border-radius: 0;
  background: rgba(215, 215, 215, 0.2);
  height: 3px;
  width: 100%;
  bottom: 0;
  cursor: grab;
  z-index: 1;
  margin-top: 32px;
}
.slider-scrollbar .swiper-scrollbar-drag {
  border-radius: 0;
  height: 3px;
  top: 0;
  left: 0;
  background: var(--theme-color);
}

/*slider-drag-cursor**************/
.slider-drag-cursor {
  pointer-events: none;
  z-index: 99999;
  position: fixed;
  top: 0;
  left: -2px;
  transition: opacity 0.4s ease-in-out;
  background: var(--white-color);
  border: 5px solid rgba(19, 24, 43, 0.3);
  width: 120px;
  height: 120px;
  line-height: 110px;
  text-align: center;
  border-radius: 100%;
  color: var(--theme-color);
  opacity: 0;
  cursor: none;
}
.slider-drag-cursor.active {
  opacity: 1;
}

/*------------------- 2.5. Mobile Menu -------------------*/
.th-menu-wrapper {
  position: fixed;
  top: 0;
  left: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 999999;
  width: 0;
  width: 100%;
  height: 100%;
  transition: all ease 0.8s;
  opacity: 0;
  visibility: hidden;
}
.th-menu-wrapper .mobile-logo {
  padding-bottom: 30px;
  padding-top: 40px;
  display: block;
  text-align: center;
  background-color: var(--smoke-color2);
}
.th-menu-wrapper .mobile-logo svg {
  max-width: 185px;
}
.th-menu-wrapper .th-menu-toggle {
  border: none;
  font-size: 22px;
  position: absolute;
  right: -16.5px;
  top: 25px;
  padding: 0;
  line-height: 1;
  width: 33px;
  height: 33px;
  line-height: 35px;
  font-size: 18px;
  z-index: 1;
  color: var(--white-color);
  background-color: var(--theme-color);
  border-radius: 50%;
}
.th-menu-wrapper .th-menu-toggle:hover {
  background-color: var(--title-color);
  color: var(--white-color);
}
.th-menu-wrapper .th-menu-area {
  width: 100%;
  max-width: 310px;
  background-color: #fff;
  border-right: 3px solid var(--theme-color);
  height: 100%;
  position: relative;
  left: -110%;
  opacity: 0;
  visibility: hidden;
  transition: all ease 1s;
  z-index: 1;
}
.th-menu-wrapper.th-body-visible {
  opacity: 1;
  visibility: visible;
}
.th-menu-wrapper.th-body-visible .th-menu-area {
  left: 0;
  opacity: 1;
  visibility: visible;
}
.th-menu-wrapper.style2 .mobile-logo {
  background: var(--title-color);
}
.th-menu-wrapper.style2 .th-menu-toggle {
  color: var(--title-color);
}
.th-menu-wrapper.style2 .th-menu-toggle:hover {
  color: var(--white-color);
  background: var(--black-color2);
}
.th-menu-wrapper.style2 .th-mobile-menu ul li.th-active > a {
  color: var(--title-color);
}

.th-mobile-menu {
  overflow-y: scroll;
  max-height: calc(100vh - 200px);
  padding-bottom: 40px;
  margin-top: 33px;
  text-align: left;
}
.th-mobile-menu ul {
  margin: 0;
  padding: 0 0;
}
.th-mobile-menu ul li {
  border-bottom: 1px solid #fdedf1;
  list-style-type: none;
}
.th-mobile-menu ul li li:first-child {
  border-top: 1px solid #fdedf1;
}
.th-mobile-menu ul li a {
  display: block;
  position: relative;
  padding: 12px 0;
  line-height: 1.4;
  font-size: 16px;
  text-transform: capitalize;
  color: var(--title-color);
  padding-left: 18px;
}
.th-mobile-menu ul li a:before {
  content: "\f105";
  font-family: var(--icon-font);
  position: absolute;
  left: 0;
  top: 12px;
  margin-right: 10px;
  display: inline-block;
}
.th-mobile-menu ul li.th-active > a {
  color: var(--theme-color);
}
.th-mobile-menu ul li.th-active > a:before {
  transform: rotate(90deg);
}
.th-mobile-menu ul li ul li {
  padding-left: 20px;
}
.th-mobile-menu ul li ul li:last-child {
  border-bottom: none;
}
.th-mobile-menu ul .menu-item-has-children > a .th-mean-expand {
  position: absolute;
  right: 0;
  top: 50%;
  font-weight: 400;
  font-size: 12px;
  width: 25px;
  height: 25px;
  line-height: 25px;
  margin-top: -12.5px;
  display: inline-block;
  text-align: center;
  background-color: var(--smoke-color);
  color: var(--title-color);
  box-shadow: 0 0 20px -8px rgba(173, 136, 88, 0.5);
  border-radius: 50%;
}
.th-mobile-menu ul .menu-item-has-children > a .th-mean-expand:before {
  content: "\f067";
  font-family: var(--icon-font);
}
.th-mobile-menu ul .menu-item-has-children > a:after {
  content: "\f067";
  font-family: var(--icon-font);
  width: 22px;
  height: 22px;
  line-height: 22px;
  display: inline-block;
  text-align: center;
  font-size: 12px;
  border-radius: 50px;
  background-color: var(--smoke-color);
  float: right;
  margin-top: 1px;
}
.th-mobile-menu ul .menu-item-has-children.th-active > a .th-mean-expand:before {
  content: "\f068";
}
.th-mobile-menu ul .menu-item-has-children.th-active > a:after {
  content: "\f068";
}
.th-mobile-menu > ul {
  padding: 0 40px;
}
.th-mobile-menu > ul > li:last-child {
  border-bottom: none;
}

.th-menu-toggle {
  width: auto;
  height: auto;
  padding: 0;
  font-size: 20px;
  border: none;
  background-color: var(--theme-color);
  color: var(--white-color);
  display: inline-block;
  border-radius: 5px;
}
.th-menu-toggle.style-text, .th-menu-toggle.style-text-white {
  width: auto;
  height: auto;
  background-color: transparent;
  color: var(--title-color);
  font-size: 20px;
}
.th-menu-toggle.style-text i, .th-menu-toggle.style-text-white i {
  margin-right: 10px;
}
.th-menu-toggle.style-text-white {
  color: var(--white-color);
}

@media (max-width: 400px) {
  .th-menu-wrapper .th-menu-area {
    width: 100%;
    max-width: 270px;
  }
  .th-mobile-menu > ul {
    padding: 0 20px;
  }
}
/*=================================
    03. Utilities
==================================*/
/*------------------- 3.1. Preloader -------------------*/
.preloader {
  position: fixed;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
  z-index: 999;
  background-color: var(--white-color);
}
.preloader .th-btn {
  padding: 15px 20px;
  border-radius: 0;
  font-size: 14px;
}

.preloader-inner {
  text-align: center;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
  line-height: 1;
}
.preloader-inner img {
  display: block;
  margin: 0 auto 0 auto;
}

.loader {
  font-size: 80px;
  display: inline-block;
  font-family: var(--title-font);
  font-weight: 700;
  color: var(--title-color);
  box-sizing: border-box;
  text-shadow: 0 0 2px var(--theme-color), 0 0 1px var(--theme-color), 0 0 1px var(--theme-color);
  position: relative;
  line-height: normal;
}

.loading-text {
  position: absolute;
  left: 0;
  top: 0;
  color: var(--theme-color);
  width: 100%;
  height: 100%;
  overflow: hidden;
  box-sizing: border-box;
  animation: animloader 6s linear infinite;
}

@keyframes animloader {
  0% {
    width: 0%;
  }
  100% {
    width: 100%;
  }
}
/*------------------- 3.2. Buttons -------------------*/
.th-btn {
  position: relative;
  z-index: 2;
  vertical-align: middle;
  display: inline-flex;
  border: none;
  text-align: center;
  background-color: var(--theme-color);
  color: var(--white-color);
  font-family: var(--body-font);
  font-size: 16px;
  font-weight: 500;
  line-height: 1;
  padding: 24px 48px;
  min-width: 170px;
  border-radius: 50px;
  justify-content: center;
  overflow: hidden;
  transition: 0.5s;
}
.th-btn i {
  transition: 0.1s;
}
.th-btn:after {
  content: "";
  position: absolute;
  width: 0;
  height: 100%;
  background: var(--title-color);
  top: 0;
  left: 0;
  z-index: -1;
  transition: 0.7s;
}
.th-btn:hover, .th-btn:active {
  background: var(--theme-color);
  color: var(--white-color);
}
.th-btn:hover i.fa-arrow-up-right, .th-btn:active i.fa-arrow-up-right {
  transform: rotate(45deg);
}
.th-btn:hover:after, .th-btn:active:after {
  width: 100%;
}
.th-btn.style2 {
  background: var(--smoke-color2);
  color: var(--title-color);
}
.th-btn.style2:after {
  background: var(--theme-color);
}
.th-btn.style2:hover {
  background: var(--smoke-color2);
  color: var(--white-color);
}
.th-btn.style3:after {
  background: var(--white-color);
}
.th-btn.style3:hover {
  color: var(--title-color);
}
.th-btn.style4 {
  background: var(--title-color);
}
.th-btn.style4:after {
  background: var(--theme-color);
}
.th-btn.style5 {
  background: var(--smoke-color2);
  color: var(--theme-color);
}
.th-btn.style5:after {
  background: var(--theme-color);
}
.th-btn.style5:hover {
  background: var(--smoke-color2);
  color: var(--white-color);
}
.th-btn.style6 {
  background: var(--theme-color);
  color: var(--title-color);
}
.th-btn.style6:after {
  background: var(--white-color);
}
.th-btn.style6:hover {
  background: var(--theme-color);
}
.th-btn.style7 {
  background: transparent;
  color: var(--white-color);
}
.th-btn.style7:after {
  background: var(--theme-color);
  width: 3px;
}
.th-btn.style7:hover {
  background: transparent;
  color: var(--title-color);
}
.th-btn.style7:hover:after {
  width: 100%;
}
.th-btn.style8 {
  background: transparent;
  color: var(--title-color);
}
.th-btn.style8:after {
  background: var(--theme-color);
  width: 3px;
}
.th-btn.style8:hover {
  color: var(--white-color);
}
.th-btn.style8:hover:after {
  width: 100%;
}
.th-btn.style9 {
  background: transparent;
  color: var(--white-color);
}
.th-btn.style9:after {
  background: var(--theme-color);
  width: 3px;
}
.th-btn.style9:hover {
  background: transparent;
}
.th-btn.style9:hover:after {
  width: 100%;
}
.th-btn.style10 {
  background: var(--title-color);
}
.th-btn.style10:after {
  background: var(--theme-color2);
}
.th-btn.style11 {
  background: var(--theme-color2);
}
.th-btn.style11:after {
  background: var(--title-color);
}
.th-btn.style12 {
  background: var(--theme-color2);
}
.th-btn.style12:after {
  background: var(--white-color);
}
.th-btn.style12:hover {
  color: var(--title-color);
}
.th-btn.style-border {
  background: transparent;
  border: 1px solid var(--light-color);
  color: var(--title-color);
  padding: 23px 48px;
}
.th-btn.style-border:after {
  background: var(--theme-color);
}
.th-btn.style-border:hover {
  border: 1px solid var(--theme-color);
  color: var(--white-color);
}
.th-btn.style-border2 {
  background: transparent;
  border: 1px solid var(--white-color);
  color: var(--white-color);
}
.th-btn.style-border2:after {
  background: var(--theme-color);
}
.th-btn.style-border2:hover {
  border-color: var(--theme-color);
  color: var(--title-color);
}
.th-btn.style-border3 {
  background: transparent;
  border: 1px solid var(--white-color);
  color: var(--white-color);
}
.th-btn.style-border3:after {
  background: var(--white-color);
}
.th-btn.style-border3:hover {
  color: var(--theme-color);
}
.th-btn.style-border4 {
  background: transparent;
  border: 1px solid var(--title-color);
  color: var(--title-color);
  padding-top: 23px;
  padding-bottom: 23px;
}
.th-btn.style-border4:after {
  background: var(--title-color);
}
.th-btn.style-border4:hover {
  color: var(--white-color);
}
.th-btn.style-border5 {
  background: transparent;
  border: 1px solid var(--light-color);
  color: var(--title-color);
  padding: 23px 48px;
}
.th-btn.style-border5:after {
  background: var(--theme-color);
}
.th-btn.style-border5:hover {
  border: 1px solid var(--theme-color);
  color: var(--title-color);
}
.th-btn.btn-fw {
  width: 100%;
}
.th-btn.btn-fw:before, .th-btn.btn-fw:after {
  display: none;
}
.th-btn.btn-fw:hover {
  background: var(--theme-color);
  color: var(--white-color);
}
.th-btn.btn-md {
  padding: 20px 48px;
}
.th-btn.btn-sm {
  padding: 16px 25px;
  min-width: 140px;
}
.th-btn.btn-radius-0 {
  border-radius: 0;
}
.th-btn.btn-radius-8 {
  border-radius: 8px;
}
.th-btn.circle-btn {
  height: 132px;
  width: 132px;
  min-width: auto;
  border-radius: 50%;
  align-items: center;
  font-weight: 500;
  font-size: 16px;
  padding: 25px;
}

@keyframes greentopBubbles {
  0% {
    background-position: 5% 90%, 10% 90%, 10% 90%, 15% 90%, 25% 90%, 25% 90%, 40% 90%, 55% 90%, 70% 90%;
  }
  50% {
    background-position: 0% 80%, 0% 20%, 10% 40%, 20% 0%, 30% 30%, 22% 50%, 50% 50%, 65% 20%, 90% 30%;
  }
  100% {
    background-position: 0% 70%, 0% 10%, 10% 30%, 20% -10%, 30% 20%, 22% 40%, 50% 40%, 65% 10%, 90% 20%;
    background-size: 0% 0%, 0% 0%, 0% 0%, 0% 0%, 0% 0%, 0% 0%;
  }
}
@keyframes greenbottomBubbles {
  0% {
    background-position: 10% -10%, 30% 10%, 55% -10%, 70% -10%, 85% -10%, 70% -10%, 70% 0%;
  }
  50% {
    background-position: 0% 80%, 20% 80%, 45% 60%, 60% 100%, 75% 70%, 95% 60%, 105% 0%;
  }
  100% {
    background-position: 0% 90%, 20% 90%, 45% 70%, 60% 110%, 75% 80%, 95% 70%, 110% 10%;
    background-size: 0% 0%, 0% 0%, 0% 0%, 0% 0%, 0% 0%, 0% 0%;
  }
}
@keyframes btn-icon-anim {
  0% {
    top: 0;
    right: 2px;
  }
  25% {
    top: -10px;
    right: -10px;
  }
  50% {
    top: 10px;
    opacity: 0;
    right: 17px;
  }
  100% {
    top: 0;
    right: 2px;
    opacity: 1;
  }
}
.icon-btn {
  display: inline-block;
  width: var(--btn-size, 56px);
  height: var(--btn-size, 56px);
  line-height: var(--btn-size, 56px);
  font-size: var(--btn-font-size, 20px);
  background-color: var(--icon-bg, var(--theme-color));
  color: var(--white-color);
  text-align: center;
  border-radius: 50%;
  border: 0;
  transition: 0.4s ease-in-out;
  position: relative;
}
.icon-btn:hover {
  background-color: var(--title-color);
  color: var(--white-color);
  border: 0;
}
.icon-btn.style2 {
  background-color: var(--theme-color2);
  color: var(--white-color);
  border: none;
}
.icon-btn.style2:hover {
  background-color: var(--theme-color);
}
.icon-btn.style3 {
  background-color: var(--theme-color);
  color: var(--white-color);
  border: none;
}
.icon-btn.style3:hover {
  background-color: var(--white-color);
  color: var(--theme-color);
}
.icon-btn.style4 {
  background-color: var(--title-color);
  color: var(--white-color);
  border: none;
}
.icon-btn.style4:hover {
  background-color: var(--theme-color2);
}
.icon-btn.style-border {
  --icon-bg: transparent;
  border: 1px solid var(--th-border-color);
  line-height: 54px;
  color: var(--title-color);
}
.icon-btn.style-border:hover {
  color: var(--white-color);
}

.play-btn {
  display: inline-block;
  position: relative;
  z-index: 1;
}
.play-btn > i {
  display: inline-block;
  width: var(--icon-size, 56px);
  height: var(--icon-size, 56px);
  line-height: var(--icon-size, 56px);
  text-align: center;
  background-color: var(--white-color);
  color: var(--theme-color);
  font-size: var(--icon-font-size, 1.4em);
  border-radius: 50%;
  z-index: 1;
  transition: all ease 0.4s;
}
.play-btn:after, .play-btn:before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  background-color: var(--white-color);
  z-index: -1;
  border-radius: 50%;
  transition: all ease 0.4s;
}
.play-btn:after {
  animation-delay: 2s;
}
.play-btn:hover:after, .play-btn:hover::before,
.play-btn:hover i {
  background-color: var(--theme-color);
  color: var(--white-color);
}
.play-btn.style2 {
  --icon-size: 80px;
  --icon-font-size: 24px;
  /* Extra small devices */
}
.play-btn.style2 i {
  background-color: var(--title-color);
}
.play-btn.style2:hover i {
  background-color: var(--theme-color);
}
.play-btn.style2:hover:before, .play-btn.style2:hover:after {
  background-color: var(--white-color);
}
@media (max-width: 575px) {
  .play-btn.style2 {
    --icon-size: 56px;
    --icon-font-size: 20px;
  }
}
.play-btn.style3 {
  --icon-size: 56px;
  --icon-font-size: 16px;
}
.play-btn.style3 > i {
  background-color: var(--theme-color2);
  color: var(--white-color);
}
.play-btn.style3:before, .play-btn.style3:after {
  background-color: var(--theme-color2);
}
.play-btn.style3:hover > i {
  background-color: var(--theme-color);
  color: var(--white-color);
}
.play-btn.style3:hover:before, .play-btn.style3:hover:after {
  background-color: var(--theme-color);
}
.play-btn.style4 > i {
  --icon-size: 100px;
  color: var(--title-color);
}
.play-btn.style4:before, .play-btn.style4:after {
  background-color: transparent;
  border: 1px solid var(--title-color);
}
.play-btn.style4:hover > i {
  color: var(--white-color);
}
.play-btn.style4:hover:before, .play-btn.style4:hover:after {
  border: 1px solid var(--white-color);
}
.play-btn.style5 {
  --icon-size: 56px;
  --icon-font-size: 16px;
}
.play-btn.style5 > i {
  background-color: var(--white-color);
  color: var(--theme-color2);
}
.play-btn.style5:before, .play-btn.style5:after {
  background-color: var(--theme-color2);
}
.play-btn.style5:hover > i {
  background-color: var(--theme-color);
  color: var(--white-color);
}
.play-btn.style5:hover:before, .play-btn.style5:hover:after {
  background-color: var(--theme-color);
}
.play-btn.style6 {
  /* Extra small devices */
}
.play-btn.style6 > i {
  --icon-size: 100px;
  color: var(--white-color);
  font-size: 20px;
  background: transparent;
  border: 2px solid var(--white-color);
}
.play-btn.style6:before, .play-btn.style6:after {
  background: transparent;
  border: 1px solid var(--white-color);
}
.play-btn.style6:hover > i {
  color: var(--theme-color);
  border-color: var(--theme-color);
}
.play-btn.style6:hover:before, .play-btn.style6:hover:after {
  border-color: var(--theme-color);
}
@media (max-width: 575px) {
  .play-btn.style6 > i {
    --icon-size: 60px;
  }
}
.play-btn.style7 {
  /* Small devices */
}
.play-btn.style7 > i {
  --icon-size: 80px;
  background: var(--theme-color2);
  color: var(--white-color);
}
.play-btn.style7:before, .play-btn.style7:after {
  background-color: var(--theme-color2);
}
.play-btn.style7:hover > i {
  background: var(--white-color);
  color: var(--theme-color2);
}
.play-btn.style7:hover:before, .play-btn.style7:hover:after {
  background: var(--white-color);
}
@media (max-width: 767px) {
  .play-btn.style7 > i {
    --icon-size: 60px;
  }
}
.play-btn.style8 > i {
  --icon-size: 40px;
  --icon-font-size: 16px;
  background: var(--white-color);
  color: var(--title-color);
}
.play-btn.style9 > i {
  --icon-size: 80px;
  --icon-font-size: 20px;
  background: var(--title-color);
  color: var(--theme-color);
}
.play-btn.style9:before, .play-btn.style9:after {
  border: 1px solid var(--white-color);
}
.play-btn.style9:hover > i {
  background: var(--theme-color2);
  color: var(--white-color);
}

.link-btn {
  font-weight: 600;
  font-size: 14px;
  display: inline-flex;
  line-height: 0.8;
  position: relative;
  padding-bottom: 2px;
  margin-bottom: -2px;
  text-transform: uppercase;
  color: var(--title-color);
}
.link-btn .icon {
  overflow: hidden;
  display: inline-block;
  position: relative;
  top: -2px;
}
.link-btn i {
  transition: 0.1s all;
  position: relative;
  font-size: 14px;
  top: -1px;
}
.link-btn:before {
  content: "";
  position: absolute;
  left: 0;
  bottom: 0;
  width: 0;
  height: 1px;
  background-color: var(--theme-color);
  transition: all ease 0.4s;
}
.link-btn:hover, .link-btn.active {
  color: var(--theme-color);
}
.link-btn:hover::before, .link-btn.active::before {
  width: 100%;
}
.link-btn:hover i.fa-arrow-up-right, .link-btn.active i.fa-arrow-up-right {
  transform: rotate(45deg);
}
.link-btn.style2 {
  color: var(--white-color);
}
.link-btn.style2:before {
  width: 100%;
  background: var(--white-color);
}
.link-btn.style2:hover {
  color: var(--theme-color);
}
.link-btn.style2:hover:before {
  background: var(--theme-color);
  width: 70%;
}
.link-btn.style3 {
  color: var(--theme-color);
  margin-bottom: 0;
}
.link-btn.style3:before {
  width: 100%;
  background: var(--theme-color);
}
.link-btn.style3:hover {
  color: var(--white-color);
}
.link-btn.style3:hover:before {
  background: var(--white-color);
  width: 70%;
}
.link-btn.style4 {
  color: var(--title-color);
  margin-bottom: 0;
}
.link-btn.style4:before {
  width: 100%;
  background: var(--title-color);
}
.link-btn.style4:hover:before {
  width: 70%;
}

.scroll-top {
  position: fixed;
  right: 30px;
  bottom: 55px;
  height: 50px;
  width: 50px;
  cursor: pointer;
  display: block;
  border-radius: 50px;
  z-index: 10000;
  opacity: 1;
  visibility: hidden;
  transform: translateY(45px);
  transition: all 300ms linear;
}
.scroll-top:after {
  content: "\f102";
  font-family: var(--icon-font);
  position: absolute;
  text-align: center;
  line-height: 50px;
  font-size: 20px;
  color: var(--theme-color);
  left: 0;
  top: 0;
  height: 50px;
  width: 50px;
  cursor: pointer;
  display: block;
  z-index: 1;
  border: 2px solid var(--theme-color);
  box-shadow: none;
  border-radius: 50%;
}
.scroll-top svg {
  color: var(--theme-color);
  border-radius: 50%;
  background: var(--white-color);
}
.scroll-top svg path {
  fill: none;
}
.scroll-top .progress-circle path {
  stroke: var(--theme-color);
  stroke-width: 20px;
  box-sizing: border-box;
  transition: all 400ms linear;
}
.scroll-top.show {
  opacity: 1;
  visibility: visible;
  transform: translateY(0);
}
.scroll-top.style2 svg {
  background: var(--black-color2);
}

/*------------------- 3.3. Titles -------------------*/
.sec-title {
  margin-bottom: calc(var(--section-title-space) - 11px);
  margin-top: -0.24em;
  font-weight: 700;
}
.sec-title.style2 {
  font-size: 40px;
  /* Medium Large devices */
  /* Small devices */
  /* Small devices */
}
@media (max-width: 1399px) {
  .sec-title.style2 {
    font-size: 36px;
  }
}
@media (max-width: 767px) {
  .sec-title.style2 {
    font-size: 34px;
  }
}
@media (max-width: 767px) {
  .sec-title.style2 {
    font-size: 28px;
  }
}

.sub-title {
  display: inline-block;
  align-items: center;
  font-size: 16px;
  font-weight: 600;
  color: var(--theme-color);
  text-transform: uppercase;
  margin-bottom: 32px;
  line-height: 24px;
  position: relative;
  padding-bottom: 4px;
}
.sub-title:before, .sub-title:after {
  content: "";
  position: absolute;
  height: 2px;
  width: 100%;
  left: 0;
  bottom: 0;
  background: var(--title-color);
  display: inline-block;
}
.sub-title:before {
  width: 20px;
  background: var(--theme-color);
  z-index: 1;
  animation: movingY 8s linear infinite;
}
.sub-title.text-white:after {
  background: var(--white-color);
}
.sub-title.after-none:after {
  display: none;
}
.sub-title.before-none:before {
  display: none;
}
.sub-title.h4 {
  font-size: 30px;
  font-weight: 600;
  line-height: 1;
  margin-top: -0.14em;
}
@media (min-width: 992px) {
  .sub-title.after-lg-none:after {
    display: none;
  }
}
.sub-title.style2 {
  font-weight: 500;
  display: inline-flex;
  gap: 8px;
  padding-bottom: 0;
  line-height: 1;
}
.sub-title.style2:after, .sub-title.style2:before {
  position: relative;
  height: 12px;
  width: 4px;
  background: var(--theme-color);
  animation: none;
}
.sub-title.style3 {
  font-size: 16px;
  font-weight: 400;
  font-family: var(--body-font);
  background: transparent;
  padding: 10px 16px;
}
.sub-title.style3:before {
  display: none;
}
.sub-title.style3:after {
  width: auto;
  height: auto;
  inset: 0;
  border-radius: 48px;
  border: 1px solid transparent;
  background: linear-gradient(0, transparent 0%, var(--theme-color) 100%) border-box;
  -webkit-mask: linear-gradient(#fff 0 0) padding-box, linear-gradient(#fff 0 0);
  -webkit-mask-composite: destination-out;
  mask-composite: exclude;
}
.sub-title.style4 {
  font-weight: 500;
  color: var(--title-color);
  padding-bottom: 2px;
  margin-bottom: 24px;
}
.sub-title.style4:after {
  display: none;
}
.sub-title.style4:before {
  width: 100%;
  height: 12px;
  z-index: -1;
  animation: none;
}

.sec-text {
  font-size: 18px;
}

.box-title {
  font-size: 24px;
  line-height: 1.417;
  font-weight: 600;
  margin-top: -0.32em;
}
.box-title a {
  color: inherit;
}
.box-title a:hover {
  color: var(--theme-color);
}

.box-subtitle {
  margin-bottom: 8px;
}

.box-title2 {
  font-size: 20px;
  line-height: 1.5;
  font-weight: 700;
  margin-top: -0.35em;
}
.box-title2 a {
  color: inherit;
}
.box-title2 a:hover {
  color: var(--theme-color);
}

.title-area {
  margin-bottom: calc(var(--section-title-space) - 12px);
  position: relative;
  z-index: 2;
}
.title-area:has(.sub-title) {
  margin-top: -0.4em;
}
.title-area .sec-title {
  margin-bottom: 18px;
}
.title-area.mb-0 .sec-title {
  margin-bottom: -0.24em;
}
.title-area.text-center .sub-title {
  justify-content: center;
}
@media (min-width: 992px) {
  .title-area.text-lg-start.text-center .sub-title {
    justify-content: start;
  }
}

.mb-32 {
  margin-bottom: 32px;
}

hr.title-line {
  margin-top: 0;
  background-color: var(--th-border-color);
  opacity: 1;
  margin-bottom: var(--section-title-space) !important;
}

.sec-btn {
  text-align: center;
}

.sec-btn,
.title-line {
  margin-bottom: var(--section-title-space);
}

.shadow-title {
  font-family: var(--title-font);
  font-size: 74px;
  font-weight: 900;
  line-height: 1;
  background-image: linear-gradient(180deg, rgba(226, 232, 250, 0.7) 0%, rgba(226, 232, 250, 0) 88.54%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;
  margin: -0.55em 0 -0.45em -0.25em;
}
.shadow-title.color2 {
  background-image: linear-gradient(180deg, #232C47 0%, rgba(20, 29, 56, 0) 91.15%);
}
.shadow-title.color3 {
  background-image: linear-gradient(180deg, #E0E0E0 0%, rgba(220, 214, 214, 0) 93.75%);
}

.title-area2 {
  padding: 50px 100px;
}
.title-area2 .subtitle {
  color: var(--white-color);
  text-transform: uppercase;
  margin-top: -0.4em;
  margin-bottom: 5px;
  display: block;
}
.title-area2 .title {
  color: var(--white-color);
  max-width: 430px;
  margin-bottom: -0.26em;
}

.page-subtitle {
  font-size: 16px;
  font-weight: 500;
  font-family: var(--body-font);
  display: inline-block;
  position: relative;
}
.page-subtitle:after, .page-subtitle:before {
  content: "";
  position: relative;
  width: 4px;
  height: 12px;
  background: var(--theme-color);
  display: inline-block;
  margin: 0 8px;
}
.page-subtitle:before {
  margin-left: 0;
}
.page-subtitle:after {
  margin-right: 0;
}

.page-title {
  font-size: 40px;
  font-weight: 600;
  /* Small devices */
  /* Extra small devices */
}
@media (max-width: 767px) {
  .page-title {
    font-size: 36px;
  }
}
@media (max-width: 575px) {
  .page-title {
    font-size: 32px;
  }
}

@media (max-width: 1700px) {
  .title-area2 {
    padding: 50px 50px;
  }
}
/* Large devices */
@media (max-width: 1199px) {
  .shadow-title {
    font-size: 64px;
  }
  .title-area,
  .sec-title {
    --section-title-space: 60px;
  }
  .title-area.mb-45,
  .sec-title.mb-45 {
    margin-bottom: 36px;
  }
  .title-area.mb-50,
  .sec-title.mb-50 {
    margin-bottom: 40px;
  }
  .sec-btn,
  .title-line {
    --section-title-space: 55px;
  }
  .title-area2 .title {
    max-width: 300px;
  }
}
/* Medium devices */
@media (max-width: 991px) {
  .shadow-title {
    font-size: 60px;
  }
  .title-area,
  .sec-title {
    --section-title-space: 50px;
  }
  .title-area.mb-45,
  .sec-title.mb-45 {
    margin-bottom: 35px;
  }
  .sec-btn,
  .title-line {
    --section-title-space: 50px;
  }
  .sub-title.h4 {
    font-size: 22px;
  }
}
/* Small devices */
@media (max-width: 767px) {
  .title-area2 {
    text-align: center;
  }
  .title-area2 .title {
    max-width: 100%;
  }
}
/* Extra small devices */
@media (max-width: 575px) {
  .shadow-title {
    font-size: 52px;
  }
}
/* Extra small devices */
@media (max-width: 375px) {
  .shadow-title {
    font-size: 40px;
  }
  .title-area2 {
    padding: 40px 20px;
  }
}
/*------------------- 3.4. Common -------------------*/
.shape-mockup-wrap {
  z-index: 2;
  position: relative;
}

.shape-mockup {
  position: absolute;
  z-index: -1;
  pointer-events: none;
}
.shape-mockup.z-index-3 {
  z-index: 3;
  pointer-events: none;
}
.shape-mockup.z-index-1 {
  z-index: 1;
  pointer-events: none;
}
.shape-mockup .svg-img {
  height: 110px;
  width: 110px;
}

.z-index-step1 {
  position: relative;
  z-index: 4 !important;
}

.z-index-common {
  position: relative;
  z-index: 3;
}

.z-index-3 {
  z-index: 3;
}

.z-index-2 {
  z-index: 2;
}

.z-index-n1 {
  z-index: -1;
}

.media-body {
  flex: 1;
}

.badge {
  position: absolute;
  width: -webkit-fit-content;
  width: -moz-fit-content;
  width: fit-content;
  display: inline-block;
  text-align: center;
  background-color: var(--theme-color);
  color: var(--white-color);
  padding: 0.25em 0.45em;
  font-size: 0.7em;
  border-radius: 50%;
  top: 8px;
  right: 8px;
  font-weight: 400;
  transition: 0.3s ease-in-out;
}

.th-social a {
  display: inline-block;
  width: var(--icon-size, 45px);
  height: var(--icon-size, 45px);
  line-height: var(--icon-size, 45px);
  background-color: var(--title-color);
  border: 0;
  color: var(--theme-color);
  font-size: 18px;
  text-align: center;
  margin-right: 11px;
  border-radius: 50%;
}
.th-social a:last-child {
  margin-right: 0;
}
.th-social a:hover {
  background-color: var(--theme-color);
  color: var(--white-color);
}
.th-social.color-theme a {
  color: var(--body-color);
  border-color: var(--theme-color);
}
.th-social.style2 a {
  --icon-size: 48px;
  border: 1px solid var(--th-border-color4);
  background: transparent;
  color: var(--body-color);
  font-size: 18px;
}
.th-social.style2 a:hover {
  background: var(--theme-color);
  color: var(--white-color);
}
.th-social.style3 a {
  --icon-size: 40px;
}
.th-social.style4 a {
  --icon-size: 44px;
  line-height: 46px;
  border-radius: 50%;
  background: var(--black-color2);
  color: var(--theme-color);
  border: 0;
}
.th-social.style4 a:hover {
  background: var(--theme-color);
  color: var(--white-color);
}
.th-social.style5 a {
  background: var(--black-color2);
  color: var(--theme-color);
}
.th-social.style5 a:hover {
  background: var(--theme-color);
  color: var(--title-color);
}
.th-social.style6 a {
  background: var(--smoke-color);
  border: 0;
  font-size: 16px;
  --icon-size: 40px;
  line-height: 42px;
}
.th-social.style6 a:hover {
  background: var(--theme-color2);
  color: var(--white-color);
}
.th-social.style7 a {
  color: var(--theme-color);
}
.th-social.style7 a:hover {
  color: var(--title-color);
}
.th-social.style8 a {
  background: var(--white-color);
  color: var(--theme-color);
}
.th-social.style8 a:hover {
  background: var(--title-color);
}
.th-social.style9 a {
  background: transparent;
  color: var(--theme-color);
  border: 1px solid var(--th-border-color);
  --icon-size: 32px;
  line-height: 32px;
  margin-right: 5px;
  font-size: 14px;
}
.th-social.style9 a:hover {
  background: var(--title-color);
}

.bg-mask {
  mask-size: 100% 100%;
  mask-repeat: no-repeat;
}

.box-icon img {
  transition: 0.4s ease-in-out;
}

.box-text {
  margin-bottom: -0.5em;
}

.btn-group {
  display: inline-flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 30px;
  /* Small devices */
}
.btn-group.style2 {
  gap: 20px 70px;
  /* Small devices */
}
@media (max-width: 767px) {
  .btn-group.style2 {
    gap: 20px 30px;
  }
}
@media (max-width: 767px) {
  .btn-group {
    gap: 20px;
  }
}

.th-bg-img {
  position: absolute;
  inset: 0;
}
.th-bg-img img {
  height: 100%;
  width: 100%;
  object-fit: cover;
}

.color-masking {
  position: relative;
  display: inline-block;
}
.color-masking .masking-src {
  position: absolute;
  inset: 0;
  mix-blend-mode: color;
  background: var(--theme-color);
}

.color-masking2 {
  position: relative;
  display: inline-block;
}
.color-masking2 .masking-src {
  position: absolute;
  inset: 0;
  mix-blend-mode: color;
  background: var(--theme-color2);
}

.mfp-zoom-in .mfp-content {
  opacity: 0;
  transition: all 0.4s ease;
  transform: scale(0.5);
}

.mfp-zoom-in.mfp-bg {
  opacity: 0;
  transition: all 0.4s ease;
}

.mfp-zoom-in.mfp-ready .mfp-content {
  opacity: 1;
  transform: scale(1);
}

.mfp-zoom-in.mfp-ready.mfp-bg {
  opacity: 0.7;
}

.mfp-zoom-in.mfp-removing .mfp-content {
  transform: scale(0.7);
  opacity: 0;
}

.mfp-zoom-in.mfp-removing.mfp-bg {
  opacity: 0;
}

.th-radius {
  border-radius: 50px;
}

.grid_lines {
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  z-index: -1;
  overflow: hidden;
}
.grid_lines .grid_line {
  position: relative;
  width: 1px;
  height: 100%;
  display: inline-block;
  background-color: rgba(255, 255, 255, 0.1);
  mix-blend-mode: difference;
}
.grid_lines .grid_line:after, .grid_lines .grid_line:before {
  content: "";
  position: absolute;
  top: -60px;
  left: 0;
  width: 1px;
  height: 60px;
  background: var(--white-color);
  background: linear-gradient(0deg, var(--white-color) 0%, rgba(255, 255, 255, 0) 100%);
  animation: gridanim 25s linear infinite;
  opacity: 0.3;
}
.grid_lines .grid_line:after {
  animation-delay: 10s;
}

@keyframes gridanim {
  0% {
    top: -60px;
  }
  50% {
    top: 100%;
  }
  100% {
    top: -60px;
    background: linear-gradient(180deg, var(--white-color) 0%, rgba(255, 255, 255, 0) 100%);
  }
}
@keyframes gridanim2 {
  0% {
    top: -60px;
  }
  50% {
    top: 100%;
  }
  100% {
    top: -60px;
    background: linear-gradient(180deg, var(--theme-color) 0%, rgba(255, 255, 255, 0) 100%);
  }
}
/*------------------- 3.6. Font -------------------*/
.font-icon {
  font-family: var(--icon-font);
}

.font-title {
  font-family: var(--title-font);
}

.font-body {
  font-family: var(--body-font);
}

.fw-extralight {
  font-weight: 100;
}

.fw-light {
  font-weight: 300;
}

.fw-normal {
  font-weight: 400;
}

.fw-medium {
  font-weight: 500;
}

.fw-semibold {
  font-weight: 600;
}

.fw-bold {
  font-weight: 700;
}

.fw-extrabold {
  font-weight: 800;
}

.fs-md {
  font-size: 18px;
}

.fs-16 {
  font-size: 16px !important;
}

.fs-20 {
  font-size: 20px !important;
  /* Extra small devices */
}
@media (max-width: 575px) {
  .fs-20 {
    font-size: 16px !important;
  }
}

.fs-xs {
  font-size: 14px;
}

.title-font {
  font-family: var(--title-font);
}

/*------------------- 3.7. Background -------------------*/
.bg-theme {
  background-color: var(--theme-color) !important;
}

.bg-theme2 {
  background-color: var(--theme-color2) !important;
}

.bg-smoke {
  background-color: var(--smoke-color) !important;
}

.bg-smoke2 {
  background-color: var(--smoke-color2) !important;
}

.bg-gray {
  background-color: var(--gray-color) !important;
}

.bg-gray2 {
  background-color: var(--gray-color2) !important;
}

.bg-white {
  background-color: var(--white-color) !important;
}

.bg-light {
  background-color: var(--light-color) !important;
}

.bg-black {
  background-color: var(--black-color) !important;
}

.bg-black2 {
  background-color: var(--black-color2) !important;
}

.bg-black3 {
  background-color: var(--black-color3) !important;
}

.bg-black4 {
  background-color: var(--black-color4) !important;
}

.bg-title {
  background-color: var(--title-color) !important;
}

.bg-body {
  background-color: var(--body-color) !important;
}

.gradient-body {
  background-image: linear-gradient(100.62deg, rgba(249, 215, 175, 0.3) 0%, rgba(214, 202, 245, 0.3) 24.03%, rgba(198, 241, 255, 0.3) 45.73%, rgba(248, 242, 222, 0.3) 69.05%, rgba(212, 179, 253, 0.3) 100.44%);
}

.gr-bg1 {
  background-image: linear-gradient(80deg, rgba(249, 215, 175, 0.3) 0%, rgba(214, 202, 245, 0.3) 23.81%, rgba(198, 241, 255, 0.3) 45.3%, rgba(248, 242, 222, 0.3) 68.4%, rgba(212, 179, 253, 0.3) 99.5%);
}

.gr-bg2 {
  background-image: linear-gradient(100.62deg, rgba(249, 215, 175, 0.3) 0%, rgba(214, 202, 245, 0.3) 24.03%, rgba(198, 241, 255, 0.3) 45.73%, rgba(248, 242, 222, 0.3) 69.05%, rgba(212, 179, 253, 0.3) 100.44%);
}

.gr-bg3 {
  background-image: linear-gradient(110.59deg, rgba(236, 207, 254, 0.4) 0%, rgba(255, 221, 221, 0.5) 46.79%, rgba(247, 255, 229, 0.7) 100%);
  backdrop-filter: blur(10px);
}

.gr-bg4 {
  background-image: linear-gradient(105.44deg, rgba(255, 229, 133, 0.4) 0%, rgba(189, 255, 199, 0.4) 48.48%, rgba(223, 109, 223, 0.4) 100%);
  backdrop-filter: blur(15px);
}

.background-image,
[data-bg-src] {
  background-repeat: no-repeat;
  background-size: cover;
  background-position: center center;
}

.bg-fluid {
  background-repeat: no-repeat;
  background-size: 100% 100%;
  background-position: center center;
}

.bg-auto {
  background-size: auto auto;
}

.bg-top-center {
  background-size: auto;
  background-position: top center;
}

.bg-repeat {
  background-size: auto;
  background-repeat: repeat;
}

.bg-bottom-right {
  background-size: auto;
  background-position: bottom right;
}

.bg-top-right {
  background-size: auto;
  background-position: top right;
}

.bg-top-left {
  background-size: auto;
  background-position: top left;
}

.bg-attachment {
  background-attachment: fixed;
}

/*------------------- 3.8. Text Color -------------------*/
.text-theme {
  color: var(--theme-color) !important;
}

.text-theme2 {
  color: var(--theme-color2) !important;
}

.text-title {
  color: var(--title-color) !important;
}

.text-body {
  color: var(--body-color) !important;
}

.text-white {
  color: var(--white-color) !important;
}

.text-light {
  color: var(--light-color) !important;
}

.text-yellow {
  color: var(--yellow-color) !important;
}

.text-success {
  color: var(--success-color) !important;
}

.text-error {
  color: var(--error-color) !important;
}

.text-inherit {
  color: inherit;
}
.text-inherit:hover {
  color: var(--theme-color);
}

a.text-theme:hover,
.text-reset:hover {
  text-decoration: underline;
}

/*------------------- 3.9. Overlay -------------------*/
.overlay {
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  width: 100%;
}

.position-center {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}

[data-overlay] {
  position: relative;
  z-index: 2;
}
[data-overlay] [class^=col-],
[data-overlay] [class*=col-] {
  z-index: 1;
}

[data-overlay]:before {
  content: "";
  position: absolute;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
}

[data-overlay=theme]:before {
  background-color: var(--theme-color);
}

[data-overlay=theme2]:before {
  background-color: var(--theme-color2);
}

[data-overlay=title]:before {
  background-color: var(--title-color);
}

[data-overlay=smoke]:before {
  background-color: var(--smoke-color);
}

[data-overlay=gray]:before {
  background-color: var(--gray-color);
}

[data-overlay=white]:before {
  background-color: var(--white-color);
}

[data-overlay=black]:before {
  background-color: var(--black-color);
}

[data-overlay=black4]:before {
  background-color: var(--black-color4);
}

[data-overlay=black7]:before {
  background-color: var(--black-color7);
}

[data-opacity="1"]:before {
  opacity: 0.1;
}

[data-opacity="2"]:before {
  opacity: 0.2;
}

[data-opacity="3"]:before {
  opacity: 0.3;
}

[data-opacity="4"]:before {
  opacity: 0.4;
}

[data-opacity="5"]:before {
  opacity: 0.5;
}

[data-opacity="6"]:before {
  opacity: 0.6;
}

[data-opacity="7"]:before {
  opacity: 0.7;
}

[data-opacity="8"]:before {
  opacity: 0.8;
}

[data-opacity="9"]:before {
  opacity: 0.9;
}

[data-opacity="10"]:before {
  opacity: 1;
}

/*------------------- 3.10. Animation -------------------*/
.ripple-animation, .play-btn:after, .play-btn:before {
  animation-duration: var(--ripple-ani-duration);
  animation-timing-function: ease-in-out;
  animation-iteration-count: infinite;
  animation-name: ripple;
}

@keyframes ripple {
  0% {
    transform: scale(1);
    opacity: 0;
  }
  30% {
    opacity: 0.4;
  }
  100% {
    transform: scale(1.8);
    opacity: 0;
  }
}
@keyframes ripple2 {
  0% {
    transform: scale(1);
    opacity: 0;
  }
  30% {
    opacity: 0.4;
  }
  100% {
    transform: scale(2.8);
    opacity: 0;
  }
}
.fancy-animation {
  animation: morph 8s ease-in-out infinite;
}

@keyframes morph {
  0% {
    border-radius: 60% 40% 30% 70%/60% 30% 70% 40%;
  }
  50% {
    border-radius: 30% 60% 70% 40%/50% 60% 30% 60%;
  }
  100% {
    border-radius: 60% 40% 30% 70%/60% 30% 70% 40%;
  }
}
.movingX {
  animation: movingX 8s linear infinite;
}

@keyframes movingX {
  0% {
    transform: translateX(0);
  }
  50% {
    transform: translateX(50px);
  }
  100% {
    transform: translateX(0);
  }
}
.shake {
  animation: shake 2s linear infinite;
}

@keyframes shake {
  0% {
    transform: rotate(0);
  }
  50% {
    transform: rotate(-10deg);
  }
  100% {
    transform: rotate(0);
  }
}
.movingCar {
  animation: movingCar 25s linear infinite;
}

@keyframes movingCar {
  0% {
    transform: translateX(0) rotateY(0deg);
  }
  50% {
    transform: translateX(calc(-100vw + 108%));
  }
  51% {
    transform: translateX(calc(-100vw + 108%)) rotateY(180deg);
  }
  100% {
    transform: translateX(0) rotateY(180deg);
  }
}
.moving {
  animation: moving 8s linear infinite;
}

@keyframes moving {
  0% {
    transform: translateX(0);
  }
  50% {
    transform: translateX(-50px);
  }
  100% {
    transform: translateX(0);
  }
}
.movingY {
  animation: movingY 8s linear infinite;
}

@keyframes movingY {
  0% {
    left: 0;
  }
  50% {
    left: calc(100% - 20px);
  }
  100% {
    left: 0;
  }
}
.jump {
  animation: jumpAni 7s linear infinite;
}

@keyframes jumpAni {
  0% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-30px);
  }
  100% {
    transform: translateY(0);
  }
}
.jump-reverse {
  animation: jumpReverseAni 7s linear infinite;
}

@keyframes jumpReverseAni {
  0% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(30px);
  }
  100% {
    transform: translateY(0);
  }
}
.spin {
  animation: spin 10s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0);
  }
  100% {
    transform: rotate(360deg);
  }
}
.spin-reverse {
  animation: spin2 10s linear infinite;
}

@keyframes spin2 {
  0% {
    transform: rotate(0);
  }
  100% {
    transform: rotate(-360deg);
  }
}
.bg-color-ani,
.color-animate {
  animation: bgColor 6s linear infinite;
}

@keyframes bgColor {
  0% {
    background-color: #F2BA4C;
  }
  25% {
    background-color: #81F24C;
  }
  50% {
    background-color: #41F27D;
  }
  75% {
    background-color: #0500FF;
  }
  100% {
    background-color: #F2BA4C;
  }
}
@keyframes animate-positive {
  0% {
    width: 0;
  }
}
.fadein,
.scalein,
.slidetopleft,
.slidebottomright,
.slideinleft,
.slideinright,
.slideindown,
.slideinup,
.rollinleft,
.rollinright {
  opacity: 0;
  animation-fill-mode: forwards;
  animation-iteration-count: 1;
  animation-duration: 1.3s;
  animation-delay: 0.3s;
  animation-name: var(--animation-name);
}

.swiper-slide-active .fadein {
  --animation-name: fadein;
}
.swiper-slide-active .scalein {
  --animation-name: scalein;
}
.swiper-slide-active .slidetopleft {
  --animation-name: slidetopleft;
}
.swiper-slide-active .slidebottomright {
  --animation-name: slidebottomright;
}
.swiper-slide-active .slideinleft {
  --animation-name: slideinleft;
}
.swiper-slide-active .slideinright {
  --animation-name: slideinright;
}
.swiper-slide-active .slideinup {
  --animation-name: slideinup;
}
.swiper-slide-active .slideindown {
  --animation-name: slideindown;
}
.swiper-slide-active .rollinleft {
  --animation-name: rollinleft;
}
.swiper-slide-active .rollinright {
  --animation-name: rollinright;
}

@keyframes fadein {
  0% {
    opacity: 0;
  }
  100% {
    opacity: 1;
  }
}
@keyframes slideinup {
  0% {
    opacity: 0;
    transform: translateY(100px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}
@keyframes slideinright {
  0% {
    opacity: 0;
    transform: translateX(180px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}
@keyframes slideindown {
  0% {
    opacity: 0;
    transform: translateY(-100px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}
@keyframes slideinleft {
  0% {
    opacity: 0;
    transform: translateX(-100px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}
@keyframes slidebottomright {
  0% {
    opacity: 0;
    transform: translateX(120px) translateY(120px);
  }
  100% {
    opacity: 1;
    transform: translateX(0) translateY(0);
  }
}
@keyframes slidetopleft {
  0% {
    opacity: 0;
    transform: translateX(-100px) translateY(-100px);
  }
  100% {
    opacity: 1;
    transform: translateX(0) translateY(0);
  }
}
/*animation bubble****************/
.animation-bubble {
  width: 100%;
  position: absolute;
  top: 0;
  left: 0;
  height: 100%;
  overflow: hidden;
  z-index: 1;
  pointer-events: none;
}

.animation-bubble div[class^=bubble-] {
  height: 1px;
  width: 1px;
  position: absolute;
  background: url(../img/shape/star.svg) no-repeat center center;
  background-size: cover;
  border-radius: 50%;
  -webkit-transform: translateX(-50%);
  transform: translateX(-50%);
  pointer-events: none;
}

@media (max-width: 1024px) {
  .animation-bubble > :nth-child(even) {
    display: none;
  }
}
.bubble-1 {
  bottom: -5px;
  left: 68%;
  animation: bubble-animation 4.5s infinite ease-in -6.57s;
}

.bubble-2 {
  bottom: -71px;
  left: 97%;
  animation: bubble-animation 4.5s infinite ease-in -5.07s;
}

.bubble-3 {
  bottom: -71px;
  left: 43%;
  animation: bubble-animation 4.5s infinite ease-in -6.73s;
}

.bubble-4 {
  bottom: -3.8px;
  left: 82%;
  animation: bubble-animation 4.5s infinite ease-in -4.04s;
}

.bubble-5 {
  bottom: -73.4px;
  left: 29%;
  animation: bubble-animation 4.5s infinite ease-in -3.11s;
}

.bubble-6 {
  bottom: -71px;
  left: 41%;
  animation: bubble-animation 4.5s infinite ease-in -5.95s;
}

.bubble-7 {
  bottom: -79.4px;
  left: 14%;
  animation: bubble-animation 4.5s infinite ease-in -3.68s;
}

.bubble-8 {
  bottom: -115.4px;
  left: 90%;
  animation: bubble-animation 4.5s infinite ease-in -3.89s;
}

.bubble-9 {
  bottom: -44.6px;
  left: 33%;
  animation: bubble-animation 4.5s infinite ease-in -1.09s;
}

.bubble-10 {
  bottom: -6.2px;
  left: 59%;
  animation: bubble-animation 4.5s infinite ease-in -0.96s;
}

@keyframes bubble-animation {
  0% {
    -webkit-transform: translate3d(-50%, 0, 0);
    transform: translate3d(-50%, 0, 0);
    height: 1px;
    width: 1px;
  }
  100% {
    -webkit-transform: translate3d(-50%, -280px, 0);
    transform: translate3d(-50%, -280px, 0);
    height: 30px;
    width: 30px;
  }
}
@keyframes toTopFromBottom {
  49% {
    transform: translateY(-100%);
  }
  50% {
    opacity: 0;
    transform: translateY(100%);
  }
  51% {
    opacity: 1;
  }
}
/*custom-animation**********************/
.custom-anim-right {
  animation: custom-anim-right 1.3s forwards cubic-bezier(0.645, 0.045, 0.355, 1) 0.4s;
  opacity: 0;
}

@keyframes custom-anim-right {
  0% {
    transform: translateX(5%);
    clip-path: inset(0 0 0 100%);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    clip-path: inset(0 0 0 0);
    opacity: 1;
  }
}
.custom-anim-left {
  animation: custom-anim-left 1.3s forwards cubic-bezier(0.645, 0.045, 0.355, 1) 0.4s;
  opacity: 0;
}

@keyframes custom-anim-left {
  0% {
    transform: translateX(-5%);
    clip-path: inset(0 100% 0 0);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    clip-path: inset(0 0 0 0);
    opacity: 1;
  }
}
.custom-anim-top {
  animation: custom-anim-top 1.3s forwards cubic-bezier(0.645, 0.045, 0.355, 1);
  opacity: 0;
}

@keyframes custom-anim-top {
  0% {
    transform: translateY(-5%);
    clip-path: inset(0 0 100% 0);
    opacity: 0;
  }
  100% {
    transform: translateY(0);
    clip-path: inset(0 0 0 0);
    opacity: 1;
  }
}
.custom-anim-bottom {
  animation: custom-anim-bottom 1.3s forwards cubic-bezier(0.645, 0.045, 0.355, 1);
  opacity: 0;
}

@keyframes custom-anim-bottom {
  0% {
    transform: translate3d(0, 100%, 0);
    clip-path: inset(100% 0 0 0);
    opacity: 0;
  }
  100% {
    transform: translateZ(0);
    clip-path: inset(0 0 0 0);
    opacity: 1;
  }
}
.fadeinup {
  opacity: 0;
  animation: fadeinup 1.3s forwards 1;
}

@keyframes fadeinup {
  0% {
    opacity: 0;
    transform: translateY(100px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}
.fadeinright {
  opacity: 0;
  animation: fadeinright 1.3s forwards 1;
}

@keyframes fadeinright {
  0% {
    opacity: 0;
    transform: translateX(100px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}
.fadeinleft {
  opacity: 0;
  animation: fadeinleft 1.3s forwards 1;
}

@keyframes fadeinleft {
  0% {
    opacity: 0;
    transform: translateX(-50px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}
/*=================================
    04. Template Style
==================================*/
/*------------------- 4.1. Widget  -------------------*/
.widget_nav_menu ul,
.widget_meta ul,
.widget_pages ul,
.widget_archive ul,
.widget_categories ul,
.wp-block-page-list ul,
.wp-block-categories ul {
  list-style: none;
  padding: 0;
  margin: 0 0 0 0;
}
.widget_nav_menu a,
.widget_meta a,
.widget_pages a,
.widget_archive a,
.widget_categories a,
.wp-block-page-list a,
.wp-block-categories a {
  display: block;
  background-color: var(--white-color);
  margin: 0 0 18px;
  font-size: 16px;
  line-height: 1.313;
  color: var(--title-color);
  transition: 0.4s all ease;
}
.widget_nav_menu li,
.widget_meta li,
.widget_pages li,
.widget_archive li,
.widget_categories li,
.wp-block-page-list li,
.wp-block-categories li {
  display: block;
  position: relative;
  border-bottom: 1px solid var(--th-border-color);
  margin-bottom: 18px;
}
.widget_nav_menu li > span,
.widget_meta li > span,
.widget_pages li > span,
.widget_archive li > span,
.widget_categories li > span,
.wp-block-page-list li > span,
.wp-block-categories li > span {
  color: var(--title-color);
  text-align: center;
  position: absolute;
  right: 0;
  top: -2px;
  transition: all ease 0.4s;
  pointer-events: none;
  min-width: 20px;
  text-align: right;
}
.widget_nav_menu .menu,
.widget_nav_menu > ul,
.widget_meta .menu,
.widget_meta > ul,
.widget_pages .menu,
.widget_pages > ul,
.widget_archive .menu,
.widget_archive > ul,
.widget_categories .menu,
.widget_categories > ul,
.wp-block-page-list .menu,
.wp-block-page-list > ul,
.wp-block-categories .menu,
.wp-block-categories > ul {
  margin-top: -0.3em;
  margin-bottom: -0.3em;
}
.widget_nav_menu .menu > li:last-child,
.widget_nav_menu > ul > li:last-child,
.widget_meta .menu > li:last-child,
.widget_meta > ul > li:last-child,
.widget_pages .menu > li:last-child,
.widget_pages > ul > li:last-child,
.widget_archive .menu > li:last-child,
.widget_archive > ul > li:last-child,
.widget_categories .menu > li:last-child,
.widget_categories > ul > li:last-child,
.wp-block-page-list .menu > li:last-child,
.wp-block-page-list > ul > li:last-child,
.wp-block-categories .menu > li:last-child,
.wp-block-categories > ul > li:last-child {
  border-bottom: 0;
  margin-bottom: 0;
}
.widget_nav_menu .menu > li:last-child > a,
.widget_nav_menu > ul > li:last-child > a,
.widget_meta .menu > li:last-child > a,
.widget_meta > ul > li:last-child > a,
.widget_pages .menu > li:last-child > a,
.widget_pages > ul > li:last-child > a,
.widget_archive .menu > li:last-child > a,
.widget_archive > ul > li:last-child > a,
.widget_categories .menu > li:last-child > a,
.widget_categories > ul > li:last-child > a,
.wp-block-page-list .menu > li:last-child > a,
.wp-block-page-list > ul > li:last-child > a,
.wp-block-categories .menu > li:last-child > a,
.wp-block-categories > ul > li:last-child > a {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: 0;
}
.widget_nav_menu a:hover,
.widget_meta a:hover,
.widget_pages a:hover,
.widget_archive a:hover,
.widget_categories a:hover,
.wp-block-page-list a:hover,
.wp-block-categories a:hover {
  color: var(--theme-color);
}
.widget_nav_menu a:hover ~ span,
.widget_meta a:hover ~ span,
.widget_pages a:hover ~ span,
.widget_archive a:hover ~ span,
.widget_categories a:hover ~ span,
.wp-block-page-list a:hover ~ span,
.wp-block-categories a:hover ~ span {
  color: var(--theme-color);
}
.widget_nav_menu .sub-menu,
.widget_nav_menu .children,
.widget_meta .sub-menu,
.widget_meta .children,
.widget_pages .sub-menu,
.widget_pages .children,
.widget_archive .sub-menu,
.widget_archive .children,
.widget_categories .sub-menu,
.widget_categories .children,
.wp-block-page-list .sub-menu,
.wp-block-page-list .children,
.wp-block-categories .sub-menu,
.wp-block-categories .children {
  margin-left: 10px;
}

.wp-block-page-list,
.wp-block-categories {
  list-style: none;
  padding: 0;
}
.wp-block-page-list > li:last-child > a,
.wp-block-categories > li:last-child > a {
  border-bottom: none;
}

.wp-block-page-list {
  margin: 0;
}

.widget_nav_menu a,
.widget_meta a,
.widget_pages a {
  padding-right: 20px;
}

.widget_nav_menu .sub-menu {
  margin-left: 10px;
}

.wp-block-archives {
  list-style: none;
  margin: 0;
  padding: 0;
}
.wp-block-archives a:not(:hover) {
  color: inherit;
}

.th-blog ul.wp-block-archives li {
  margin: 5px 0;
}

.widget {
  padding: var(--widget-padding-y, 32px) var(--widget-padding-x, 24px);
  background-color: transparent;
  margin-bottom: 40px;
  position: relative;
  border-radius: 8px;
  border: 1px solid var(--th-border-color);
}
.widget[data-overlay]:before {
  z-index: -1;
  border-radius: 8px;
}
.widget select,
.widget input,
.widget .form-select,
.widget .form-control,
.widget textarea {
  background-color: var(--white-color);
}
.widget textarea {
  min-height: 120px;
}
.widget .form-group > i {
  background-color: var(--white-color);
}

.wp-block-archives__label,
.widget_title {
  position: relative;
  font-size: 24px;
  font-weight: 600;
  font-family: var(--title-font);
  line-height: 1em;
  margin: -0.12em 0 32px 0;
  padding-bottom: 20px;
  border-bottom: 3px solid var(--th-border-color2);
}
.wp-block-archives__label:after,
.widget_title:after {
  content: "";
  position: absolute;
  bottom: -3px;
  left: 0;
  height: 3px;
  width: 50px;
  background: var(--theme-color);
}

.widget-form {
  --bs-gutter-x: 20px;
}

.widget .search-form {
  position: relative;
  display: flex;
}
.widget .search-form input {
  flex: 1;
  border-radius: 100px;
  padding-right: 85px;
  border: 1px solid var(--th-border-color);
  height: 56px;
}
.widget .search-form input:focus {
  border-color: var(--theme-color);
}
.widget .search-form button {
  border: none;
  font-size: 16px;
  background-color: var(--theme-color);
  color: var(--white-color);
  display: inline-block;
  padding: 0;
  width: 40px;
  height: 40px;
  text-align: center;
  border-radius: 50%;
  position: absolute;
  top: 8px;
  right: 8px;
}
.widget .search-form button:hover {
  background-color: var(--title-color);
}

.wp-block-tag-cloud,
.tagcloud {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}
.wp-block-tag-cloud a,
.tagcloud a {
  display: inline-block;
  font-size: 16px;
  font-weight: 400;
  font-family: var(--body-font);
  text-transform: capitalize;
  line-height: 1;
  padding: 10px 11px;
  color: var(--title-color);
  background-color: var(--smoke-color2);
  border-radius: 4px;
}
.wp-block-tag-cloud a:hover,
.tagcloud a:hover {
  background-color: var(--theme-color);
  color: var(--white-color) !important;
  border-color: var(--theme-color);
}

.sidebar-gallery {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
  max-width: 320px;
}
.sidebar-gallery .gallery-btn {
  position: absolute;
  top: 20px;
  left: 0;
  color: var(--white-color);
  visibility: hidden;
  opacity: 0;
  height: 100%;
  width: 100%;
  transform: none;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
}
.sidebar-gallery .gallery-btn:hover {
  color: var(--theme-color);
}
.sidebar-gallery .gallery-thumb {
  overflow: hidden;
  position: relative;
  border-radius: 20px;
}
.sidebar-gallery .gallery-thumb:before {
  content: "";
  height: 100%;
  width: 100%;
  background-color: var(--theme-color);
  mix-blend-mode: multiply;
  opacity: 0.8;
  position: absolute;
  top: 0;
  left: 0;
  transform: scaleX(0);
  border-radius: inherit;
  transition: 0.4s ease-in-out;
}
.sidebar-gallery .gallery-thumb img {
  width: 100%;
}
.sidebar-gallery .gallery-thumb:hover:before {
  transform: scaleX(1);
}
.sidebar-gallery .gallery-thumb:hover .gallery-btn {
  visibility: visible;
  opacity: 1;
  top: 0;
}
.sidebar-gallery .gallery-thumb:hover .gallery-btn:hover {
  color: var(--white-color);
}
.sidebar-gallery .wp-block-image {
  overflow: hidden;
  position: relative;
  border-radius: 5px;
  width: 100% !important;
}
.sidebar-gallery .wp-block-image img {
  width: 100%;
}

.recent-post {
  display: flex;
  align-items: center;
  margin-bottom: 32px;
}
.recent-post:last-child {
  margin-bottom: 0;
}
.recent-post .media-img {
  margin-right: 22px;
  width: 85px;
  overflow: hidden;
  border-radius: 4px;
  position: relative;
}
.recent-post .media-img img {
  width: 100%;
  transition: 0.4s ease-in-out;
}
.recent-post .media-img:after {
  content: "\f0c1";
  font-family: var(--icon-font);
  position: absolute;
  inset: 0;
  text-align: center;
  line-height: 85px;
  font-size: 24px;
  background-color: rgba(0, 0, 0, 0.7);
  color: var(--theme-color);
  pointer-events: none;
  transform: scale(0);
  transition: 0.4s ease-in-out;
}
.recent-post .post-title {
  font-weight: 600;
  font-size: 18px;
  line-height: 26px;
  margin: -0.35em 0 10px 0;
  font-family: var(--title-font);
  text-transform: capitalize;
}
.recent-post .recent-post-meta {
  margin-top: -0.3em;
}
.recent-post .recent-post-meta a {
  text-transform: capitalize;
  font-size: 14px;
  color: var(--body-color);
  font-family: var(--body-font);
}
.recent-post .recent-post-meta a i {
  margin-right: 8px;
  color: var(--theme-color);
}
.recent-post .recent-post-meta a:hover {
  color: var(--theme-color);
}
.recent-post:hover .media-img img {
  transform: scale(1.1);
}
.recent-post:hover .media-img:after {
  transform: scale(1);
}

.sidebar-area.sidebar-sticky {
  position: sticky;
  top: 140px;
  /* Medium devices */
}
@media (max-width: 991px) {
  .sidebar-area.sidebar-sticky {
    position: relative;
    top: auto;
    margin-bottom: -40px;
  }
}
.sidebar-area ul.wp-block-latest-posts {
  margin-bottom: 0;
}
.sidebar-area ul.wp-block-latest-posts li:last-child {
  margin-bottom: 0;
}
.sidebar-area select,
.sidebar-area input {
  background-color: var(--white-color);
  border-radius: 50px;
}
.sidebar-area .sidebar-gallery {
  margin-bottom: 0 !important;
}
.sidebar-area .widget .wp-block-search {
  margin-bottom: 0;
}
.sidebar-area .wp-block-search__label,
.sidebar-area .wp-block-group__inner-container h2 {
  position: relative;
  font-size: 24px;
  font-weight: 700;
  font-family: var(--title-font);
  line-height: 1em;
  padding-bottom: 20px;
  margin: -0.12em 0 38px 0;
}
.sidebar-area .wp-block-search__label:after, .sidebar-area .wp-block-search__label:before,
.sidebar-area .wp-block-group__inner-container h2:after,
.sidebar-area .wp-block-group__inner-container h2:before {
  content: "";
  height: 3px;
  width: 100%;
  background-color: var(--th-border-color);
  position: absolute;
  bottom: 0;
  left: 0;
}
.sidebar-area .wp-block-search__label:after,
.sidebar-area .wp-block-group__inner-container h2:after {
  background-color: var(--theme-color);
  width: 40px;
}
.sidebar-area ol.wp-block-latest-comments {
  padding: 0;
  margin: 0;
}
.sidebar-area ol.wp-block-latest-comments li {
  line-height: 1.5;
  margin: 0 0 20px 0;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  padding-bottom: 20px;
}
.sidebar-area ol.wp-block-latest-comments li:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: none;
}

.download-widget-wrap .th-btn {
  width: 100%;
}
.download-widget-wrap .th-btn:first-child {
  margin-bottom: 15px;
}
.download-widget-wrap .th-btn.style2 {
  color: var(--body-color);
}
.download-widget-wrap .th-btn.style2:before {
  background-color: var(--theme-color);
}
.download-widget-wrap .th-btn.style2:hover {
  color: var(--white-color);
  border-color: var(--theme-color);
}

.widget_info {
  border: 0;
  background: var(--smoke-color);
  padding: 0 !important;
  border-radius: 16px;
  overflow: hidden;
}
.widget_info .th-btn {
  width: 100%;
  margin-bottom: 10px;
  display: block;
}
.widget_info .th-video {
  margin-bottom: 20px;
}
.widget_info .widget_title {
  text-align: center;
  font-size: 28px;
  font-weight: 600;
  color: var(--white-color);
  background: var(--theme-color);
  border: 0;
  padding: 36px 20px;
  margin: 0;
}
.widget_info .widget_title:after {
  display: none;
}

.info-list {
  margin-top: -20px;
  padding: 48px 48px 48px;
  /* Extra large devices */
}
.info-list ul {
  list-style: none;
  padding: 0;
  margin: 0;
}
.info-list strong {
  font-weight: 600;
  color: var(--title-color);
  display: block;
  font-size: 18px;
  position: relative;
  margin-bottom: 4px;
}
.info-list strong:after {
  content: "";
  position: absolute;
  width: 2px;
  height: 13px;
  top: 6px;
  left: -10px;
  background: var(--theme-color);
}
.info-list li {
  border-bottom: 1px solid var(--th-border-color);
  padding: 16px 0;
  display: flex;
  align-items: center;
  gap: 5px;
  padding-left: 10px;
}
.info-list li:last-child {
  border-bottom: none;
  padding-bottom: 0;
  margin-bottom: 0;
}
.info-list li a {
  color: var(--body-color);
}
.info-list li .th-social {
  margin-top: 10px;
}
@media (max-width: 1500px) {
  .info-list {
    padding: 38px 30px 38px;
  }
}

.price_slider_wrapper {
  margin-top: -0.5em;
}
.price_slider_wrapper .price_label span {
  display: inline-block;
  color: var(--body-color);
}
.price_slider_wrapper .ui-slider {
  height: 6px;
  position: relative;
  width: 100%;
  background-color: var(--theme-color);
  border: none;
  margin-top: 15px;
  margin-bottom: 25px;
  cursor: pointer;
  border-radius: 0;
}
.price_slider_wrapper .ui-slider-range {
  border: none;
  cursor: pointer;
  position: absolute;
  top: 0;
  height: 100%;
  z-index: 1;
  display: block;
  background-color: var(--title-color);
}
.price_slider_wrapper .ui-slider-handle {
  width: 16px;
  height: 16px;
  line-height: 16px;
  border-radius: 50%;
  text-align: center;
  padding: 0;
  cursor: pointer;
  position: absolute;
  margin-top: -5px;
  z-index: 2;
  background-color: var(--white-color);
  border: 3px solid var(--title-color);
  transform: translateX(-1px);
}
.price_slider_wrapper .ui-slider-handle:focus {
  outline: none;
  box-shadow: 1.5px 2.598px 10px 0px rgba(0, 0, 0, 0.15);
}
.price_slider_wrapper .ui-slider-handle:last-child {
  transform: translateX(-9px);
}
.price_slider_wrapper button,
.price_slider_wrapper .button {
  background-color: var(--theme-color);
  color: var(--white-color);
  font-weight: 500;
  line-height: 1.6;
  text-transform: capitalize;
  text-align: center;
  border: none;
  display: inline-block;
  overflow: hidden;
  position: relative;
  z-index: 2;
  padding: 7px 20px;
  min-width: 100px;
  font-size: 14px;
  border-radius: 0;
  transition: 0.4s ease-in;
}
.price_slider_wrapper button:hover,
.price_slider_wrapper .button:hover {
  background-color: var(--title-color);
}

.widget_shopping_cart .widget_title {
  margin-bottom: 35px;
  border-bottom: none;
}
.widget_shopping_cart ul {
  margin: 0;
  padding: 0;
}
.widget_shopping_cart ul li {
  list-style-type: none;
}
.widget_shopping_cart .mini_cart_item {
  position: relative;
  padding: 30px 30px 30px 90px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  margin-bottom: 0;
  text-align: left;
}
.widget_shopping_cart .mini_cart_item:first-child {
  border-top: 1px solid rgba(0, 0, 0, 0.1);
}
.widget_shopping_cart .cart_list a:not(.remove) {
  display: block;
  color: var(--body-color);
  font-size: 16px;
  font-weight: 500;
  font-family: var(--title-font);
  font-weight: 600;
  color: var(--title-color);
}
.widget_shopping_cart .cart_list a:not(.remove):hover {
  color: var(--theme-color);
}
.widget_shopping_cart .cart_list a.remove {
  position: absolute;
  top: 50%;
  left: 95%;
  transform: translateY(-50%);
  color: var(--body-color);
}
.widget_shopping_cart .cart_list a.remove:hover {
  color: var(--theme-color);
}
.widget_shopping_cart .cart_list img {
  width: 75px;
  height: 75px;
  position: absolute;
  left: 0;
  top: 18px;
}
.widget_shopping_cart .quantity {
  display: inline-flex;
  white-space: nowrap;
  vertical-align: top;
  margin-right: 20px;
  font-size: 14px;
  font-weight: 500;
}
.widget_shopping_cart .total {
  margin-top: 20px;
  font-size: 18px;
  color: var(--title-color);
  font-family: var(--body-font);
}
.widget_shopping_cart .total strong {
  font-family: var(--title-font);
}
.widget_shopping_cart .amount {
  padding-left: 5px;
}
.widget_shopping_cart .th-btn {
  margin-right: 15px;
}
.widget_shopping_cart .th-btn:last-child {
  margin-right: 0;
}

.widget_banner {
  --widget-padding-y: 68px;
  /* Large devices */
}
.widget_banner .title {
  font-size: 24px;
  font-weight: 600;
  line-height: 1.333;
  color: var(--white-color);
  margin-bottom: 24px;
  margin-top: -0.3em;
}
.widget_banner .logo {
  background: var(--white-color);
  padding: 13px 21px 13px 9px;
  display: inline-block;
  border-radius: 48px;
  margin-bottom: 25px;
}
.widget_banner .subtitle {
  color: var(--white-color);
  font-size: 20px;
  font-weight: 600;
  margin-bottom: 6px;
}
.widget_banner .link {
  font-size: 18px;
  font-weight: 500;
  font-family: var(--title-font);
  color: var(--white-color);
  margin-bottom: 15px;
  display: block;
}
.widget_banner .link:hover {
  color: var(--theme-color);
}
.widget_banner .th-btn {
  padding: 20px 48px;
}
@media (max-width: 1199px) {
  .widget_banner .subtitle {
    font-size: 20px;
    line-height: normal;
  }
  .widget_banner .title {
    font-size: 20px;
    line-height: 1.5;
  }
}

.th-video-widget .video-thumb {
  position: relative;
}
.th-video-widget .play-btn {
  --icon-size: 60px;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}
.th-video-widget .play-btn i {
  font-size: 16px;
}
.th-video-widget .video-thumb-title {
  font-size: 20px;
  font-weight: 600;
  line-height: 1.4;
  margin-top: 10px;
  margin-bottom: -0.3em;
}

.widget_recent_entries ul {
  margin: -0.3em 0 0 0;
  padding: 0;
  list-style: none;
}
.widget_recent_entries ul li > a {
  color: var(--body-color);
  font-weight: 500;
  display: inline-block;
}
.widget_recent_entries ul li > a:hover {
  color: var(--theme-color);
}
.widget_recent_entries ul li span.post-date {
  font-size: 14px;
}
.widget_recent_entries ul li:not(:last-child) {
  border-bottom: 1px solid #dadada;
  padding-bottom: 12px;
  margin-bottom: 12px;
}

.widget_recent_comments ul,
.wp-block-latest-comments ul {
  list-style-type: none;
  padding-left: 0;
}

ul.widget_recent_comments,
ol.widget_recent_comments,
.wp-block-latest-comments {
  margin-top: -0.11em;
  padding-left: 0;
}

.widget_recent_comments ol,
.widget_recent_comments ul,
.wp-block-latest-comments ol,
.wp-block-latest-comments ul {
  margin-bottom: 0;
}
.widget_recent_comments li,
.wp-block-latest-comments li {
  margin-bottom: 0;
  color: var(--body-color);
  padding-left: 30px;
  position: relative;
}
.widget_recent_comments li:before,
.wp-block-latest-comments li:before {
  content: "\f086";
  position: absolute;
  left: 0;
  top: -1px;
  color: var(--theme-color);
  font-family: var(--icon-font);
}
.widget_recent_comments.has-avatars li,
.wp-block-latest-comments.has-avatars li {
  padding-left: 0;
  padding-bottom: 0 !important;
}
.widget_recent_comments.has-avatars li:before,
.wp-block-latest-comments.has-avatars li:before {
  display: none;
}
.widget_recent_comments .avatar,
.wp-block-latest-comments .avatar {
  margin-top: 0.4em;
}
.widget_recent_comments li:not(:last-child),
.wp-block-latest-comments li:not(:last-child) {
  padding-bottom: 12px;
}
.widget_recent_comments article,
.wp-block-latest-comments article {
  line-height: 1.5;
}
.widget_recent_comments a,
.wp-block-latest-comments a {
  color: inherit;
}
.widget_recent_comments a:hover,
.wp-block-latest-comments a:hover {
  color: var(--theme-color);
}

.wp-block-latest-comments__comment {
  line-height: 1.6;
}
.wp-block-latest-comments__comment a {
  color: var(--body-color);
}
.wp-block-latest-comments__comment a:hover {
  color: var(--theme-color);
}
.wp-block-latest-comments__comment:last-child {
  margin-bottom: 0;
}

.wp-block-calendar tbody td,
.wp-block-calendar th {
  padding: 10px;
}

.wp-block-calendar,
.calendar_wrap {
  position: relative;
  background-color: #fff;
  padding-bottom: 0;
  border: none;
}
.wp-block-calendar span[class*=wp-calendar-nav],
.calendar_wrap span[class*=wp-calendar-nav] {
  position: absolute;
  top: 9px;
  left: 20px;
  font-size: 14px;
  color: var(--white-color);
  font-weight: 400;
  z-index: 1;
  line-height: 1.7;
}
.wp-block-calendar span[class*=wp-calendar-nav] a,
.calendar_wrap span[class*=wp-calendar-nav] a {
  color: inherit;
}
.wp-block-calendar span.wp-calendar-nav-next,
.calendar_wrap span.wp-calendar-nav-next {
  left: auto;
  right: 20px;
}
.wp-block-calendar caption,
.calendar_wrap caption {
  caption-side: top;
  text-align: center;
  color: var(--white-color);
  background-color: var(--theme-color);
}
.wp-block-calendar th,
.calendar_wrap th {
  font-size: 14px;
  padding: 5px 5px;
  border: none;
  text-align: center;
  border-right: 1px solid #fff;
  color: var(--title-color);
  font-weight: 500;
}
.wp-block-calendar th:first-child,
.calendar_wrap th:first-child {
  border-left: 1px solid #eee;
}
.wp-block-calendar th:last-child,
.calendar_wrap th:last-child {
  border-right: 1px solid #eee;
}
.wp-block-calendar table th,
.calendar_wrap table th {
  font-weight: 500;
}
.wp-block-calendar td,
.calendar_wrap td {
  font-size: 14px;
  padding: 5px 5px;
  color: #01133c;
  border: 1px solid #eee;
  text-align: center;
  background-color: transparent;
  transition: all ease 0.4s;
}
.wp-block-calendar #today,
.calendar_wrap #today {
  color: var(--theme-color);
  background-color: var(--white-color);
  border-color: #ededed;
}
.wp-block-calendar thead,
.calendar_wrap thead {
  background-color: #fff;
}
.wp-block-calendar .wp-calendar-table,
.calendar_wrap .wp-calendar-table {
  margin-bottom: 0;
}
.wp-block-calendar .wp-calendar-nav .pad,
.calendar_wrap .wp-calendar-nav .pad {
  display: none;
}
.wp-block-calendar a,
.calendar_wrap a {
  color: inherit;
  text-decoration: none;
}
.wp-block-calendar a:hover,
.calendar_wrap a:hover {
  color: var(--title-color);
}

.wp-block-calendar {
  border: none;
  padding-bottom: 0;
}
.wp-block-calendar table caption {
  color: var(--white-color);
}

ul.widget_rss, ul.wp-block-rss,
ol.widget_rss,
ol.wp-block-rss {
  padding-left: 0;
}

.widget_rss,
.wp-block-rss {
  list-style-type: none;
}
.widget_rss ul,
.wp-block-rss ul {
  margin: -0.2em 0 -0.5em 0;
  padding: 0;
  list-style: none;
}
.widget_rss ul .rsswidget,
.wp-block-rss ul .rsswidget {
  color: var(--title-color);
  font-family: var(--theme-font);
  font-size: 18px;
  display: block;
  margin-bottom: 10px;
}
.widget_rss ul .rssSummary,
.wp-block-rss ul .rssSummary {
  font-size: 14px;
  margin-bottom: 7px;
  line-height: 1.5;
}
.widget_rss ul a,
.wp-block-rss ul a {
  display: block;
  font-weight: 600;
  color: inherit;
}
.widget_rss ul a:hover,
.wp-block-rss ul a:hover {
  color: var(--theme-color);
}
.widget_rss ul .rss-date,
.wp-block-rss ul .rss-date {
  font-size: 14px;
  display: inline-block;
  margin-bottom: 5px;
  font-weight: 400;
  color: var(--title-color);
}
.widget_rss ul .rss-date:before,
.wp-block-rss ul .rss-date:before {
  content: "\f073";
  font-family: var(--icon-font);
  margin-right: 10px;
  font-weight: 300;
  color: var(--theme-color);
}
.widget_rss ul cite,
.wp-block-rss ul cite {
  font-weight: 500;
  color: var(--title-color);
  font-family: var(--body-font);
  font-size: 14px;
}
.widget_rss ul cite:before,
.wp-block-rss ul cite:before {
  content: "";
  position: relative;
  top: -1px;
  left: 0;
  width: 20px;
  height: 2px;
  display: inline-block;
  vertical-align: middle;
  margin-right: 8px;
  background-color: var(--theme-color);
}
.widget_rss li:not(:last-child),
.wp-block-rss li:not(:last-child) {
  margin-bottom: 16px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
  padding-bottom: 16px;
}
.widget_rss a:hover,
.wp-block-rss a:hover {
  color: var(--theme-color);
}

.textwidget {
  margin-top: -0.1em;
}

.sidebar-area .widget_shopping_cart .th-btn {
  margin-right: 10px;
  padding: 8px 22px;
  font-size: 14px;
}

.widget:has(.author-widget-wrap) {
  padding: 0;
  overflow: hidden;
}

.author-widget-wrap {
  text-align: center;
  position: relative;
  padding: var(--widget-padding-y, 40px) var(--widget-padding-x, 40px);
  /* Large devices */
}
.author-widget-wrap .author-tag {
  background: var(--theme-color);
  position: absolute;
  left: 120px;
  top: -40px;
  font-size: 24px;
  font-weight: 800;
  color: var(--white-color);
  transform: rotate(-45deg) translate(-100%, 0);
  transform-origin: top left;
  padding: 15px 57px;
}
.author-widget-wrap .avater {
  display: inline-block;
}
.author-widget-wrap .avater img {
  border-radius: 50%;
  width: 120px;
  height: 120px;
}
.author-widget-wrap .name {
  font-size: 30px;
  font-weight: 700;
  margin-top: 22px;
  margin-bottom: 10px;
}
.author-widget-wrap .meta {
  display: block;
  margin-bottom: 4px;
}
.author-widget-wrap .meta a {
  color: var(--body-color);
}
.author-widget-wrap .meta a:hover {
  color: var(--theme-color);
}
.author-widget-wrap .meta:last-child {
  margin-bottom: -0.3em;
}
.author-widget-wrap .meta i {
  color: var(--theme-color2);
  margin-right: 6px;
}
@media (max-width: 1199px) {
  .author-widget-wrap .name {
    font-size: 22px;
  }
  .author-widget-wrap .author-tag {
    font-size: 18px;
    left: 100px;
  }
}

.widget_schedule {
  background: var(--smoke-color);
  border-radius: 8px;
  border: 0;
}
.widget_schedule .widget_title {
  padding-bottom: 0;
  border-bottom: 0;
  margin-bottom: 20px;
}
.widget_schedule .widget_title:after {
  display: none;
}
.widget_schedule .checklist {
  margin-top: 38px;
  margin-bottom: 20px;
}
.widget_schedule .th-btn {
  margin-top: 27px;
}

.widget_banner2 {
  /* Extra small devices */
}
.widget_banner2 .widget-banner {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 8px;
  padding: 24px 0;
}
.widget_banner2 .widget-banner .icon-btn {
  --btn-size: 64px;
  background: var(--theme-color);
}
.widget_banner2 .widget-banner .box-title {
  font-size: 18px;
  font-weight: 500;
  color: var(--white-color);
  margin-bottom: 10px;
}
.widget_banner2 .widget-banner .box-link {
  font-size: 28px;
  font-weight: 600;
  color: var(--white-color);
  margin-bottom: -0.1em;
  display: block;
}
@media (max-width: 375px) {
  .widget_banner2 .widget-banner {
    display: block;
    padding: 10px 0;
  }
  .widget_banner2 .widget-banner .icon-btn {
    margin-bottom: 20px;
  }
}

.widget_team_info {
  border: 0;
  padding: 0 !important;
  border-radius: 16px;
  overflow: hidden;
  background: var(--smoke-color);
}
.widget_team_info .widget-thumb img {
  width: 100%;
}
.widget_team_info .widget-info-wrap {
  background: var(--title-color);
  border-radius: 3px;
  padding: 25px 48px;
}
.widget_team_info .widget-info-wrap .box-title {
  color: var(--white-color);
  font-size: 28px;
  font-weight: 600;
  margin-bottom: -0.1em;
}
.widget_team_info .widget-info-wrap .box-text {
  color: var(--white-color);
  font-size: 18px;
  font-weight: 400;
}

.widget-contact-wrap {
  padding: 56px;
  position: relative;
  z-index: 1;
  /* Medium Large devices */
  /* Medium Large devices */
  /* Extra small devices */
}
.widget-contact-wrap .widget-logo {
  margin-bottom: 120px;
}
.widget-contact-wrap .widget_title {
  border: 0;
  color: var(--white-color);
  font-size: 40px;
  font-weight: 600;
  padding-bottom: 0;
  margin-bottom: 58px;
}
.widget-contact-wrap .widget_title:after {
  display: none;
}
.widget-contact-wrap .widget-contact-list {
  list-style: none;
  padding: 0;
  margin: 0;
}
.widget-contact-wrap .widget-contact-list li {
  display: flex;
  gap: 24px;
  align-items: center;
}
.widget-contact-wrap .widget-contact-list li:not(:last-child) {
  margin-bottom: 48px;
}
.widget-contact-wrap .widget-contact-list li .box-icon {
  flex: none;
  width: 70px;
  height: 70px;
  line-height: 70px;
  border: 1px solid var(--light-color);
  border-radius: 50%;
  color: var(--theme-color);
  text-align: center;
  font-size: 30px;
}
.widget-contact-wrap .widget-contact-list li .box-title {
  font-size: 24px;
  font-weight: 600;
  color: var(--white-color);
  margin-bottom: 3px;
}
.widget-contact-wrap .widget-contact-list li .box-text {
  color: var(--light-color);
}
@media (max-width: 1399px) {
  .widget-contact-wrap .widget-logo {
    margin-bottom: 60px;
  }
}
@media (max-width: 1299px) {
  .widget-contact-wrap {
    padding: 40px 30px;
  }
  .widget-contact-wrap .widget-logo {
    margin-bottom: 40px;
  }
  .widget-contact-wrap .widget_title {
    margin-bottom: 30px;
    font-size: 24px;
  }
  .widget-contact-wrap .widget-contact-list li .box-icon {
    width: 56px;
    height: 56px;
    line-height: 56px;
    font-size: 20px;
  }
}
@media (max-width: 375px) {
  .widget-contact-wrap .widget-contact-list li {
    display: block;
  }
  .widget-contact-wrap .widget-contact-list li .box-icon {
    margin-bottom: 20px;
  }
}

@media (min-width: 1200px) {
  .sidebar-shop .widget {
    padding: 30px;
  }
}
@media (min-width: 992px) {
  .sidebar-shop .widget {
    margin-bottom: 24px;
  }
}
/* Large devices */
@media (max-width: 1199px) {
  .widget {
    --widget-padding-y: 30px;
    --widget-padding-x: 30px;
  }
  .widget_title {
    font-size: 22px;
    margin: -0.12em 0 28px 0;
  }
}
/* Medium devices */
@media (max-width: 991px) {
  .sidebar-area {
    padding-top: 30px;
  }
  .widget {
    --widget-padding-y: 40px;
    --widget-padding-x: 40px;
  }
}
/* Small devices */
@media (max-width: 767px) {
  .widget {
    padding: 30px;
  }
}
/* Extra small devices */
@media (max-width: 375px) {
  .recent-post .post-title {
    font-size: 16px;
    line-height: 24px;
  }
  .recent-post .recent-post-meta a {
    font-size: 12px;
  }
}
@media (max-width: 330px) {
  .recent-post .media-img {
    margin-right: 15px;
  }
  .recent-post .post-title {
    font-size: 14px;
    line-height: 24px;
  }
}
.footer-widget {
  margin-bottom: 50px;
}
.footer-widget,
.footer-widget .widget {
  padding: 0;
  border: none;
  padding-bottom: 0;
  background-color: transparent;
  box-shadow: none;
}
.footer-widget .form-group > i {
  color: var(--theme-color);
  top: 18px;
}
.footer-widget .widget_title {
  max-width: 270px;
  color: var(--white-color);
  font-weight: 600;
  font-size: 24px;
  text-transform: uppercase;
  margin: -0.12em 0 30px 0;
  padding: 0 0 0 11px;
  border: 0;
}
.footer-widget .widget_title:before {
  display: none;
}
.footer-widget .widget_title:after {
  content: "";
  position: absolute;
  left: 0;
  top: 3px;
  height: 17px;
  width: 3px;
  background-color: var(--theme-color);
  opacity: 1;
  z-index: 1;
}
.footer-widget .widget_title img {
  margin: -7px 10px 0 0;
}
.footer-widget.widget_meta ul, .footer-widget.widget_pages ul, .footer-widget.widget_archive ul, .footer-widget.widget_categories ul, .footer-widget.widget_nav_menu ul {
  margin-top: -4px;
}
.footer-widget.widget_meta .menu,
.footer-widget.widget_meta > ul, .footer-widget.widget_pages .menu,
.footer-widget.widget_pages > ul, .footer-widget.widget_archive .menu,
.footer-widget.widget_archive > ul, .footer-widget.widget_categories .menu,
.footer-widget.widget_categories > ul, .footer-widget.widget_nav_menu .menu,
.footer-widget.widget_nav_menu > ul {
  margin-bottom: -4px;
}
.footer-widget.widget_meta a, .footer-widget.widget_pages a, .footer-widget.widget_archive a, .footer-widget.widget_categories a, .footer-widget.widget_nav_menu a {
  font-size: 16px;
  font-weight: 400;
  padding: 0 0 0 16px;
  margin-bottom: 14px;
  font-family: var(--body-font);
  display: block;
  max-width: 100%;
  width: max-content;
  padding-right: 0;
  background-color: transparent;
  border: none;
  position: relative;
  box-shadow: none;
  color: var(--body-color);
}
.footer-widget.widget_meta a:before, .footer-widget.widget_pages a:before, .footer-widget.widget_archive a:before, .footer-widget.widget_categories a:before, .footer-widget.widget_nav_menu a:before {
  content: "\f105";
  position: absolute;
  font-weight: 400;
  font-family: var(--icon-font);
  left: 0;
  top: 0;
  opacity: 1;
  transform: translateY(0);
  font-size: 16px;
  background-color: transparent;
  border: none;
  color: var(--body-color);
  transition: 0.4s;
}
.footer-widget.widget_meta a:after, .footer-widget.widget_pages a:after, .footer-widget.widget_archive a:after, .footer-widget.widget_categories a:after, .footer-widget.widget_nav_menu a:after {
  display: none;
}
.footer-widget.widget_meta a:hover, .footer-widget.widget_pages a:hover, .footer-widget.widget_archive a:hover, .footer-widget.widget_categories a:hover, .footer-widget.widget_nav_menu a:hover {
  background-color: transparent;
  color: var(--theme-color);
}
.footer-widget.widget_meta a:hover:before, .footer-widget.widget_pages a:hover:before, .footer-widget.widget_archive a:hover:before, .footer-widget.widget_categories a:hover:before, .footer-widget.widget_nav_menu a:hover:before {
  color: var(--theme-color);
  left: 2px;
}
.footer-widget.widget_meta li, .footer-widget.widget_pages li, .footer-widget.widget_archive li, .footer-widget.widget_categories li, .footer-widget.widget_nav_menu li {
  border: 0;
  padding-bottom: 0;
  margin-bottom: 0;
}
.footer-widget.widget_meta li > span, .footer-widget.widget_pages li > span, .footer-widget.widget_archive li > span, .footer-widget.widget_categories li > span, .footer-widget.widget_nav_menu li > span {
  width: auto;
  height: auto;
  position: relative;
  background-color: transparent;
  color: var(--body-color);
  line-height: 1;
}
.footer-widget.widget_meta li:last-child a, .footer-widget.widget_pages li:last-child a, .footer-widget.widget_archive li:last-child a, .footer-widget.widget_categories li:last-child a, .footer-widget.widget_nav_menu li:last-child a {
  margin-bottom: 0;
}
.footer-widget .recent-post {
  max-width: 300px;
  margin-bottom: 20px;
}
.footer-widget .recent-post .media-img {
  max-width: 80px;
}
.footer-widget .recent-post .media-img:after {
  line-height: 74px;
}
.footer-widget .recent-post .post-title {
  color: var(--white-color);
}
.footer-widget .recent-post:last-child {
  margin-bottom: 0;
  padding-bottom: 0;
  border-bottom: 0;
}
.footer-widget .recent-post .recent-post-meta {
  margin-top: -0.4em;
}
.footer-widget .recent-post .recent-post-meta a {
  font-weight: 400;
  line-height: 1.2;
}
.footer-widget .recent-post .recent-post-meta i {
  color: var(--theme-color);
}
.footer-widget .recent-post .recent-post-meta a:hover i {
  color: var(--theme-color);
}

.about-logo {
  margin-bottom: 15px;
}

.th-widget-about {
  max-width: 353px;
}
.th-widget-about .about-logo {
  margin-bottom: 30px;
}
.th-widget-about .about-text {
  margin-bottom: 62px;
  margin-top: -0.5em;
}

.th-newsletter-widget {
  max-width: 290px;
}
.th-newsletter-widget .newsletter-form {
  margin-top: 40px;
  position: relative;
  display: block;
}
.th-newsletter-widget .newsletter-form .form-group input {
  backdrop-filter: none;
  font-size: 14px;
  height: 50px;
  padding: 0 20px;
  border: 1px solid var(--body-color);
  border-radius: 50px;
}
.th-newsletter-widget .newsletter-form .form-group input::placeholder {
  color: var(--body-color);
}
.th-newsletter-widget .newsletter-form .form-group:after {
  display: none;
}
.th-newsletter-widget .newsletter-form .th-btn {
  min-width: auto;
  font-size: 16px;
  font-weight: 400;
  display: flex;
  align-items: center;
  margin-top: 16px;
  width: 100%;
  padding: 20px 38px;
}
.th-newsletter-widget .form-group {
  margin-bottom: 0;
}
.th-newsletter-widget .check-group {
  margin-bottom: 20px;
}
.th-newsletter-widget.style2 .newsletter-form .form-group input {
  border-radius: 8px;
}
.th-newsletter-widget.style2 .newsletter-form .th-btn {
  border-radius: 8px;
}
.th-newsletter-widget.style3 .newsletter-form .form-group input {
  border-radius: 0px;
}

.th-widget-instagram .instagram-feeds {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12px;
  max-width: 312px;
}
.th-widget-instagram .insta-thumb {
  overflow: hidden;
  position: relative;
  border-radius: 0px;
}
.th-widget-instagram .insta-thumb:before {
  content: "";
  height: calc(100% - 14px);
  width: calc(100% - 14px);
  background-color: var(--title-color);
  opacity: 0.8;
  position: absolute;
  top: 7px;
  left: 7px;
  transform: scaleX(0);
  border-radius: inherit;
  transition: 0.4s ease-in-out;
}
.th-widget-instagram .insta-thumb .insta-btn {
  position: absolute;
  top: 50%;
  left: 50%;
  color: var(--white-color);
  visibility: hidden;
  opacity: 0;
  transform: translate(-50%, 20px);
  background: transparent;
}
.th-widget-instagram .insta-thumb .insta-btn:hover {
  color: var(--theme-color);
}
.th-widget-instagram .insta-thumb:hover:before {
  transform: scaleX(1);
}
.th-widget-instagram .insta-thumb:hover .insta-btn {
  transform: translate(-50%, -50%);
  opacity: 1;
  visibility: visible;
}

.footer-text {
  margin-top: -0.5em;
  margin-bottom: 25px;
}

.social-box {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}
.social-box .social-title {
  font-size: 20px;
  color: var(--white-color);
  font-weight: 600;
  margin-right: 20px;
  margin-bottom: 0;
}

.icon-group a {
  color: var(--white-color);
  font-size: 18px;
  margin-right: 17px;
}
.icon-group a:last-child {
  margin-right: 0;
}

/* Large devices */
@media (max-width: 1199px) {
  .footer-widget.widget_meta a, .footer-widget.widget_pages a, .footer-widget.widget_archive a, .footer-widget.widget_categories a, .footer-widget.widget_nav_menu a {
    margin-bottom: 16px;
  }
}
/* Small devices */
@media (max-width: 767px) {
  .footer-widget .widget_title {
    margin-bottom: 35px;
  }
  .th-widget-about .about-text {
    margin-bottom: 20px;
  }
  .social-box.mb-30 {
    margin-bottom: 25px;
  }
}
/*------------------- 4.2. Header  -------------------*/
.th-header {
  position: relative;
  z-index: 41;
}
.th-header .icon-btn {
  border-radius: 99px;
}
.th-header .menu-area {
  position: relative;
  z-index: 2;
}

.sticky-wrapper {
  transition: 0.4s ease-in-out;
}
.sticky-wrapper.sticky {
  position: fixed;
  top: 0;
  right: 0;
  left: 0;
  background-color: var(--white-color);
  filter: drop-shadow(0 0 10px rgba(0, 0, 0, 0.07));
  animation: stickyAni 0.4s ease-in-out;
}

@keyframes stickyAni {
  0% {
    transform: translate3d(0, -40px, 0) scaleY(0.8);
    opacity: 0.7;
  }
  100% {
    transform: translate3d(0, 0, 0) scaleY(1);
    opacity: 1;
  }
}
.main-menu a {
  display: block;
  position: relative;
  font-weight: 400;
  font-size: 16px;
  font-family: var(--body-font);
  color: var(--title-color);
}
.main-menu a:hover {
  color: var(--theme-color);
}
.main-menu > ul > li {
  margin: 0 15px;
}
.main-menu > ul > li > a {
  padding: 41.5px 0;
}
.main-menu > ul > li > a:hover {
  color: var(--theme-color);
}
.main-menu ul {
  margin: 0;
  padding: 0;
}
.main-menu ul li {
  list-style-type: none;
  display: inline-block;
  position: relative;
}
.main-menu ul li:has(.sub-menu) > a:after, .main-menu ul li:has(.mega-menu) > a:after, .main-menu ul li.menu-item-has-children > a:after {
  content: "\f107";
  display: inline-block;
  position: relative;
  font-family: var(--icon-font);
  margin-left: 5px;
  font-weight: 600;
  top: 0;
  font-size: 0.9em;
  color: var(--title-color);
  transition: 0.3s ease-in-out;
}
.main-menu ul li:has(.sub-menu) > a:hover:after, .main-menu ul li:has(.mega-menu) > a:hover:after, .main-menu ul li.menu-item-has-children > a:hover:after {
  content: "\f106";
  transform: rotate(180deg);
  color: var(--theme-color);
}
.main-menu ul li:last-child {
  margin-right: 0 !important;
}
.main-menu ul li:first-child {
  margin-left: 0 !important;
}
.main-menu ul li:hover > ul.sub-menu {
  visibility: visible;
  opacity: 1;
  transform: scaleY(1);
  z-index: 9;
}
.main-menu ul li:hover ul.mega-menu {
  visibility: visible;
  opacity: 1;
  transform: scaleY(1) translateX(0%);
  z-index: 9;
}
.main-menu ul.sub-menu {
  position: absolute;
  text-align: left;
  top: 100%;
  left: 0;
  background-color: var(--white-color);
  visibility: hidden;
  min-width: 230px;
  width: max-content;
  padding: 7px;
  left: -14px;
  opacity: 0;
  z-index: -1;
  box-shadow: 0px 4px 15px rgba(1, 15, 28, 0.06);
  border-radius: 10px;
  transform: scaleY(0);
  transform-origin: top center;
  transition: all 0.4s ease 0s;
  border-bottom: 3px solid var(--theme-color);
}
.main-menu ul.sub-menu a {
  font-size: 16px;
  line-height: 30px;
}
.main-menu ul.sub-menu {
  padding: 18px 20px 18px 18px;
  left: -27px;
}
.main-menu ul.sub-menu li {
  display: block;
  margin: 0 0;
  padding: 0px 9px;
}
.main-menu ul.sub-menu li:has(.sub-menu) > a:after, .main-menu ul.sub-menu li:has(.mega-menu) > a:after, .main-menu ul.sub-menu li.menu-item-has-children > a:after {
  content: "\f105";
  float: right;
  top: 1px;
  display: inline-block;
}
.main-menu ul.sub-menu li:has(.sub-menu) > a:hover:after, .main-menu ul.sub-menu li:has(.mega-menu) > a:hover:after, .main-menu ul.sub-menu li.menu-item-has-children > a:hover:after {
  content: "\f105";
  transform: rotate(180deg);
}
.main-menu ul.sub-menu li a {
  position: relative;
  padding-left: 0;
  text-transform: capitalize;
}
.main-menu ul.sub-menu li a:before {
  content: "\f2dc";
  position: absolute;
  top: 6px;
  left: 10px;
  font-family: var(--icon-font);
  width: 11px;
  height: 11px;
  text-align: center;
  border-radius: 50%;
  display: inline-block;
  font-size: 1em;
  line-height: 1;
  color: var(--theme-color);
  font-weight: 400;
  opacity: 0;
  visibility: visible;
  transition: 0.3s ease-in-out;
}
.main-menu ul.sub-menu li a:hover {
  padding-left: 23px;
}
.main-menu ul.sub-menu li a:hover:before {
  visibility: visible;
  opacity: 1;
  left: 0;
}
.main-menu ul.sub-menu li ul.sub-menu {
  left: 100%;
  right: auto;
  top: 0;
  margin: 0 0;
  margin-left: 20px;
}
.main-menu ul.sub-menu li ul.sub-menu li ul {
  left: 100%;
  right: auto;
}
.main-menu .mega-menu-wrap {
  position: static;
}

@media (max-width: 1500px) {
  .main-menu > ul > li {
    margin: 0 13px;
  }
}
.menu-style1 > ul > li {
  margin: 0 14px;
  /* Medium Large devices */
}
@media (max-width: 1299px) {
  .menu-style1 > ul > li {
    margin: 0 10px;
  }
}
.menu-style1 > ul > li > a {
  padding: 17px 0;
  color: var(--white-color);
}
.menu-style1 > ul > li > a:hover {
  color: var(--theme-color2);
}
.menu-style1 ul li:has(.sub-menu) > a:after,
.menu-style1 ul li:has(.mega-menu) > a:after,
.menu-style1 ul li.menu-item-has-children > a:after {
  color: var(--white-color);
}

.simple-icon {
  border: none;
  background-color: transparent;
  padding: 0;
  font-size: 24px;
  position: relative;
}
.simple-icon:has(.badge) {
  padding-right: 8px;
}
.simple-icon .badge {
  top: -8px;
  right: 0;
  font-size: 12px;
}

.header-button {
  height: 100%;
  display: flex;
  align-items: center;
  gap: 15px;
}
.header-button .icon-btn .badge {
  font-size: 12px;
  top: 0;
  right: 0;
  background: var(--theme-color2);
  color: var(--title-color);
}
.header-button .icon-btn:hover .badge {
  background: var(--theme-color);
  color: var(--white-color);
}
.header-button .th-btn {
  padding: 21px 48px;
}

.social-links .social-title {
  font-weight: 500;
  font-size: 16px;
  display: inline-block;
  margin: 0 10px 0 0;
  color: var(--body-color);
}
.social-links a {
  font-size: 14px;
  display: inline-block;
  color: var(--body-color);
  margin: 0 15px 0 0;
}
.social-links a:last-child {
  margin-right: 0 !important;
}
.social-links a:hover {
  color: var(--theme-color);
}

.header-logo {
  padding-top: 15px;
  padding-bottom: 15px;
}
.header-logo a:hover {
  color: var(--white-color);
}

.header-links {
  /* Extra small devices */
}
.header-links ul {
  margin: 0;
  padding: 0;
  list-style-type: none;
}
.header-links li {
  display: inline-block;
  position: relative;
  font-size: 14px;
  font-weight: 400;
  font-family: var(--body-font);
  line-height: normal;
}
.header-links li:not(:last-child) {
  margin: 0 45px 0 0;
}
.header-links li:not(:last-child):after {
  content: "";
  height: 14px;
  width: 1px;
  background-color: var(--white-color);
  position: absolute;
  top: 0px;
  right: -25px;
  margin-top: 1px;
}
.header-links li > i {
  margin-right: 10px;
  color: var(--theme-color);
}
.header-links li,
.header-links span,
.header-links p,
.header-links a {
  color: var(--white-color);
}
.header-links a:hover {
  color: var(--theme-color);
}
.header-links b,
.header-links strong {
  font-weight: 600;
  margin-right: 6px;
}
.header-links.social-links li:not(:last-child) {
  margin: 0 35px 0 0;
}
.header-links.social-links li:not(:last-child):after {
  right: -20px;
}
@media (max-width: 375px) {
  .header-links li:not(:last-child) {
    margin: 0 30px 0 0;
  }
  .header-links li:not(:last-child):after {
    right: -18px;
  }
}

.header-top {
  padding: 11px 0;
  background-color: var(--black-color2);
}

.dropdown-link {
  position: relative;
}
.dropdown-link > a {
  color: var(--white-color);
}
.dropdown-link > a i {
  color: var(--theme-color);
  margin-right: 5px;
}

.dropdown-toggle::after {
  content: "\f078";
  border: none;
  font-family: var(--icon-font);
  vertical-align: middle;
  font-weight: 400;
  margin-left: 6px;
  margin-top: -1px;
}

.dropdown-menu {
  width: fit-content;
  min-width: auto;
  top: calc(100% + 14px) !important;
  left: 50% !important;
  transform: translateX(-50%) !important;
  padding: 8px 20px !important;
  text-align: center;
  border-color: var(--th-border-color);
}
.dropdown-menu li {
  padding-right: 0;
  margin-right: 0;
}
.dropdown-menu li:after {
  display: none;
}
.dropdown-menu li a {
  display: block;
  font-weight: 400;
  font-size: 14px;
  line-height: 1.7;
}
.dropdown-menu a {
  color: var(--title-color) !important;
}
.dropdown-menu a:hover {
  color: var(--theme-color) !important;
}
.dropdown-menu:before {
  content: "";
  position: absolute;
  left: 50%;
  top: -7px;
  width: 14px;
  height: 14px;
  margin-left: -7px;
  background-color: var(--white-color);
  z-index: -1;
  transform: rotate(45deg);
  border-top: 1px solid var(--th-border-color);
  border-left: 1px solid var(--th-border-color);
}

.header-icons {
  display: flex;
  display: flex;
  gap: 15px;
}
.header-icons .icon-btn {
  font-size: 18px;
}
.header-icons .icon-btn .badge {
  font-size: 12px;
  top: 0;
  right: 0;
}
.header-icons .icon-btn:hover .badge {
  background-color: var(--title-color);
}

.header-search {
  position: relative;
}
.header-search input {
  height: 46px;
  border: 1px solid var(--th-border-color);
  width: 500px;
  max-width: 100%;
  /* Large devices */
}
.header-search input:focus {
  border-color: var(--theme-color);
}
@media (max-width: 1199px) {
  .header-search input {
    max-width: 350px;
  }
}
.header-search button {
  display: inline-block;
  position: absolute;
  top: 0;
  right: 0;
  border: none;
  background-color: var(--theme-color);
  color: var(--white-color);
  width: 50px;
  height: 46px;
  line-height: 45px;
  text-align: center;
  padding: 0;
  border-radius: 0 99px 99px 0;
}

.menu-expand {
  display: inline-block;
  font-size: 16px;
  font-weight: 700;
  text-transform: uppercase;
  color: var(--white-color);
  background-color: var(--theme-color2);
  padding: 17px 25px;
  width: 100%;
  /* Medium Large devices */
}
@media (max-width: 1299px) {
  .menu-expand {
    font-size: 15px;
  }
}
.menu-expand i {
  margin-right: 15px;
}
.menu-expand:hover {
  color: var(--white-color);
}

/* Header default ---------------------------------- */
.header-default {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
}
.header-default .menu-area {
  background: var(--smoke-color);
  border-radius: 120px;
  padding: 0 24px;
  margin-top: 32px;
}
.header-default .sticky-wrapper.sticky {
  background: var(--smoke-color);
}
.header-default .sticky-wrapper.sticky .menu-area {
  margin-top: 0;
  padding: 0;
}

/* Large devices */
@media (max-width: 1199px) {
  .header-default .menu-area {
    padding: 0 40px 0 30px;
  }
}
/* Medium devices */
@media (max-width: 991px) {
  .header-default .menu-area {
    padding: 0 20px;
  }
}
/* Extra small devices */
@media (max-width: 575px) {
  .header-default .header-logo h2 {
    font-size: 36px;
  }
  .header-default .header-logo img {
    max-width: 160px;
  }
  .header-default .menu-area {
    padding: 0 12px;
    margin-top: 20px;
  }
}
/* Extra small devices */
@media (max-width: 375px) {
  .header-default .menu-area {
    padding: 0 10px;
  }
}
.sidebar-btn {
  /* Medium devices */
}
.sidebar-btn .line {
  display: block;
  height: 2px;
  width: 56px;
  background: var(--title-color);
  transition: 0.4s;
}
.sidebar-btn .line:not(:last-child) {
  margin-bottom: 13px;
}
.sidebar-btn .line:nth-child(2) {
  width: 40px;
}
.sidebar-btn:hover .line:nth-child(2) {
  width: 56px;
}
@media (max-width: 991px) {
  .sidebar-btn .line {
    width: 40px;
  }
  .sidebar-btn .line:nth-child(2) {
    width: 26px;
  }
  .sidebar-btn .line:not(:last-child) {
    margin-bottom: 9px;
  }
  .sidebar-btn:hover .line:nth-child(2) {
    width: 40px;
  }
}
.sidebar-btn:has(.dots) .simple-icon {
  display: flex;
  gap: 12.5px;
  flex-wrap: wrap;
  height: 40px;
  width: 40px;
  align-items: center;
}
.sidebar-btn .dots {
  width: 5px;
  height: 5px;
  background: var(--title-color);
  border-radius: 50%;
  display: flex;
  flex-wrap: wrap;
  gap: 12.5px;
  position: relative;
  transition: 0.4s;
}
.sidebar-btn .dots:after, .sidebar-btn .dots:before {
  content: "";
  position: relative;
  display: inline-block;
  width: 5px;
  height: 5px;
  background: var(--title-color);
  border-radius: 50%;
  transition: 0.4s;
}
.sidebar-btn .dots:before {
  top: -17.5px;
}

/* Header 1 ---------------------------------- */
.header-layout1 {
  background: rgba(198, 201, 212, 0.3);
  border-bottom: 1px solid rgba(198, 201, 212, 0.5);
  /* Extra large devices */
  /* Medium Large devices */
  /* Medium Large devices */
  /* Large devices */
  /* Medium devices */
}
.header-layout1 .sticky-wrapper {
  border: 8px solid var(--white-color);
}
.header-layout1 .sticky-wrapper.sticky {
  border: 0;
}
.header-layout1 .main-menu ul.mega-menu {
  left: -20em !important;
}
.header-layout1 .main-menu > ul > li > a {
  padding: 38.5px 0;
}
.header-layout1 .menu-area {
  padding: 0 12px 0 112px;
}
.header-layout1 .header-button {
  gap: 30px;
}
.header-layout1 .sidebar-btn {
  height: 104px;
  display: inline-flex;
  border-left: 1px solid var(--light-color);
  padding-left: 30px;
  padding-right: 18px;
  align-items: center;
}
@media (max-width: 1500px) {
  .header-layout1 .menu-area {
    padding: 0 12px;
  }
}
@media (max-width: 1399px) {
  .header-layout1 .main-menu ul.mega-menu {
    left: -28rem !important;
  }
}
@media (max-width: 1299px) {
  .header-layout1 .main-menu ul.mega-menu {
    left: -22rem !important;
  }
}
@media (max-width: 1199px) {
  .header-layout1 .menu-area {
    padding: 0 30px;
  }
}
@media (max-width: 991px) {
  .header-layout1 .menu-area {
    padding: 0 12px;
  }
  .header-layout1 .sidebar-btn {
    height: 88px;
    padding: 0 13px 0 25px;
  }
}

/* Extra small devices */
@media (max-width: 575px) {
  .header-layout1 .header-logo {
    margin-right: 0;
  }
  .header-layout1 .header-logo h2 {
    font-size: 36px;
  }
  .header-layout1 .header-logo img {
    max-width: 160px;
  }
}
/* Extra small devices */
@media (max-width: 375px) {
  .header-layout1 .header-button .icon-btn {
    --btn-size: 50px;
  }
}
/* Header 2 ---------------------------------- */
.header-layout2 {
  background: var(--title-color);
  /* Extra large devices */
  /* Medium Large devices */
  /* Medium Large devices */
  /* Large devices */
  /* Medium devices */
}
.header-layout2 .header-links li, .header-layout2 .header-links span, .header-layout2 .header-links p, .header-layout2 .header-links a {
  color: var(--light-color);
}
.header-layout2 .header-links a:hover {
  color: var(--theme-color);
}
.header-layout2 .dropdown-menu {
  background: var(--title-color);
  border-color: var(--light-color);
}
.header-layout2 .dropdown-menu:before {
  background: var(--title-color);
  border-color: var(--light-color);
}
.header-layout2 .dropdown-menu a {
  color: var(--light-color) !important;
}
.header-layout2 .dropdown-menu a:hover {
  color: var(--theme-color) !important;
}
.header-layout2 .main-menu a {
  color: var(--white-color);
}
.header-layout2 .main-menu a:hover {
  color: var(--theme-color);
}
.header-layout2 .main-menu > ul > li > a {
  padding: 41.5px 0;
  color: var(--white-color);
}
.header-layout2 .main-menu > ul > li > a:hover {
  color: var(--theme-color);
}
.header-layout2 .main-menu ul li:has(.sub-menu) > a:after, .header-layout2 .main-menu ul li:has(.mega-menu) > a:after, .header-layout2 .main-menu ul li.menu-item-has-children > a:after {
  color: var(--white-color);
}
.header-layout2 .main-menu ul li:has(.sub-menu) > a:hover:after, .header-layout2 .main-menu ul li:has(.mega-menu) > a:hover:after, .header-layout2 .main-menu ul li.menu-item-has-children > a:hover:after {
  color: var(--theme-color);
}
.header-layout2 .mega-menu-box .mega-menu-img .btn-wrap .th-btn {
  color: var(--title-color);
}
.header-layout2 .mega-menu-box .mega-menu-img .btn-wrap .th-btn:hover {
  color: var(--white-color);
}
.header-layout2 .main-menu ul.sub-menu {
  background: var(--black-color2);
  box-shadow: 0px 4px 15px rgba(255, 255, 255, 0.05);
}
.header-layout2 .menu-area {
  padding: 0 12px 0 112px;
}
.header-layout2 .header-button {
  gap: 30px;
}
.header-layout2 .header-button .th-btn {
  padding: 19px 32px;
}
.header-layout2 .sidebar-btn {
  height: 110px;
  display: inline-flex;
  padding-left: 40px;
  padding-right: 40px;
  margin-right: -12px;
  align-items: center;
  background: var(--theme-color);
  transition: 0.4s;
}
.header-layout2 .sidebar-btn:hover {
  background: var(--white-color);
}
.header-layout2 .sticky-wrapper.sticky {
  background: var(--title-color);
  filter: drop-shadow(0 0 10px rgba(255, 255, 255, 0.07));
}
@media (max-width: 1500px) {
  .header-layout2 .menu-area {
    padding: 0 12px;
  }
}
@media (max-width: 1399px) {
  .header-layout2 .main-menu ul.mega-menu {
    left: -28rem !important;
  }
}
@media (max-width: 1299px) {
  .header-layout2 .main-menu ul.mega-menu {
    left: -22rem !important;
  }
}
@media (max-width: 1199px) {
  .header-layout2 .menu-area {
    padding: 0 30px 0 12px;
  }
}
@media (max-width: 991px) {
  .header-layout2 .menu-area {
    padding: 0 12px;
  }
  .header-layout2 .sidebar-btn {
    height: 90px;
    padding: 0 25px;
  }
}

/* Extra small devices */
@media (max-width: 575px) {
  .header-layout2 .header-logo {
    margin-right: 0;
  }
  .header-layout2 .header-logo h2 {
    font-size: 36px;
  }
  .header-layout2 .header-logo img {
    max-width: 160px;
  }
}
/* Header 3 ---------------------------------- */
.header-layout3 {
  background: var(--title-color);
  /* Extra large devices */
  /* Medium Large devices */
  /* Medium Large devices */
  /* Large devices */
  /* Medium devices */
}
.header-layout3 .header-top {
  background-color: transparent;
  border-bottom: 1px solid rgba(87, 88, 95, 0.5);
}
.header-layout3 .header-links li, .header-layout3 .header-links span, .header-layout3 .header-links p, .header-layout3 .header-links a {
  color: var(--light-color);
}
.header-layout3 .header-links a:hover {
  color: var(--theme-color);
}
.header-layout3 .main-menu a {
  color: var(--white-color);
}
.header-layout3 .main-menu a:hover {
  color: var(--theme-color);
}
.header-layout3 .main-menu > ul > li > a {
  padding: 41.5px 0;
  color: var(--white-color);
}
.header-layout3 .main-menu > ul > li > a:hover {
  color: var(--theme-color);
}
.header-layout3 .main-menu ul li:has(.sub-menu) > a:after, .header-layout3 .main-menu ul li:has(.mega-menu) > a:after, .header-layout3 .main-menu ul li.menu-item-has-children > a:after {
  color: var(--white-color);
}
.header-layout3 .main-menu ul li:has(.sub-menu) > a:hover:after, .header-layout3 .main-menu ul li:has(.mega-menu) > a:hover:after, .header-layout3 .main-menu ul li.menu-item-has-children > a:hover:after {
  color: var(--theme-color);
}
.header-layout3 .main-menu ul.sub-menu {
  background: var(--black-color2);
  box-shadow: 0px 4px 15px rgba(255, 255, 255, 0.05);
}
.header-layout3 .menu-area {
  padding: 0 12px 0 112px;
}
.header-layout3 .header-button {
  gap: 30px;
}
.header-layout3 .header-button .th-btn {
  padding: 19px 32px;
}
.header-layout3 .sidebar-btn {
  height: 110px;
  display: inline-flex;
  padding-left: 40px;
  padding-right: 40px;
  margin-right: -12px;
  align-items: center;
  background: var(--theme-color);
  transition: 0.4s;
}
.header-layout3 .sidebar-btn .dots {
  background: var(--white-color);
}
.header-layout3 .sidebar-btn .dots:after, .header-layout3 .sidebar-btn .dots:before {
  background: var(--white-color);
}
.header-layout3 .sidebar-btn:hover {
  background: var(--white-color);
}
.header-layout3 .sidebar-btn:hover .dots {
  background: var(--theme-color);
}
.header-layout3 .sidebar-btn:hover .dots:after, .header-layout3 .sidebar-btn:hover .dots:before {
  background: var(--theme-color);
}
.header-layout3 .sticky-wrapper.sticky {
  background: var(--title-color);
  filter: drop-shadow(0 0 10px rgba(255, 255, 255, 0.07));
}
@media (max-width: 1500px) {
  .header-layout3 .menu-area {
    padding: 0 12px;
  }
}
@media (max-width: 1399px) {
  .header-layout3 .main-menu ul.mega-menu {
    left: -28rem !important;
  }
}
@media (max-width: 1299px) {
  .header-layout3 .main-menu ul.mega-menu {
    left: -22rem !important;
  }
}
@media (max-width: 1199px) {
  .header-layout3 .menu-area {
    padding: 0 30px 0 12px;
  }
}
@media (max-width: 991px) {
  .header-layout3 .menu-area {
    padding: 0 12px;
  }
  .header-layout3 .sidebar-btn {
    height: 90px;
    padding: 0 25px;
  }
}

/* Extra small devices */
@media (max-width: 575px) {
  .header-layout3 .header-logo {
    margin-right: 0;
  }
  .header-layout3 .header-logo h2 {
    font-size: 36px;
  }
  .header-layout3 .header-logo img {
    max-width: 160px;
  }
}
/* Header 4 ---------------------------------- */
.header-layout4 {
  background: var(--white-color);
}
.header-layout4 .dropdown-menu {
  background: var(--title-color);
  border-color: var(--light-color);
}
.header-layout4 .dropdown-menu:before {
  background: var(--title-color);
  border-color: var(--light-color);
}
.header-layout4 .dropdown-menu a {
  color: var(--light-color) !important;
}
.header-layout4 .dropdown-menu a:hover {
  color: var(--theme-color) !important;
}
.header-layout4 .main-menu > ul > li > a:hover {
  color: var(--theme-color2);
}
.header-layout4 .main-menu ul li:has(.sub-menu) > a:hover:after, .header-layout4 .main-menu ul li:has(.mega-menu) > a:hover:after, .header-layout4 .main-menu ul li.menu-item-has-children > a:hover:after {
  color: var(--theme-color2);
}
.header-layout4 .mega-menu-box .mega-menu-img .btn-wrap .th-btn {
  color: var(--title-color);
}
.header-layout4 .mega-menu-box .mega-menu-img .btn-wrap .th-btn:hover {
  color: var(--white-color);
}
.header-layout4 .main-menu ul.sub-menu {
  border-color: var(--theme-color2);
}
.header-layout4 .main-menu a:hover {
  color: var(--theme-color2);
}
.header-layout4 .main-menu ul.sub-menu li a:before {
  color: var(--theme-color2);
}
.header-layout4 .icon-btn {
  border-radius: 5px;
}

/* Extra small devices */
@media (max-width: 575px) {
  .header-layout4 .header-logo {
    margin-right: 0;
  }
  .header-layout4 .header-logo h2 {
    font-size: 36px;
  }
  .header-layout4 .header-logo img {
    max-width: 160px;
  }
}
/* Header 5 ---------------------------------- */
.header-layout5 {
  background: var(--smoke-color);
  /* Medium Large devices */
  /* Extra small devices */
}
@media (max-width: 1299px) {
  .header-layout5 .header-button .th-btn {
    display: none;
  }
}
@media (max-width: 575px) {
  .header-layout5 .header-logo {
    margin-right: 0;
  }
  .header-layout5 .header-logo h2 {
    font-size: 36px;
  }
  .header-layout5 .header-logo img {
    max-width: 160px;
  }
}

/*------------------- 4.3. Footer  -------------------*/
.footer-wrapper {
  --border-color: rgba(255, 255, 255, 0.3);
  --body-color: #829592;
  position: relative;
  z-index: 2;
  background-color: var(--black-color2);
  overflow: hidden;
}
.footer-wrapper .social-links a {
  margin: 0 30px 0 0;
}

.widget-area {
  padding-top: 120px;
  padding-bottom: 70px;
}

.sticky-footer {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
}

.footer-links ul {
  padding: 0;
  margin: 0;
}
.footer-links li {
  font-family: var(--body-font);
  display: inline-block;
  margin-right: 20px;
  position: relative;
}
.footer-links li:last-child {
  margin-right: 0;
  padding-right: 0;
}
.footer-links a {
  font-family: inherit;
  color: var(--body-color);
  font-size: 14px;
}
.footer-links a:hover {
  color: var(--theme-color);
}

.copyright-wrap {
  padding: 26.5px 0;
  background-size: 100% auto;
  background-color: var(--title-color);
}
.copyright-wrap .copyright-text {
  color: var(--body-color);
  font-size: 14px;
}
.copyright-wrap .copyright-text a {
  color: var(--theme-color);
}
.copyright-wrap .copyright-text a:hover {
  color: var(--white-color);
}
.copyright-wrap.bg-title .copyright-text a:hover {
  color: var(--theme-color);
}

.subscribe-box {
  position: relative;
  z-index: 1;
  padding: 140px 0 80px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  /* Extra large devices */
  /* Medium Large devices */
  /* Large devices */
  /* Extra small devices */
}
.subscribe-box_title {
  color: var(--white-color);
  font-size: 36px;
  font-weight: 700;
  text-transform: capitalize;
  margin-bottom: 0px;
  margin-top: -0.25em;
}
.subscribe-box_text {
  font-size: 18px;
  font-weight: 600;
  font-family: var(--title-font);
  margin-bottom: -0.45em;
}
@media (max-width: 1500px) {
  .subscribe-box {
    padding: 80px 0;
  }
}
@media (max-width: 1299px) {
  .subscribe-box .subscribe-box_title {
    font-size: 30px;
  }
}
@media (max-width: 1199px) {
  .subscribe-box {
    text-align: center;
  }
}
@media (max-width: 575px) {
  .subscribe-box {
    padding: 60px 0;
  }
  .subscribe-box .subscribe-box_title {
    font-size: 26px;
  }
  .subscribe-box .subscribe-box_text {
    font-size: 16px;
  }
}

.newsletter-form {
  display: flex;
  gap: 10px;
  align-items: center;
}
.newsletter-form .form-group {
  margin-bottom: 0;
  width: 100%;
}
.newsletter-form .th-btn {
  min-width: auto;
  font-size: 20px;
  padding: 18px 38px;
}

.copyright-text {
  margin: 0;
}
.copyright-text a {
  color: var(--theme-color);
}
.copyright-text a:hover {
  color: var(--white-color);
}

/* Medium devices */
@media (max-width: 991px) {
  .newsletter-wrap {
    padding: 40px;
  }
  .newsletter-wrap .newsletter-title {
    font-size: 30px;
  }
  .newsletter-wrap .newsletter-text {
    font-size: 16px;
  }
}
/* Small devices */
@media (max-width: 767px) {
  .newsletter-wrap {
    flex-wrap: wrap;
    justify-content: center;
    gap: 25px;
  }
  .newsletter-wrap .newsletter-title {
    text-align: center;
    margin-left: auto;
    margin-right: auto;
  }
}
/* Extra small devices */
@media (max-width: 575px) {
  .newsletter-wrap {
    padding: 40px 20px;
  }
  .newsletter-wrap .newsletter-title {
    font-size: 24px;
  }
  .newsletter-wrap .newsletter-form {
    flex-wrap: wrap;
    justify-content: center;
  }
  .footer-wrapper .newsletter-form {
    flex-wrap: wrap;
    justify-content: center;
  }
}
/* Medium devices */
@media (max-width: 991px) {
  .footer-wrapper .widget-area {
    padding-top: var(--section-space-mobile);
    padding-bottom: 30px;
  }
  .copyright-text {
    text-align: center;
  }
}
/* footer default ---------------------------------- */
.footer-default {
  --body-color: #C6C9D4;
}
.footer-default .footer-line {
  border-left: 1px solid var(--th-border-color3);
  border-right: 1px solid var(--th-border-color3);
}
.footer-default .footer-top {
  border-bottom: 1px solid var(--th-border-color3);
  padding: 80px 0;
  /* Large devices */
  /* Medium devices */
  /* Small devices */
}
.footer-default .footer-top .client-group-wrap {
  display: flex;
  align-items: center;
  gap: 40px;
}
.footer-default .footer-top .client-group-wrap .title {
  color: var(--white-color);
  font-size: 28px;
  font-weight: 600;
  margin-bottom: 0;
}
.footer-default .footer-top .client-group-wrap .title a {
  color: var(--white-color);
  text-decoration: underline;
  text-underline-offset: 5px;
}
.footer-default .footer-top .client-group-wrap .title a:hover {
  color: var(--theme-color);
}
@media (max-width: 1199px) {
  .footer-default .footer-top {
    text-align: center;
  }
  .footer-default .footer-top .client-group-wrap {
    justify-content: center;
  }
  .footer-default .footer-top .client-group-wrap .title {
    font-size: 24px;
  }
}
@media (max-width: 991px) {
  .footer-default .footer-top .client-group-wrap {
    display: block;
  }
  .footer-default .footer-top .client-group-wrap .title {
    margin-top: 20px;
  }
}
@media (max-width: 767px) {
  .footer-default .footer-top .client-group-wrap .title {
    font-size: 20px;
  }
}

/* footer 1 ---------------------------------- */
.footer-layout1 {
  background: var(--title-color);
  padding-bottom: 80px;
  --body-color: #C6C9D4;
}
.footer-layout1 .footer-top {
  padding: 80px 0;
  /* Large devices */
  /* Medium devices */
}
.footer-layout1 .footer-top .cta-group-wrap {
  display: flex;
  gap: 16px;
}
.footer-layout1 .footer-top .cta-group-wrap .title {
  margin-bottom: 0;
}
.footer-layout1 .footer-top .cta-group-wrap .title a {
  color: transparent;
  -webkit-text-stroke: 1px var(--white-color);
  font-size: 48px;
  font-weight: 700;
  font-family: var(--title-font);
}
.footer-layout1 .footer-top .cta-group-wrap .title a:hover {
  -webkit-text-stroke: 1px var(--theme-color);
}
@media (max-width: 1199px) {
  .footer-layout1 .footer-top {
    text-align: center;
  }
  .footer-layout1 .footer-top .cta-group-wrap {
    justify-content: center;
  }
}
@media (max-width: 991px) {
  .footer-layout1 .footer-top .cta-group-wrap .title a {
    font-size: 40px;
  }
}
.footer-layout1 .footer-bottom {
  background: var(--title-color);
  border-radius: 8px;
  position: relative;
}
.footer-layout1 .footer-bottom:after {
  content: "";
  position: absolute;
  inset: -1px;
  background: linear-gradient(to top, var(--title-color) 10%, var(--white-color));
  border-radius: 8px;
  z-index: -1;
}
.footer-layout1 .widget-area {
  padding-bottom: 70px;
  padding-left: 120px;
  padding-right: 120px;
  /* Medium Large devices */
  /* Medium devices */
  /* Extra small devices */
}
@media (max-width: 1399px) {
  .footer-layout1 .widget-area {
    padding-left: 80px;
    padding-right: 80px;
  }
}
@media (max-width: 991px) {
  .footer-layout1 .widget-area {
    padding-bottom: 30px;
    padding-left: 60px;
    padding-right: 60px;
  }
}
@media (max-width: 575px) {
  .footer-layout1 .widget-area {
    padding-left: 30px;
    padding-right: 30px;
  }
}
.footer-layout1 .footer-text {
  max-width: 670px;
  margin-left: auto;
  margin-right: auto;
  margin-bottom: 35px;
}
.footer-layout1 .widget_title {
  padding-left: 0;
}
.footer-layout1 .widget_title:after {
  display: none;
}
.footer-layout1 .newsletter-widget {
  max-width: 354px;
}
.footer-layout1 .newsletter-widget .newsletter-form {
  margin-bottom: 15px;
}
.footer-layout1 .copyright-wrap {
  border-radius: 0 0 8px 8px;
  padding-left: 120px;
  padding-right: 120px;
  /* Medium Large devices */
  /* Medium devices */
}
@media (max-width: 1399px) {
  .footer-layout1 .copyright-wrap {
    padding-left: 80px;
    padding-right: 80px;
  }
}
@media (max-width: 991px) {
  .footer-layout1 .copyright-wrap {
    padding-left: 20px;
    padding-right: 20px;
  }
}

/* footer 2 ---------------------------------- */
.footer-layout2 {
  overflow: hidden;
  background: var(--title-color);
  --body-color: #57585F;
}
.footer-layout2 .footer-line {
  border-left: 1px solid var(--th-border-color3);
  border-right: 1px solid var(--th-border-color3);
}
.footer-layout2 .footer-top {
  padding: 32px;
}
.footer-layout2 .footer-author {
  display: inline-flex;
  gap: 15px 32px;
  align-items: center;
  flex-wrap: wrap;
  /* Extra small devices */
}
.footer-layout2 .footer-author .author-thumb {
  flex: none;
}
.footer-layout2 .footer-author .author-title {
  margin-bottom: 0;
  font-size: 72px;
  font-weight: 800;
  font-family: var(--title-font);
  /* Small devices */
  /* Extra small devices */
}
.footer-layout2 .footer-author .author-title a {
  -webkit-text-stroke: 1px var(--body-color);
  color: transparent;
}
.footer-layout2 .footer-author .author-title a:hover {
  color: var(--theme-color);
  -webkit-text-stroke: 1px var(--theme-color);
}
@media (max-width: 767px) {
  .footer-layout2 .footer-author .author-title {
    font-size: 60px;
  }
}
@media (max-width: 575px) {
  .footer-layout2 .footer-author .author-title {
    font-size: 40px;
  }
}
@media (max-width: 575px) {
  .footer-layout2 .footer-author {
    justify-content: center;
  }
}
.footer-layout2 .client-group-wrap {
  display: inline-flex;
  gap: 20px 40px;
  flex-wrap: wrap;
  /* Medium devices */
}
.footer-layout2 .client-group-wrap .title {
  color: var(--white-color);
  font-size: 28px;
  font-weight: 600;
  margin-bottom: 0;
  /* Small devices */
}
.footer-layout2 .client-group-wrap .title a {
  color: white;
  text-decoration: underline;
  text-underline-offset: 5px;
}
.footer-layout2 .client-group-wrap .title a:hover {
  color: var(--theme-color);
}
@media (max-width: 767px) {
  .footer-layout2 .client-group-wrap .title {
    font-size: 24px;
  }
}
@media (max-width: 991px) {
  .footer-layout2 .client-group-wrap {
    justify-content: center;
  }
}
.footer-layout2 .footer-middle {
  border-top: 1px solid var(--th-border-color3);
  border-bottom: 1px solid var(--th-border-color3);
  padding: 32px;
}
.footer-layout2 .footer-links {
  display: inline-flex;
  gap: 20px 48px;
  flex-wrap: wrap;
  /* Medium devices */
}
.footer-layout2 .footer-links a {
  color: var(--white-color);
  font-size: 16px;
  font-weight: 400;
}
.footer-layout2 .footer-links a:hover {
  color: var(--theme-color);
}
@media (max-width: 991px) {
  .footer-layout2 .footer-links {
    gap: 20px 30px;
    justify-content: center;
  }
}
.footer-layout2 .copyright-wrap {
  background: transparent;
  padding: 23px 32px;
}
.footer-layout2 .copyright-wrap .copyright-text {
  color: var(--light-color);
  font-size: 16px;
}
.footer-layout2 .copyright-wrap .footer-links a {
  color: var(--light-color);
}
.footer-layout2 .copyright-wrap .footer-links a:hover {
  color: var(--theme-color);
}

/*------------------- 4.4. Breadcumb  -------------------*/
.breadcumb-menu {
  max-width: 100%;
  padding: 0;
  margin: -0.4em 0 25px 0;
  list-style-type: none;
  position: relative;
}
.breadcumb-menu li {
  display: inline-block;
  margin-right: 3px;
  padding-right: 3px;
  list-style: none;
  position: relative;
}
.breadcumb-menu li:after {
  content: "\f105";
  position: relative;
  margin-left: 10px;
  font-weight: 500;
  font-size: 16px;
  color: var(--white-color);
  font-family: var(--icon-font);
}
.breadcumb-menu li:last-child {
  padding-right: 0;
  margin-right: 0;
  color: var(--theme-color);
}
.breadcumb-menu li:last-child:after {
  display: none;
}
.breadcumb-menu li,
.breadcumb-menu a,
.breadcumb-menu span {
  white-space: normal;
  word-break: break-word;
  font-weight: 500;
  font-size: 18px;
  font-family: var(--title-font);
  color: var(--white-color);
}
.breadcumb-menu a:hover {
  color: var(--theme-color);
}

.breadcumb-title {
  margin: -0.28em 0 -0.28em 0;
  font-size: 56px;
  line-height: 1.285;
  letter-spacing: 0.02em;
  color: var(--white-color);
}

body .th-header.header-default ~ .breadcumb-wrapper .breadcumb-content {
  padding: calc(var(--space) + 142px) 0 var(--space);
  /* Medium devices */
  /* Extra small devices */
}
@media (max-width: 991px) {
  body .th-header.header-default ~ .breadcumb-wrapper .breadcumb-content {
    padding: calc(var(--space) + 120px) 0 var(--space);
  }
}
@media (max-width: 575px) {
  body .th-header.header-default ~ .breadcumb-wrapper .breadcumb-content {
    padding: calc(var(--space) + 92px) 0 var(--space);
  }
}

body .th-header.header-default:has(.header-top) ~ .breadcumb-wrapper .breadcumb-content {
  padding: calc(var(--space) + 191px) 0 var(--space);
  /* Medium devices */
  /* Extra small devices */
}
@media (max-width: 991px) {
  body .th-header.header-default:has(.header-top) ~ .breadcumb-wrapper .breadcumb-content {
    padding: calc(var(--space) + 168px) 0 var(--space);
  }
}
@media (max-width: 575px) {
  body .th-header.header-default:has(.header-top) ~ .breadcumb-wrapper .breadcumb-content {
    padding: calc(var(--space) + 140px) 0 var(--space);
  }
}

.breadcumb-wrapper {
  background-color: var(--title-color);
  overflow: hidden;
  position: relative;
  z-index: 1;
}
.breadcumb-wrapper .breadcumb-content {
  --space: 148px;
  padding: var(--space) 0;
  position: relative;
  z-index: 1;
}
.breadcumb-wrapper .breadcumb-thumb {
  position: relative;
  z-index: 1;
  text-align: end;
  margin: -124px -50px 0px 0;
}

/* Large devices */
@media (max-width: 1199px) {
  .breadcumb-wrapper .breadcumb-content {
    --space: 120px;
  }
  .breadcumb-menu li,
  .breadcumb-menu a,
  .breadcumb-menu span {
    font-size: 16px;
  }
  .breadcumb-title {
    font-size: 48px;
  }
}
/* Medium devices */
@media (max-width: 991px) {
  .breadcumb-title {
    font-size: 46px;
  }
}
/* Small devices */
@media (max-width: 767px) {
  .breadcumb-title {
    font-size: 40px;
  }
}
/* Extra small devices */
@media (max-width: 575px) {
  .breadcumb-title {
    font-size: 34px;
  }
  .breadcumb-wrapper .breadcumb-content {
    --space: 100px;
  }
}
/*------------------- 4.5. Pagination  -------------------*/
.wp-block-query-pagination-numbers,
.th-pagination {
  margin-bottom: 30px;
}
.wp-block-query-pagination-numbers ul,
.th-pagination ul {
  margin: 0;
  padding: 0;
}
.wp-block-query-pagination-numbers li,
.th-pagination li {
  display: inline-block;
  margin: 0 6px;
  list-style-type: none;
}
.wp-block-query-pagination-numbers li:last-child,
.th-pagination li:last-child {
  margin-right: 0;
}
.wp-block-query-pagination-numbers li:first-child,
.th-pagination li:first-child {
  margin-left: 0;
}
.wp-block-query-pagination-numbers span,
.wp-block-query-pagination-numbers a,
.th-pagination span,
.th-pagination a {
  display: inline-block;
  text-align: center;
  position: relative;
  border: none;
  color: var(--title-color);
  background-color: transparent;
  border: 1px solid var(--th-border-color);
  width: 48px;
  height: 48px;
  line-height: 48px;
  z-index: 1;
  font-size: 16px;
  font-weight: 400;
  border-radius: 50%;
}
.wp-block-query-pagination-numbers span.active, .wp-block-query-pagination-numbers span:hover,
.wp-block-query-pagination-numbers a.active,
.wp-block-query-pagination-numbers a:hover,
.th-pagination span.active,
.th-pagination span:hover,
.th-pagination a.active,
.th-pagination a:hover {
  color: var(--white-color);
  background-color: var(--theme-color);
}
.wp-block-query-pagination-numbers span.active i, .wp-block-query-pagination-numbers span:hover i,
.wp-block-query-pagination-numbers a.active i,
.wp-block-query-pagination-numbers a:hover i,
.th-pagination span.active i,
.th-pagination span:hover i,
.th-pagination a.active i,
.th-pagination a:hover i {
  color: inherit;
}

.wp-block-query-pagination-next {
  display: inline-block;
  text-align: center;
  position: relative;
  border: none;
  color: var(--title-color);
  background-color: var(--smoke-color2);
  min-width: 56px;
  height: 56px;
  line-height: 56px;
  z-index: 1;
  font-size: 16px;
  padding: 0 20px;
  font-weight: 500;
  border-radius: 12px;
  transition: 0.4s ease-in-out;
}
.wp-block-query-pagination-next.active, .wp-block-query-pagination-next:hover {
  color: var(--white-color);
  background-color: var(--theme-color);
  border-color: var(--theme-color);
}

/* Small devices */
@media (max-width: 767px) {
  .wp-block-query-pagination-numbers span,
  .wp-block-query-pagination-numbers a,
  .th-pagination span,
  .th-pagination a {
    width: 40px;
    height: 40px;
    line-height: 40px;
    font-size: 14px;
  }
}
/*------------------- 4.6. Blog  -------------------*/
/* Blockquote ---------------------*/
blockquote,
.wp-block-quote {
  font-size: 18px;
  line-height: 1.777;
  padding: 39px 60px 35px 60px;
  font-weight: 400;
  display: block;
  position: relative;
  background-color: transparent;
  margin: 50px 0 50px 0;
  color: var(--title-color);
  font-family: var(--title-font);
  border: 1px solid #D8DDE1 !important;
  border-radius: 10px;
}
blockquote p,
.wp-block-quote p {
  font-size: inherit;
  font-family: inherit;
  margin-top: -0.3em;
  margin-bottom: 9px;
  line-height: inherit;
  color: inherit;
  width: 100%;
  position: relative;
  z-index: 3;
}
blockquote p a,
.wp-block-quote p a {
  color: inherit;
}
blockquote:before,
.wp-block-quote:before {
  content: "";
  position: absolute;
  top: -1px;
  left: -1px;
  height: 50px;
  width: 33px;
  background-color: var(--white-color);
}
blockquote:after,
.wp-block-quote:after {
  content: "";
  position: absolute;
  top: -10px;
  left: 0px;
  height: 20px;
  width: 25px;
  background-color: var(--theme-color);
  clip-path: path("M2.21945 18.2759C0.775335 16.6762 0 14.8819 0 11.9734C0 6.8553 3.44484 2.26804 8.45438 0L9.70641 2.01506C5.03057 4.65307 4.11643 8.07633 3.75189 10.2347C4.5048 9.82818 5.49044 9.68633 6.45645 9.77992C8.98576 10.0241 10.9795 12.1898 10.9795 14.8819C10.9795 16.2393 10.4625 17.5411 9.54219 18.5009C8.62192 19.4608 7.37376 20 6.07229 20C5.35256 19.9934 4.64126 19.8376 3.97981 19.5416C3.31836 19.2457 2.71996 18.8154 2.21945 18.2759ZM16.24 18.2759C14.7959 16.6762 14.0205 14.8819 14.0205 11.9734C14.0205 6.8553 17.4654 2.26804 22.4749 0L23.7269 2.01506C19.0511 4.65307 18.137 8.07633 17.7724 10.2347C18.5253 9.82818 19.511 9.68633 20.477 9.77992C23.0063 10.0241 25 12.1898 25 14.8819C25 16.2393 24.483 17.5411 23.5627 18.5009C22.6424 19.4608 21.3943 20 20.0928 20C19.3731 19.9934 18.6618 19.8376 18.0003 19.5416C17.3389 19.2457 16.7405 18.8154 16.24 18.2759Z");
}
blockquote cite,
.wp-block-quote cite {
  display: inline-block;
  font-size: 20px;
  line-height: 1;
  font-weight: 500;
  font-style: normal;
  font-family: var(--title-font);
  white-space: nowrap;
  position: absolute;
  bottom: -17px;
  left: 100px;
  background-color: var(--theme-color);
  color: var(--white-color);
  padding: 7px 45px 7px 20px;
  clip-path: polygon(0 0, 100% 0, calc(100% - 25px) 100%, 0% 100%);
  border-radius: 10px 0 0 10px;
}
blockquote cite br,
.wp-block-quote cite br {
  display: none;
}
blockquote.is-large:not(.is-style-plain), blockquote.is-style-large:not(.is-style-plain), blockquote.style-left-icon, blockquote.has-text-align-right,
.wp-block-quote.is-large:not(.is-style-plain),
.wp-block-quote.is-style-large:not(.is-style-plain),
.wp-block-quote.style-left-icon,
.wp-block-quote.has-text-align-right {
  padding: 40px;
  margin-bottom: 30px;
}
blockquote.style-left-icon,
.wp-block-quote.style-left-icon {
  font-size: 18px;
  color: var(--body-color);
  font-weight: 400;
  line-height: 1.556;
  background-color: var(--smoke-color);
  padding-left: 160px;
}
blockquote.style-left-icon:before,
.wp-block-quote.style-left-icon:before {
  right: unset;
  left: 56px;
  top: 60px;
  font-size: 6rem;
  font-weight: 400;
  line-height: 4rem;
  color: var(--theme-color);
  text-shadow: none;
}
blockquote.style-left-icon cite,
.wp-block-quote.style-left-icon cite {
  color: var(--title-color);
}
blockquote.style-left-icon cite:before,
.wp-block-quote.style-left-icon cite:before {
  background-color: var(--title-color);
  top: 8px;
}
blockquote:not(:has(> cite)) p:last-child,
.wp-block-quote:not(:has(> cite)) p:last-child {
  margin-bottom: -0.3em;
}
blockquote p:has(cite),
.wp-block-quote p:has(cite) {
  padding-bottom: 10px;
}
blockquote p cite,
.wp-block-quote p cite {
  margin-top: 20px;
  margin-bottom: -0.5em;
  bottom: -32px;
}

.wp-block-pullquote {
  color: var(--white-color);
  padding: 0;
}

blockquote.has-very-dark-gray-color {
  color: var(--title-color) !important;
}

.wp-block-pullquote blockquote,
.wp-block-pullquote p {
  color: var(--title-color);
}

.wp-block-pullquote cite {
  position: absolute;
  color: var(--white-color) !important;
}

.wp-block-column blockquote,
.wp-block-column .wp-block-quote {
  padding: 40px 15px 40px 15px;
}
.wp-block-column blockquote:before,
.wp-block-column .wp-block-quote:before {
  width: 100%;
  height: 60px;
  font-size: 30px;
}
.wp-block-column blockquote.style-left-icon, .wp-block-column blockquote.is-large:not(.is-style-plain), .wp-block-column blockquote.is-style-large:not(.is-style-plain), .wp-block-column blockquote.has-text-align-right,
.wp-block-column .wp-block-quote.style-left-icon,
.wp-block-column .wp-block-quote.is-large:not(.is-style-plain),
.wp-block-column .wp-block-quote.is-style-large:not(.is-style-plain),
.wp-block-column .wp-block-quote.has-text-align-right {
  padding: 40px 15px 40px 15px;
}
.wp-block-column blockquote cite,
.wp-block-column .wp-block-quote cite {
  font-size: 14px;
  left: 20px;
}
.wp-block-column blockquote cite:before,
.wp-block-column .wp-block-quote cite:before {
  bottom: 6px;
}

.wp-block-pullquote__citation::before,
.wp-block-pullquote cite::before,
.wp-block-pullquote footer::before {
  bottom: 7px;
}

.has-cyan-bluish-gray-background-color blockquote,
.has-cyan-bluish-gray-background-color .wp-block-quote {
  background-color: var(--white-color);
}

/* Large devices */
@media (max-width: 1199px) {
  blockquote,
  .wp-block-quote {
    padding: 22px 30px;
  }
}
/* Small devices */
@media (max-width: 767px) {
  .wp-block-pullquote.is-style-solid-color blockquote {
    max-width: 90%;
  }
  blockquote cite,
  .wp-block-quote cite {
    font-size: 18px;
    left: 30px;
  }
}
/* Extra small devices */
@media (max-width: 575px) {
  .wp-block-quote.is-large:not(.is-style-plain) p,
  .wp-block-quote.is-style-large:not(.is-style-plain) p {
    font-size: 1.2em;
  }
}
/* Extra small devices */
@media (max-width: 375px) {
  blockquote cite,
  .wp-block-quote cite {
    font-size: 18px;
    padding-left: 22px;
  }
  blockquote cite:before,
  .wp-block-quote cite:before {
    width: 20px;
  }
}
.blog-meta {
  display: flex;
  flex-wrap: wrap;
}
.blog-meta span,
.blog-meta a {
  display: inline-block;
  font-size: 14px;
  font-weight: 500;
  color: var(--title-color);
  font-family: var(--body-font);
  position: relative;
  margin-right: 30px;
}
.blog-meta span i,
.blog-meta a i {
  margin-right: 8px;
}
.blog-meta span:after,
.blog-meta a:after {
  content: "";
  position: absolute;
  height: 14px;
  width: 1px;
  background: var(--title-color);
  right: -15px;
  top: 50%;
  transform: translate(-50%, -50%);
}
.blog-meta span:last-child,
.blog-meta a:last-child {
  margin-right: 0;
  padding-right: 0;
}
.blog-meta span:last-child:after,
.blog-meta a:last-child:after {
  display: none;
}
.blog-meta .author img {
  border-radius: 50%;
  width: 24px;
  height: 24px;
  margin-right: 8px;
  background: var(--title-color);
  border: 1px solid var(--body-color);
}
.blog-meta a:hover {
  color: var(--theme-color);
}

.blog-audio img,
.blog-img img,
.blog-video img {
  transition: 0.4s ease-in-out;
}

.blog-radius-img {
  border-radius: 16px;
  overflow: hidden;
}

.blog-title a {
  color: inherit;
}
.blog-title a:hover {
  color: var(--theme-color);
}

.th-blog {
  margin-bottom: 30px;
}

.blog-inner-title {
  margin-top: -0.25em;
  margin-bottom: 32px;
  font-weight: 600;
  font-size: 40px;
}
.blog-inner-title i {
  color: var(--theme-color);
  margin-right: 8px;
}

.blog-single {
  position: relative;
  margin-bottom: var(--blog-space-y, 56px);
  background-color: var(--white-color);
  overflow: hidden;
}
.blog-single .blog-title {
  margin-bottom: 18px;
  font-size: 28px;
  line-height: 1.333;
  font-weight: 600;
}
.blog-single .blog-text {
  margin-bottom: 28px;
}
.blog-single .social-links {
  margin: 0;
  padding: 0;
  list-style-type: none;
  display: inline-block;
}
.blog-single .social-links li {
  display: inline-block;
  margin-right: 3px;
}
.blog-single .social-links li:last-child {
  margin-right: 0;
}
.blog-single .social-links a {
  display: inline-block;
  width: 40px;
  height: 40px;
  line-height: 40px;
  background-color: var(--smoke-color);
  font-size: 14px;
  color: var(--title-color);
  text-align: center;
  border-radius: 5px;
}
.blog-single .social-links a:hover {
  color: var(--white-color);
  background-color: var(--theme-color);
}
.blog-single .blog-meta {
  margin: -0.35em 0 22px 0;
}
.blog-single .blog-content {
  margin: 0px 0 0 0;
  padding: 0 0 0;
  position: relative;
}
.blog-single .blog-audio {
  line-height: 1;
}
.blog-single .blog-audio,
.blog-single .blog-img,
.blog-single .blog-video {
  position: relative;
  overflow: hidden;
  background-color: var(--smoke-color);
  border-radius: 16px;
  margin-bottom: 40px;
}
.blog-single .blog-img .slick-arrow {
  --pos-x: 30px;
  --icon-size: 45px;
  border: none;
  background-color: var(--white-color);
  color: var(--theme-color);
  box-shadow: none;
}
.blog-single .blog-img .slick-arrow:hover {
  background-color: var(--theme-color);
  color: var(--white-color);
}
.blog-single .blog-img .play-btn {
  --icon-size: 60px;
  position: absolute;
  left: 50%;
  top: 50%;
  margin: calc(var(--icon-size) / -2) 0 0 calc(var(--icon-size) / -2);
}
.blog-single .line-btn {
  display: block;
  max-width: fit-content;
  margin-bottom: -1px;
}
.blog-single .th-slider .slider-arrow {
  --pos-x: 20px;
}
.blog-single:hover .blog-img .slick-arrow {
  opacity: 1;
  visibility: visible;
}

.blog-details .blog-single {
  margin-bottom: 80px;
}

.share-links-title {
  font-size: 24px;
  color: var(--title-color);
  font-family: var(--title-font);
  font-weight: 600;
  margin: 0 13px 0 0;
  display: inline-block;
}

.share-links {
  margin: 70px 0 0 0;
  border-top: 1px solid var(--th-border-color4);
  padding: 24px 0 0;
}
.share-links > .row {
  align-items: center;
  --bs-gutter-y: 20px;
}
.share-links .wp-block-tag-cloud,
.share-links .tagcloud {
  gap: 10px;
  display: inline-flex;
}
.share-links .wp-block-tag-cloud a,
.share-links .tagcloud a {
  background: transparent;
  box-shadow: none;
  border: 1px solid var(--th-border-color);
  padding: 12px 25px;
  font-size: 14px;
  font-weight: 400;
  font-family: var(--title-font);
  color: var(--title-color);
}
.share-links .wp-block-tag-cloud a:hover,
.share-links .tagcloud a:hover {
  background: var(--theme-color);
}
.share-links .th-social {
  display: inline-flex;
}

.blog-author {
  background: var(--white-color);
  margin: 40px 0;
  padding: 40px;
  display: flex;
  gap: 30px;
  box-shadow: 0px 0px 30px rgba(0, 0, 0, 0.06);
  border-radius: 30px;
}
.blog-author .author-img {
  border-radius: 50%;
  flex: none;
  background: var(--smoke-color);
  align-self: self-start;
}
.blog-author .author-img img {
  width: 130px;
  height: 130px;
  object-fit: cover;
  border-radius: 50%;
}
.blog-author .media {
  display: flex;
  margin-bottom: 9px;
}
.blog-author .media .media-body .th-social {
  display: inline-flex;
}
.blog-author .author-name {
  font-weight: 500;
  font-size: 20px;
  margin-bottom: 3px;
}
.blog-author .author-name a {
  color: var(--title-color);
}
.blog-author .author-name a:hover {
  color: var(--theme-color);
}
.blog-author .author-desig {
  color: var(--theme-color);
  font-size: 14px;
  font-weight: 500;
}
.blog-author .author-text {
  margin-bottom: -0.3em;
}

/* Medium Large devices */
@media (max-width: 1399px) {
  .share-links {
    --blog-space-x: 20px;
  }
}
/* Large devices */
@media (max-width: 1199px) {
  .blog-author {
    display: block;
    --blog-space-y: 40px;
    --blog-space-x: 30px;
  }
  .blog-author .author-img {
    margin-bottom: 25px;
  }
}
/* Medium devices */
@media (max-width: 991px) {
  .blog-author,
  .share-links {
    --blog-space-x: 40px;
  }
}
/* Small devices */
@media (max-width: 767px) {
  .blog-author, .share-links {
    --blog-space-x: 20px;
  }
  .blog-inner-title {
    margin-bottom: 22px;
  }
  .blog-details .blog-single {
    --blog-space-x: 20px;
    --blog-space-y: 20px;
  }
  .blog-single .blog-title {
    font-size: 24px;
    line-height: 1.3;
  }
  .blog-single .blog-text {
    margin-bottom: 22px;
  }
  .blog-single .blog-bottom {
    padding-top: 15px;
  }
  .blog-single .blog-meta span,
  .blog-single .blog-meta a {
    padding-right: 3px;
  }
  .blog-single .blog-meta span:after,
  .blog-single .blog-meta a:after {
    display: none;
  }
  .blog-single .share-links-title {
    font-size: 18px;
    display: block;
    margin: 0 0 10px 0;
  }
}
/* Extra small devices */
@media (max-width: 575px) {
  blockquote, .wp-block-quote {
    padding: 20px 20px 30px;
  }
  blockquote:before, .wp-block-quote:before {
    right: 30px;
  }
  .blog-author {
    padding: 20px;
  }
}
/*------------------- 4.7. Comments  -------------------*/
.th-comment-form {
  margin: 0px 0 40px 0;
  position: relative;
  background: var(--white-color);
}
.th-comment-form .row {
  --bs-gutter-x: 20px;
}
.th-comment-form .form-title {
  margin-top: -0.35em;
}
.th-comment-form .form-title a#cancel-comment-reply-link {
  font-size: 0.7em;
  text-decoration: underline;
}
.th-comment-form .form-text {
  margin-bottom: 34px;
  font-size: 16px;
  font-weight: 400;
  color: var(--body-color);
}

.blog-comment-area {
  margin: 40px 0 0;
}

.th-comments-wrap {
  margin: 0px 0 80px;
  background: var(--white-color);
}
.th-comments-wrap .description p:last-child {
  margin-bottom: -0.5em;
}
.th-comments-wrap .comment-respond {
  margin: 30px 0;
}
.th-comments-wrap pre {
  background: #ededed;
  color: #666;
  font-size: 14px;
  margin: 20px 0;
  overflow: auto;
  padding: 20px;
  white-space: pre-wrap;
  word-wrap: break-word;
}
.th-comments-wrap li {
  margin: 0;
}
.th-comments-wrap .th-post-comment {
  padding: 30px;
  position: relative;
  display: flex;
  margin-bottom: 40px;
  background: var(--smoke-color2);
  position: relative;
  border-radius: 10px;
}
.th-comments-wrap .th-post-comment ol,
.th-comments-wrap .th-post-comment ul,
.th-comments-wrap .th-post-comment dl {
  margin-bottom: 1rem;
}
.th-comments-wrap .th-post-comment ol ol,
.th-comments-wrap .th-post-comment ol ul,
.th-comments-wrap .th-post-comment ul ol,
.th-comments-wrap .th-post-comment ul ul {
  margin-bottom: 0;
}
.th-comments-wrap ul.comment-list {
  list-style: none;
  margin: 0;
  padding: 0;
}
.th-comments-wrap ul.comment-list ul ul,
.th-comments-wrap ul.comment-list ul ol,
.th-comments-wrap ul.comment-list ol ul,
.th-comments-wrap ul.comment-list ol ol {
  margin-bottom: 0;
}
.th-comments-wrap .comment-avater {
  width: 80px;
  height: 80px;
  margin-right: 24px;
  overflow: hidden;
  border-radius: 10px;
}
.th-comments-wrap .comment-avater img {
  width: 100%;
  border-radius: 10px;
}
.th-comments-wrap .comment-content {
  flex: 1;
  position: relative;
}
.th-comments-wrap .commented-on {
  font-size: 14px;
  display: inline-block;
  margin-bottom: 14px;
  font-weight: 400;
  font-family: var(--title-font);
  color: var(--body-color);
}
.th-comments-wrap .commented-on i {
  margin-right: 7px;
  font-size: 0.9rem;
  color: var(--theme-color);
}
.th-comments-wrap .name {
  margin-bottom: 3px;
  font-size: 18px;
  font-weight: 600;
  margin-top: -0.15em;
}
.th-comments-wrap .comment-top {
  display: flex;
  justify-content: space-between;
}
.th-comments-wrap .text {
  margin-bottom: 0;
}
.th-comments-wrap .children {
  margin: 0;
  padding: 0;
  list-style-type: none;
  margin-left: 80px;
}
.th-comments-wrap .reply_and_edit {
  margin-bottom: -0.46em;
}
.th-comments-wrap .reply_and_edit a {
  margin-right: 0px;
}
.th-comments-wrap .reply_and_edit a:last-child {
  margin-right: 0;
}
.th-comments-wrap .reply_and_edit .comment-edit-link {
  transform: translate(-105px, 0);
}
.th-comments-wrap .reply_and_edit .comment-edit-link:first-child {
  transform: none;
}
.th-comments-wrap .reply-btn {
  font-weight: 500;
  font-size: 16px;
  color: var(--theme-color);
  display: inline-block;
  background: transparent;
  border-radius: 4px;
  text-transform: uppercase;
  padding: 0;
  position: absolute;
  right: 0;
  top: 0;
  margin-top: -0.3em;
}
.th-comments-wrap .reply-btn i {
  margin-right: 7px;
  color: var(--title-color);
}
.th-comments-wrap .reply-btn:hover {
  color: var(--title-color);
}
.th-comments-wrap .star-rating {
  font-size: 12px;
  margin-bottom: 10px;
  position: absolute;
  top: 5px;
  right: 0;
  width: 80px;
}

ul.comment-list .th-comment-item:last-child:not(.children .th-comment-item) > .th-post-comment {
  margin-bottom: 0;
}
ul.comment-list .th-comment-item:first-child:not(.children .th-comment-item) > .th-post-comment {
  margin-bottom: 30px;
}

.th-comments-wrap.th-comment-form {
  margin: 0;
}

/* Large devices */
@media (max-width: 1199px) {
  .blog-comment-area {
    --blog-space-y: 20px;
    --blog-space-x: 20px;
  }
  .th-comments-wrap,
  .th-comment-form {
    --blog-space-x: 20px;
  }
  .th-comments-wrap .children {
    margin-left: 40px;
  }
}
/* Medium devices */
@media (max-width: 991px) {
  .blog-comment-area {
    --blog-space-x: 40px;
    --blog-space-y: 40px;
  }
  .th-comment-form, .th-comments-wrap {
    --blog-space-x: 40px;
  }
  .th-comments-wrap .comment-avater {
    margin-right: 20px;
  }
  .th-comments-wrap .name {
    font-size: 18px;
  }
}
/* Small devices */
@media (max-width: 767px) {
  .blog-comment-area {
    --blog-space-x: 20px;
    --blog-space-y: 20px;
  }
  .th-comment-form, .th-comments-wrap {
    --blog-space-x: 20px;
  }
  .th-comments-wrap .name {
    font-size: 16px;
  }
  .th-comments-wrap .th-post-comment {
    display: block;
  }
  .th-comments-wrap .star-rating {
    position: relative;
    top: 0;
    right: 0;
  }
  .th-comments-wrap .comment-top {
    display: block;
  }
  .th-comments-wrap .comment-avater {
    margin-right: 0;
    margin-bottom: 25px;
  }
  .th-comments-wrap .children {
    margin-left: 40px;
  }
  .th-comments-wrap .children {
    margin-left: 30px;
  }
}
/*------------------- 4.8. Hero Area  -------------------*/
/* Hero Global ---------------------------------- */
.th-hero-wrapper {
  position: relative;
  z-index: 2;
  overflow: hidden;
}

.th-hero-bg {
  position: absolute;
  inset: 0;
  z-index: -1;
}
.th-hero-bg img {
  height: 100%;
  width: 100%;
  object-fit: cover;
}

/* Hero 1 ---------------------------------- */
.hero-title {
  line-height: 1.222;
  margin-bottom: 28px;
  margin-top: -0.25em;
}
.hero-title span {
  display: block;
}
.hero-title span .text-theme {
  display: inline-block;
}

.hero-text {
  margin-bottom: 32px;
  font-size: 16px;
}

.hero-1 .hero-inner {
  padding: 100px 0;
  /* Medium Large devices */
  /* Large devices */
}
@media (max-width: 1399px) {
  .hero-1 .hero-inner {
    padding: 60px 0;
  }
}
@media (max-width: 1199px) {
  .hero-1 .hero-inner {
    padding: 80px 0 140px;
  }
}
.hero-1 .hero-shape1-2 {
  position: absolute;
  bottom: 0;
  left: 40px;
  /* Extra small devices */
}
@media (max-width: 575px) {
  .hero-1 .hero-shape1-2 {
    display: none;
  }
}
.hero-1 .hero-shadow-text {
  font-size: 250px;
  font-weight: 800;
  font-family: var(--title-font);
  text-transform: uppercase;
  color: rgba(87, 88, 95, 0.05);
  line-height: 0.75em;
  position: absolute;
  bottom: -5px;
  left: 120px;
  z-index: -1;
  /* Extra large devices */
  /* Large devices */
  /* Small devices */
  /* Extra small devices */
  /* Extra small devices */
}
@media (max-width: 1500px) {
  .hero-1 .hero-shadow-text {
    font-size: 180px;
    left: 50px;
  }
}
@media (max-width: 1199px) {
  .hero-1 .hero-shadow-text {
    left: 0;
    right: 0;
    text-align: center;
    font-size: 150px;
  }
}
@media (max-width: 767px) {
  .hero-1 .hero-shadow-text {
    font-size: 100px;
  }
}
@media (max-width: 575px) {
  .hero-1 .hero-shadow-text {
    font-size: 80px;
  }
}
@media (max-width: 375px) {
  .hero-1 .hero-shadow-text {
    font-size: 70px;
  }
}
.hero-1 .scroll-down {
  position: absolute;
  right: 45px;
  bottom: 80px;
  z-index: 1;
  margin-bottom: 50px;
  /* Extra large devices */
  /* Extra small devices */
}
.hero-1 .scroll-down .hero-scroll-wrap {
  display: inline-block;
  width: 30px;
  /* Extra small devices */
}
.hero-1 .scroll-down .hero-scroll-wrap span {
  font-size: 18px;
  font-weight: 400;
  font-family: var(--title-font);
  color: var(--light-color);
  transform-origin: right top;
  transform: translate(-100%, -50%) rotate(-90deg);
  width: 100px;
  display: inline-block;
  margin-bottom: 76px;
}
.hero-1 .scroll-down .hero-scroll-wrap:after {
  content: "";
  position: absolute;
  height: 50px;
  width: 30px;
  border: 1px solid var(--light-color);
  border-radius: 30px;
}
.hero-1 .scroll-down .hero-scroll-wrap:before {
  content: "";
  height: 8px;
  width: 8px;
  border-radius: 50px;
  background-color: var(--theme-color);
  position: absolute;
  bottom: 0;
  left: 50%;
  margin: -20px -4px;
  animation: scrollMove 1.5s infinite;
}
@media (max-width: 575px) {
  .hero-1 .scroll-down .hero-scroll-wrap {
    margin-top: 60px;
  }
}
@media (max-width: 1500px) {
  .hero-1 .scroll-down {
    bottom: 40px;
  }
  .hero-1 .scroll-down .hero-scroll-wrap span {
    display: none;
  }
}
@media (max-width: 575px) {
  .hero-1 .scroll-down {
    right: 50%;
    transform: translate(50%, 0);
  }
}

@keyframes scrollMove {
  0% {
    opacity: 0;
  }
  50% {
    opacity: 1;
  }
  100% {
    opacity: 0;
    transform: translateY(10px);
  }
}
.hero-thumb1-1 {
  border-radius: 24px;
  overflow: hidden;
  display: inline-block;
}
.hero-thumb1-1 img {
  object-fit: cover;
}

.hero-slider1 {
  /* Extra large devices */
}
.hero-slider1 .swiper-slide {
  opacity: 0 !important;
}
.hero-slider1 .swiper-slide.swiper-slide-active {
  opacity: 1 !important;
}
.hero-slider1 .slider-pagination {
  position: absolute;
  right: 55px;
  left: auto;
  bottom: 50%;
  margin: 0;
  width: auto;
  height: auto;
  transform: translate(0, 50%);
}
.hero-slider1 .slider-pagination .swiper-pagination-bullet {
  display: block;
  margin: 48px 0;
  background: transparent;
  border: 1px solid var(--light-color);
  transition: 0.4s;
}
.hero-slider1 .slider-pagination .swiper-pagination-bullet:before {
  inset: -15px;
}
.hero-slider1 .slider-pagination .swiper-pagination-bullet.swiper-pagination-bullet-active {
  background: var(--theme-color);
  border-color: var(--theme-color);
}
@media (max-width: 1500px) {
  .hero-slider1 .slider-pagination {
    display: none;
  }
}

.hero-style1 {
  position: relative;
  z-index: 6;
  /* Extra large devices */
  /* Medium Large devices */
  /* Large devices */
  /* Small devices */
}
.hero-style1 .hero-shape1-1 {
  position: absolute;
  right: 80px;
  top: 150px;
}
.hero-style1 .sub-title {
  margin-bottom: 36px;
  font-weight: 600;
  font-size: 24px;
  margin-top: -0.1em;
  color: var(--title-color);
  font-family: var(--title-font);
  padding-bottom: 0;
}
.hero-style1 .sub-title:after, .hero-style1 .sub-title:before {
  display: none;
}
.hero-style1 .hero-title {
  font-weight: 800;
}
.hero-style1 .hero-text {
  max-width: 650px;
}
.hero-style1 .btn-wrap {
  gap: 24px;
  margin-top: 48px;
}
@media (max-width: 1500px) {
  .hero-style1 .hero-shape1-1 {
    display: none;
  }
}
@media (max-width: 1399px) {
  .hero-style1 .sub-title {
    font-size: 20px;
  }
}
@media (max-width: 1199px) {
  .hero-style1 {
    text-align: center;
  }
  .hero-style1 .sub-title {
    margin-bottom: 26px;
  }
  .hero-style1 .hero-text {
    margin-left: auto;
    margin-right: auto;
  }
  .hero-style1 .btn-wrap {
    justify-content: center;
  }
}
@media (max-width: 767px) {
  .hero-style1 .sub-title {
    font-size: 18px;
  }
}

/* Hero 2 --------------------------------------*/
.hero-2 .hero-inner {
  padding: 178px 0;
  position: relative;
  /* Extra large devices */
  /* Medium Large devices */
  /* Large devices */
}
.hero-2 .hero-inner:after {
  content: "";
  position: absolute;
  inset: 0;
  background: linear-gradient(-76.59deg, rgba(19, 24, 43, 0) 5.14%, rgba(19, 24, 43, 0.8) 59.94%);
}
@media (max-width: 1500px) {
  .hero-2 .hero-inner {
    padding: 200px 0;
  }
}
@media (max-width: 1399px) {
  .hero-2 .hero-inner {
    padding: 180px 0;
  }
}
@media (max-width: 1199px) {
  .hero-2 .hero-inner {
    padding: 120px 0;
  }
}
.hero-2 .hero-inner .grid_lines {
  z-index: 1;
}
.hero-2 .scroll-down {
  position: absolute;
  right: 45px;
  bottom: 40px;
  z-index: 2;
  margin-bottom: 50px;
  /* Extra large devices */
  /* Large devices */
  /* Extra small devices */
}
.hero-2 .scroll-down .hero-scroll-wrap {
  display: inline-block;
  width: 30px;
  /* Extra small devices */
}
.hero-2 .scroll-down .hero-scroll-wrap span {
  font-size: 18px;
  font-weight: 400;
  font-family: var(--title-font);
  color: var(--light-color);
  transform-origin: right top;
  transform: translate(-100%, -50%) rotate(-90deg);
  width: 100px;
  display: inline-block;
  margin-bottom: 76px;
}
.hero-2 .scroll-down .hero-scroll-wrap:after {
  content: "";
  position: absolute;
  height: 50px;
  width: 30px;
  border: 1px solid var(--light-color);
  border-radius: 30px;
}
.hero-2 .scroll-down .hero-scroll-wrap:before {
  content: "";
  height: 8px;
  width: 8px;
  border-radius: 50px;
  background-color: var(--theme-color);
  position: absolute;
  bottom: 0;
  left: 50%;
  margin: -20px -4px;
  animation: scrollMove 1.5s infinite;
}
@media (max-width: 575px) {
  .hero-2 .scroll-down .hero-scroll-wrap {
    margin-top: 60px;
  }
}
@media (max-width: 1500px) {
  .hero-2 .scroll-down {
    bottom: 40px;
  }
  .hero-2 .scroll-down .hero-scroll-wrap span {
    display: none;
  }
}
@media (max-width: 1199px) {
  .hero-2 .scroll-down {
    right: 50%;
    transform: translate(50%, 0);
  }
}
@media (max-width: 575px) {
  .hero-2 .scroll-down {
    right: 50%;
    transform: translate(50%, 0);
  }
}

.hero-slider2 {
  position: relative;
  /* Large devices */
}
.hero-slider2:after {
  content: "";
  position: absolute;
  top: 0;
  bottom: 0;
  right: 0;
  background: var(--black-color2);
  width: 120px;
  z-index: 1;
  /* Large devices */
}
@media (max-width: 1199px) {
  .hero-slider2:after {
    display: none;
  }
}
.hero-slider2 .swiper-slide {
  opacity: 0 !important;
}
.hero-slider2 .swiper-slide.swiper-slide-active {
  opacity: 1 !important;
}
.hero-slider2 .slider-pagination {
  position: absolute;
  right: 55px;
  left: auto;
  bottom: 50%;
  margin: 0;
  width: auto;
  height: auto;
  transform: translate(0, 50%);
}
.hero-slider2 .slider-pagination .swiper-pagination-bullet {
  display: block;
  margin: 48px 0;
  background: transparent;
  border: 1px solid var(--light-color);
  transition: 0.4s;
}
.hero-slider2 .slider-pagination .swiper-pagination-bullet:before {
  inset: -15px;
}
.hero-slider2 .slider-pagination .swiper-pagination-bullet.swiper-pagination-bullet-active {
  background: var(--theme-color);
  border-color: var(--theme-color);
}
@media (max-width: 1199px) {
  .hero-slider2 .slider-pagination {
    display: none;
  }
}

.hero-style2 {
  position: relative;
  z-index: 6;
  /* Extra large devices */
  /* Large devices */
}
.hero-style2 .hero-shape2-1 {
  position: absolute;
  left: -60px;
  top: -120px;
  z-index: 3;
}
.hero-style2 .hero-title {
  font-weight: 800;
  margin-bottom: 20px;
}
.hero-style2 .hero-text {
  max-width: 650px;
  font-size: 20px;
}
.hero-style2 .btn-wrap {
  gap: 24px;
  margin-top: 48px;
}
@media (max-width: 1500px) {
  .hero-style2 .hero-shape1-1 {
    display: none;
  }
}
@media (max-width: 1199px) {
  .hero-style2 {
    text-align: center;
  }
  .hero-style2 .hero-text {
    margin-left: auto;
    margin-right: auto;
  }
  .hero-style2 .btn-wrap {
    justify-content: center;
  }
}

/* Hero 3 -------------------------------------*/
.hero-3 {
  position: relative;
  background: var(--smoke-color3);
}
.hero-3 .grid_lines .grid_line {
  background-color: #E9E2D7;
}
.hero-3 .grid_lines .grid_line:after, .hero-3 .grid_lines .grid_line:before {
  background: linear-gradient(0deg, var(--theme-color) 0%, rgba(255, 255, 255, 0) 100%);
  animation: gridanim2 25s linear infinite;
}
.hero-3 .grid_lines .grid_line:after {
  animation-delay: 10s;
}
.hero-3 .scroll-down {
  position: absolute;
  left: 50%;
  bottom: 64px;
  z-index: 7;
  display: inline-flex;
  /* Extra large devices */
  /* Large devices */
}
.hero-3 .scroll-down .hero-scroll-wrap {
  display: inline-block;
  width: 30px;
  height: 50px;
  position: relative;
  /* Extra small devices */
}
.hero-3 .scroll-down .hero-scroll-wrap:after {
  content: "";
  position: absolute;
  height: 50px;
  width: 30px;
  border: 1px solid var(--body-color);
  border-radius: 30px;
}
.hero-3 .scroll-down .hero-scroll-wrap:before {
  content: "";
  height: 8px;
  width: 8px;
  border-radius: 50px;
  background-color: var(--title-color);
  position: absolute;
  bottom: 0;
  left: 50%;
  margin: 25px -4px;
  animation: scrollMove 1.5s infinite;
}
@media (max-width: 575px) {
  .hero-3 .scroll-down .hero-scroll-wrap {
    margin-top: 60px;
  }
}
@media (max-width: 1500px) {
  .hero-3 .scroll-down {
    bottom: 40px;
  }
  .hero-3 .scroll-down .hero-scroll-wrap span {
    display: none;
  }
}
@media (max-width: 1199px) {
  .hero-3 .scroll-down .hero-scroll-wrap:after {
    background: var(--white-color);
    border: 1px solid var(--white-color);
    z-index: -1;
  }
}

.hero-style3 {
  position: relative;
  z-index: 6;
  padding: 190px 0;
  /* Medium Large devices */
  /* Large devices */
  /* Small devices */
}
.hero-style3 .hero-bg-shape {
  position: absolute;
  top: 90px;
  left: 0;
}
.hero-style3 .hero-subtitle {
  margin-bottom: 48px;
  font-size: 24px;
  font-weight: 600;
  font-family: var(--title-font);
  color: var(--title-color);
  margin-top: -0.2em;
  display: block;
}
.hero-style3 .btn-wrap {
  margin-top: 43px;
}
@media (max-width: 1399px) {
  .hero-style3 {
    padding: 180px 0;
  }
}
@media (max-width: 1199px) {
  .hero-style3 {
    padding: 150px 0 100px;
    text-align: center;
    margin: 0 auto;
    max-width: none;
  }
  .hero-style3 .btn-wrap {
    justify-content: center;
  }
}
@media (max-width: 767px) {
  .hero-style3 {
    padding: 120px 0 100px;
  }
  .hero-style3 .hero-subtitle {
    margin-bottom: 28px;
    font-size: 18px;
  }
  .hero-style3 .hero-bg-shape {
    display: none;
  }
}

.hero-thumb3 {
  text-align: center;
  position: relative;
  top: 0;
  bottom: 0;
  height: 100%;
  display: flex;
  align-items: end;
  justify-content: center;
  /* Extra small devices */
}
.hero-thumb3:after {
  content: "";
  position: absolute;
  width: 570px;
  height: 570px;
  border-radius: 50%;
  background: var(--theme-color);
  bottom: -285px;
  z-index: -1;
}
.hero-thumb3 .ripple-1,
.hero-thumb3 .ripple-2,
.hero-thumb3 .ripple-3,
.hero-thumb3 .ripple-4,
.hero-thumb3 .ripple-5 {
  width: 570px;
  height: 570px;
  position: absolute;
  margin-left: -285px;
  left: 50%;
  bottom: -285px;
  background-color: transparent;
  border: 1px solid rgba(87, 88, 95, 0.5);
  border-radius: 50%;
  animation: ripple2 10s linear infinite;
  opacity: 0;
  z-index: -1;
}
.hero-thumb3 .ripple-1 {
  animation-delay: 0;
}
.hero-thumb3 .ripple-2 {
  animation-delay: 2s;
}
.hero-thumb3 .ripple-3 {
  animation-delay: 4s;
}
.hero-thumb3 .ripple-4 {
  animation-delay: 6s;
}
.hero-thumb3 .ripple-5 {
  animation-delay: 8s;
}
@media (max-width: 575px) {
  .hero-thumb3:after {
    width: 320px;
    height: 320px;
    bottom: -160px;
  }
}

@keyframes ripple2 {
  0% {
    transform: scale(0.6);
    opacity: 0.4;
  }
  50% {
    opacity: 0.5;
  }
  80% {
    opacity: 0.2;
  }
  100% {
    transform: scale(2);
    opacity: 0;
  }
}
/* Hero 4 -------------------------------------*/
.hero-4 {
  position: relative;
  overflow: hidden;
  background: var(--title-color);
}
.hero-4 .hero-thumb4 {
  position: absolute;
  right: 0;
  top: 0;
  bottom: 0;
  width: 50%;
  /* Large devices */
  /* Medium devices */
}
.hero-4 .hero-thumb4 .circle-tag {
  position: absolute;
  left: 0;
  bottom: 100px;
  background: rgba(19, 24, 43, 0.7);
  border: 1px solid var(--light-color);
  backdrop-filter: blur(5px);
  height: 138px;
  width: 138px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
}
.hero-4 .hero-thumb4 .circle-anime-tag {
  width: 120px;
  height: 120px;
  margin-left: -60px;
  margin-top: -60px;
  font-size: 14px;
  font-weight: 500;
}
.hero-4 .hero-thumb4 .circle-anime-tag span {
  --rotate-letter: 9.5deg;
  height: 65px;
  width: 30px;
  left: 30%;
  top: -3px;
}
@media (max-width: 1199px) {
  .hero-4 .hero-thumb4 {
    max-width: 40%;
  }
}
@media (max-width: 991px) {
  .hero-4 .hero-thumb4 {
    display: none;
  }
}
.hero-4 .hero-img {
  clip-path: polygon(150px 0, 100% 0, 100% 100%, 150px 100%, 0 50%);
  height: 100%;
  width: 100%;
}
.hero-4 .hero-img img {
  height: 100%;
  width: 100%;
  object-fit: cover;
}
.hero-4 .hero-img:after {
  content: "";
  position: absolute;
  inset: 0;
  background: rgba(19, 24, 43, 0.2);
}

.hero-slider4 {
  position: relative;
  /* Large devices */
}
.hero-slider4 .swiper-slide {
  opacity: 0 !important;
}
.hero-slider4 .swiper-slide.swiper-slide-active {
  opacity: 1 !important;
}
.hero-slider4 .slider-pagination {
  position: absolute;
  right: 55px;
  left: auto;
  bottom: 50%;
  margin: 0;
  width: auto;
  height: auto;
  transform: translate(0, 50%);
}
.hero-slider4 .slider-pagination .swiper-pagination-bullet {
  display: block;
  margin: 48px 0;
  background: var(--white-color);
  border: 1px solid var(--light-color);
  transition: 0.4s;
}
.hero-slider4 .slider-pagination .swiper-pagination-bullet:before {
  inset: -15px;
}
.hero-slider4 .slider-pagination .swiper-pagination-bullet.swiper-pagination-bullet-active {
  background: var(--theme-color);
  border-color: var(--theme-color);
}
@media (max-width: 1199px) {
  .hero-slider4 .slider-pagination {
    display: none;
  }
}

.hero-style4 {
  padding: 150px 0;
  position: relative;
  /* Large devices */
  /* Medium devices */
  /* Extra small devices */
}
.hero-style4 .hero-bg-shape4-1 {
  position: absolute;
  right: 80px;
  top: 120px;
  /* Extra large devices */
  /* Medium Large devices */
  /* Medium devices */
  /* Extra small devices */
}
@media (max-width: 1500px) {
  .hero-style4 .hero-bg-shape4-1 {
    right: -20px;
  }
}
@media (max-width: 1299px) {
  .hero-style4 .hero-bg-shape4-1 {
    right: -60px;
  }
}
@media (max-width: 991px) {
  .hero-style4 .hero-bg-shape4-1 {
    right: -30px;
    top: 80px;
  }
}
@media (max-width: 575px) {
  .hero-style4 .hero-bg-shape4-1 {
    display: none;
  }
}
.hero-style4 .hero-title {
  margin-bottom: 20px;
}
.hero-style4 .hero-text {
  font-size: 20px;
  max-width: 590px;
  margin-bottom: -0.3em;
}
.hero-style4 .btn-wrap {
  margin-top: 60px;
}
@media (max-width: 1199px) {
  .hero-style4 {
    padding: 120px 0;
  }
  .hero-style4 .hero-text {
    font-size: 16px;
  }
}
@media (max-width: 991px) {
  .hero-style4 {
    text-align: center;
  }
  .hero-style4 .hero-text {
    margin-left: auto;
    margin-right: auto;
  }
  .hero-style4 .btn-wrap {
    justify-content: center;
  }
}
@media (max-width: 575px) {
  .hero-style4 {
    padding: 100px 0;
  }
  .hero-style4 .btn-wrap {
    margin-top: 40px;
  }
}

/* Hero 5 -------------------------------------*/
.hero-5 {
  background: var(--smoke-color2);
}
.hero-5 .scroll-down {
  position: absolute;
  right: 45px;
  bottom: 48px;
  z-index: 1;
  margin-bottom: 50px;
  /* Extra large devices */
  /* Large devices */
}
.hero-5 .scroll-down .hero-scroll-wrap {
  display: inline-block;
  width: 30px;
  /* Extra small devices */
}
.hero-5 .scroll-down .hero-scroll-wrap span {
  font-size: 18px;
  font-weight: 400;
  font-family: var(--title-font);
  color: var(--title-color);
  transform-origin: right top;
  transform: translate(-100%, -50%) rotate(-90deg);
  width: 100px;
  display: inline-block;
  margin-bottom: 76px;
}
.hero-5 .scroll-down .hero-scroll-wrap:after {
  content: "";
  position: absolute;
  height: 50px;
  width: 30px;
  border: 1px solid var(--light-color);
  border-radius: 30px;
}
.hero-5 .scroll-down .hero-scroll-wrap:before {
  content: "";
  height: 8px;
  width: 8px;
  border-radius: 50px;
  background-color: var(--theme-color);
  position: absolute;
  bottom: 0;
  left: 50%;
  margin: -20px -4px;
  animation: scrollMove 1.5s infinite;
}
@media (max-width: 575px) {
  .hero-5 .scroll-down .hero-scroll-wrap {
    margin-top: 60px;
  }
}
@media (max-width: 1500px) {
  .hero-5 .scroll-down {
    bottom: 40px;
  }
  .hero-5 .scroll-down .hero-scroll-wrap span {
    display: none;
  }
}
@media (max-width: 1199px) {
  .hero-5 .scroll-down {
    right: 50%;
    transform: translate(50%, 0);
  }
}

.hero-style5 {
  padding: 200px 0;
  /* Medium Large devices */
  /* Large devices */
}
.hero-style5 .hero-shape5-1 {
  position: absolute;
  transform: translate(-50%, -30%);
  max-width: 153px;
}
.hero-style5 .hero-shape5-2 {
  position: absolute;
  bottom: 20%;
  right: 25%;
  width: 65px;
}
.hero-style5 .hero-shape5-3 {
  position: absolute;
  top: 10%;
  right: 5%;
}
.hero-style5 .hero-title {
  margin-bottom: 38px;
}
.hero-style5 .hero-title span.text-theme {
  display: inline-block;
}
.hero-style5 .hero-subtitle {
  margin-bottom: 35px;
  font-size: 18px;
  font-weight: 600;
  font-family: var(--title-font);
  color: var(--title-color);
  margin-top: -0.2em;
  display: block;
}
.hero-style5 .hero-video-wrap {
  position: absolute;
  bottom: 25%;
  left: 0;
}
.hero-style5 .hero-video-wrap .play-btn {
  z-index: 3;
}
.hero-style5 .hero-video-wrap .circle-anime-tag {
  color: var(--title-color);
  font-size: 16px;
  font-weight: 600;
  width: 152px;
  height: 152px;
  margin-top: -76px;
  margin-left: -76px;
}
.hero-style5 .hero-video-wrap .circle-anime-tag span {
  --rotate-letter: 10deg;
  height: 81px;
  left: 38%;
  top: -3px;
}
@media (max-width: 1399px) {
  .hero-style5 {
    padding: 100px 0;
  }
}
@media (max-width: 1199px) {
  .hero-style5 {
    padding: 100px 0 120px;
  }
  .hero-style5 .hero-video-wrap {
    position: relative;
    bottom: 0;
    left: 0;
    width: 100%;
    padding: 55px 0;
  }
}

.image-wrapper {
  position: fixed;
  display: flex;
  justify-content: center;
  align-items: center;
  /* Large devices */
}
.image-wrapper .hero-thumb5 {
  height: auto;
  transform: scale(0); /* Start with scale 0 */
  width: var(--img-width);
  height: var(--img-width);
  perspective: 800px;
}
.image-wrapper .hero-thumb5 .single-rb {
  transform-style: preserve-3d;
  width: var(--img-width);
  height: var(--img-width);
  display: inline-block;
  transition: inherit;
}
.image-wrapper .hero-thumb5 .gsap-full-width {
  transform: scale(0); /* Start hidden */
  transition: transform 0.5s ease-in-out; /* Smooth scaling */
  display: none; /* Initially hidden */
  position: fixed;
  width: 100%;
  height: 100vh;
}
.image-wrapper .hero-thumb5 img {
  position: absolute;
  object-fit: cover;
  width: var(--img-width);
  height: var(--img-width);
}
.image-wrapper .hero-thumb5 .front-side {
  transform: translateZ(calc(var(--img-width) / 2));
}
.image-wrapper .hero-thumb5 .back-side {
  transform: rotateY(180deg) translateZ(calc(var(--img-width) / 2));
}
.image-wrapper .hero-thumb5 .left-side {
  transform: rotateY(-90deg) translateX(calc((0px - var(--img-width)) / 2));
  transform-origin: left;
}
.image-wrapper .hero-thumb5 .right-side {
  transform: rotateY(90deg) translateX(calc(var(--img-width) / 2));
  transform-origin: right;
}
.image-wrapper .hero-thumb5 .top-side {
  transform: rotateX(-90deg) translateY(calc((0px - var(--img-width)) / 2));
  transform-origin: top;
}
.image-wrapper .hero-thumb5 .bottom-side {
  transform: rotateX(90deg) translateY(calc((0px - var(--img-width)) / 2));
  transform-origin: bottom;
}
@media (max-width: 1199px) {
  .image-wrapper {
    display: none;
  }
}

.hero-thumb5-1 {
  --img-width: 145px;
  right: 12%;
  top: 15%;
  width: var(--img-width);
  height: var(--img-width);
  /* Medium Large devices */
}
@media (max-width: 1399px) {
  .hero-thumb5-1 {
    --img-width: 100px;
  }
}

.hero-thumb5-2 {
  --img-width: 145px;
  left: 15%;
  top: 15%;
  width: var(--img-width);
  height: var(--img-width);
  /* Medium Large devices */
}
@media (max-width: 1399px) {
  .hero-thumb5-2 {
    --img-width: 100px;
  }
}

.hero-thumb5-3 {
  left: 5%;
  top: 43%;
  --img-width: 145px;
  width: var(--img-width);
  height: var(--img-width);
  /* Medium Large devices */
}
@media (max-width: 1399px) {
  .hero-thumb5-3 {
    --img-width: 100px;
  }
}

.hero-thumb5-4 {
  left: 15%;
  bottom: 11%;
  --img-width: 145px;
  width: var(--img-width);
  height: var(--img-width);
  /* Medium Large devices */
}
@media (max-width: 1399px) {
  .hero-thumb5-4 {
    --img-width: 100px;
  }
}

.hero-thumb5-5 {
  right: 5%;
  top: 43%;
  --img-width: 145px;
  width: var(--img-width);
  height: var(--img-width);
  /* Medium Large devices */
}
@media (max-width: 1399px) {
  .hero-thumb5-5 {
    --img-width: 100px;
  }
}

.hero-thumb5-6 {
  right: 12%;
  bottom: 11%;
  --img-width: 145px;
  width: var(--img-width);
  height: var(--img-width);
  /* Medium Large devices */
}
@media (max-width: 1399px) {
  .hero-thumb5-6 {
    --img-width: 100px;
  }
}

/*------------------- 4.9. Error  -------------------*/
.error-page-wrap {
  background: var(--smoke-color);
  display: flex;
  flex-direction: row-reverse;
  border-radius: 16px;
  overflow: hidden;
  /* Medium devices */
}
@media (max-width: 991px) {
  .error-page-wrap {
    display: block;
  }
}

.error-content {
  text-align: center;
  flex: 1;
  align-self: center;
  padding: 20px;
  /* Medium devices */
}
@media (max-width: 991px) {
  .error-content {
    padding: 60px 30px;
  }
}

.error-img {
  flex: 1;
}
.error-img img {
  height: 100%;
  width: 100%;
  object-fit: cover;
}

/*------------------- 4.00. Popup Search  -------------------*/
.popup-search-box {
  position: fixed;
  top: 0;
  left: 50%;
  background-color: rgba(19, 24, 43, 0.75);
  height: 0;
  width: 0;
  overflow: hidden;
  z-index: 99999;
  opacity: 0;
  visibility: hidden;
  border-radius: 50%;
  transform: translateX(-50%);
  transition: all ease 0.4s;
}
.popup-search-box button.searchClose {
  width: 60px;
  height: 60px;
  line-height: 58px;
  position: absolute;
  top: 40px;
  right: 40px;
  border-width: 2px;
  border-style: solid;
  border-color: var(--theme-color);
  background-color: transparent;
  font-size: 22px;
  border-radius: 50%;
  transform: rotate(0);
  transition: all ease 0.4s;
  color: var(--white-color);
}
.popup-search-box button.searchClose:hover {
  color: var(--white-color);
  border-color: var(--theme-color);
  background: var(--theme-color);
  transform: rotate(90deg);
}
.popup-search-box form {
  position: absolute;
  top: 50%;
  left: 50%;
  display: inline-block;
  padding-bottom: 40px;
  cursor: auto;
  width: 100%;
  max-width: 700px;
  transform: translate(-50%, -50%) scale(0);
  transition: transform ease 0.4s;
  /* Large devices */
}
@media (max-width: 1199px) {
  .popup-search-box form {
    max-width: 600px;
  }
}
.popup-search-box form input {
  font-size: 18px;
  height: 70px;
  width: 100%;
  border: 2px solid var(--theme-color);
  background-color: transparent;
  padding-left: 30px;
  color: #fff;
  border-radius: 50px;
  padding-right: 80px;
}
.popup-search-box form input::-moz-placeholder {
  color: #fff;
}
.popup-search-box form input::-webkit-input-placeholder {
  color: #fff;
}
.popup-search-box form input:-ms-input-placeholder {
  color: #fff;
}
.popup-search-box form input::placeholder {
  color: #fff;
}
.popup-search-box form button {
  position: absolute;
  top: 0px;
  background-color: transparent;
  border: none;
  color: #fff;
  font-size: 24px;
  right: 12px;
  color: var(--white-color);
  cursor: pointer;
  width: 70px;
  height: 70px;
  transition: all ease 0.4s;
  transform: scale(1.001);
}
.popup-search-box form button:hover {
  transform: scale(1.1);
}
.popup-search-box.show {
  opacity: 1;
  visibility: visible;
  width: 100.1%;
  height: 100%;
  transition: all ease 0.4s;
  border-radius: 0;
}
.popup-search-box.show form {
  transition-delay: 0.5s;
  transform: translate(-50%, -50%) scale(1);
}

/*------------------- 4.00. Popup Side Menu  -------------------*/
.sidemenu-wrapper {
  position: fixed;
  z-index: 99999;
  right: 0;
  top: 0;
  height: 100%;
  width: 0;
  background-color: rgba(0, 0, 0, 0.75);
  opacity: 0;
  visibility: hidden;
  transition: all ease 0.8s;
}
.sidemenu-wrapper .info-box_link {
  color: var(--title-color);
}
.sidemenu-wrapper .info-box_link:hover {
  color: var(--theme-color);
}
.sidemenu-wrapper .th-newsletter-widget .newsletter-form input {
  color: var(--title-color);
}
.sidemenu-wrapper .newsletter-widget .newsletter-form .th-btn:hover {
  color: var(--title-color);
}
.sidemenu-wrapper .th-social a {
  box-shadow: none;
}
.sidemenu-wrapper .closeButton {
  display: inline-block;
  border: 2px solid;
  width: 50px;
  height: 50px;
  line-height: 48px;
  font-size: 24px;
  padding: 0;
  position: absolute;
  top: 20px;
  right: 20px;
  background-color: var(--white-color);
  border-radius: 50%;
  transform: rotate(0);
  transition: all ease 0.4s;
}
.sidemenu-wrapper .closeButton:hover {
  color: var(--theme-color);
  border-color: var(--theme-color);
  transform: rotate(90deg);
}
.sidemenu-wrapper .sidemenu-content {
  background-color: var(--white-color);
  width: 450px;
  margin-left: auto;
  padding: 80px 30px;
  height: 100%;
  overflow: scroll;
  position: relative;
  right: -500px;
  cursor: auto;
  transition-delay: 1s;
  transition: right ease 1s;
}
.sidemenu-wrapper .sidemenu-content::-webkit-scrollbar-track {
  -webkit-box-shadow: inset 0 0 1px rgba(0, 0, 0, 0.1);
  box-shadow: inset 0 0 1px rgba(0, 0, 0, 0.1);
  background-color: #f5f5f5;
}
.sidemenu-wrapper .sidemenu-content::-webkit-scrollbar {
  width: 2px;
  background-color: #f5f5f5;
}
.sidemenu-wrapper .widget {
  padding: 0;
  border: none;
  background-color: transparent;
}
.sidemenu-wrapper.show {
  opacity: 1;
  visibility: visible;
  width: 100%;
  transition: all ease 0.8s;
}
.sidemenu-wrapper.show .sidemenu-content {
  right: 0;
  opacity: 1;
  visibility: visible;
}

/* Extra small devices */
@media (max-width: 575px) {
  .sidemenu-wrapper {
    background: transparent;
  }
  .sidemenu-wrapper .sidemenu-content {
    width: 100%;
  }
  .sidemenu-wrapper .sidemenu-content .widget_shopping_cart .th-btn {
    margin-bottom: 10px;
  }
}
/*------------------- 4.00. Wocommerce  -------------------*/
.woocommerce-message,
.woocommerce-info {
  position: relative;
  padding: 11px 20px 11px 50px;
  background-color: var(--theme-color);
  color: var(--white-color);
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 15px;
  border-radius: 0px;
}
.woocommerce-message a,
.woocommerce-info a {
  color: var(--white-color);
  text-decoration: underline;
}
.woocommerce-message a:hover,
.woocommerce-info a:hover {
  color: var(--theme-color2);
}
.woocommerce-message:before,
.woocommerce-info:before {
  content: "\f06a";
  font-family: var(--icon-font);
  font-weight: 400;
  margin-right: 10px;
  font-size: 18px;
  position: absolute;
  left: 20px;
  top: 11px;
}

.woocommerce-notices-wrapper .woocommerce-message {
  background-color: var(--theme-color);
  color: var(--white-color);
}
.woocommerce-notices-wrapper .woocommerce-message:before {
  content: "\f14a";
  font-weight: 300;
}

.woocommerce-form-login-toggle .woocommerce-info {
  background-color: var(--theme-color);
  color: var(--white-color);
}
.woocommerce-form-login-toggle .woocommerce-info a {
  color: inherit;
}
.woocommerce-form-login-toggle .woocommerce-info a:hover {
  color: var(--theme-color2);
}

.woocommerce-form-register,
.woocommerce-form-coupon,
.woocommerce-form-login {
  padding: 35px 40px 35px 40px;
  background-color: var(--white-color);
  box-shadow: 0px 6px 30px rgba(1, 15, 28, 0.1);
  margin-bottom: 0;
  border-radius: 0px;
  /* Extra small devices */
}
@media (max-width: 575px) {
  .woocommerce-form-register,
  .woocommerce-form-coupon,
  .woocommerce-form-login {
    padding: 40px 20px;
    border-radius: 10px;
  }
}
.woocommerce-form-register .form-group,
.woocommerce-form-coupon .form-group,
.woocommerce-form-login .form-group {
  margin-bottom: 20px;
}
.woocommerce-form-register .form-group:last-child,
.woocommerce-form-coupon .form-group:last-child,
.woocommerce-form-login .form-group:last-child {
  margin-bottom: 0;
}

.woocommerce-error {
  background-color: var(--error-color);
  color: #fff;
  list-style: none;
  padding: 10px 26px;
  margin: 0 0 30px 0;
  border-radius: 0;
  font-weight: 500;
  font-size: 14px;
}

nav.woocommerce-MyAccount-navigation li {
  border: 1px solid #ddd;
  margin: 0;
  border-top: none;
}
nav.woocommerce-MyAccount-navigation li:first-child {
  border-top: 1px solid #ddd;
}
nav.woocommerce-MyAccount-navigation li a {
  color: var(--title-color);
  font-weight: 700;
  padding: 7px 17px;
  display: block;
}
nav.woocommerce-MyAccount-navigation li.is-active a,
nav.woocommerce-MyAccount-navigation li a:hover {
  color: var(--white-color);
  background-color: var(--theme-color);
}

.woocommerce-MyAccount-content h3 {
  margin-top: -0.3em;
}
.woocommerce-MyAccount-content .btn {
  background-color: var(--theme-color);
  color: var(--white-color);
  font-size: 14px;
  padding: 10px 25px;
  font-weight: 700;
}
.woocommerce-MyAccount-content .btn:hover {
  background-color: var(--title-color);
  color: var(--white-color);
}

table.variations,
.woocommerce-grouped-product-list-item {
  border-collapse: separate;
  border-spacing: 0 15px;
  margin-bottom: 5px;
  align-items: center;
  border: none;
}
table.variations td,
.woocommerce-grouped-product-list-item td {
  border: none;
  vertical-align: middle;
  padding: 0 5px;
}
table.variations td:first-child,
.woocommerce-grouped-product-list-item td:first-child {
  padding: 0;
}
table.variations label,
.woocommerce-grouped-product-list-item label {
  margin: 0;
  font-size: 14px;
  text-transform: capitalize;
}
table.variations label a,
.woocommerce-grouped-product-list-item label a {
  color: var(--title-color);
}
table.variations label a:hover,
.woocommerce-grouped-product-list-item label a:hover {
  color: var(--theme-color);
}
table.variations .label,
.woocommerce-grouped-product-list-item .label {
  border: none;
}
table.variations__label,
.woocommerce-grouped-product-list-item__label {
  border: none !important;
  font-weight: 600;
}
table.variations__price,
.woocommerce-grouped-product-list-item__price {
  border: none !important;
}
table.variations__price .price,
table.variations__price .amount,
.woocommerce-grouped-product-list-item__price .price,
.woocommerce-grouped-product-list-item__price .amount {
  font-size: 18px !important;
}
table.variations del,
.woocommerce-grouped-product-list-item del {
  margin-left: 12px;
}

.woocommerce-product-attributes th,
.woocommerce-product-attributes td {
  border: 1px solid var(--th-border-color);
}
.woocommerce-product-attributes th p:last-child,
.woocommerce-product-attributes td p:last-child {
  margin-bottom: 0;
}

.woocommerce-grouped-product-list.group_table {
  border-collapse: collapse;
  margin-bottom: 15px;
}
.woocommerce-grouped-product-list.group_table .woocommerce-Price-amount.amount {
  font-size: 16px;
  color: var(--title-color);
}
.woocommerce-grouped-product-list.group_table label {
  margin: 0 0 0 10px;
  margin: 0 0 0 10px;
  font-family: var(--title-font);
  font-size: 18px;
}
.woocommerce-grouped-product-list.group_table .qty-input {
  border-color: #e3e6e9;
}
.woocommerce-grouped-product-list.group_table tr {
  border-bottom: 1px solid #e3e6e9;
}
.woocommerce-grouped-product-list.group_table tr:last-child {
  border-bottom: none;
}
.woocommerce-grouped-product-list.group_table td {
  padding: 30px 5px;
}

table.variations {
  width: max-content;
  position: relative;
}
table.variations td {
  padding: 0;
}
table.variations td.label {
  padding-right: 10px;
  width: max-content;
}
table.variations select {
  width: max-content;
  font-weight: 400;
  line-height: 1.5;
  vertical-align: middle;
  margin: 0;
  padding-right: 54px;
  padding-left: 20px;
  height: 50px;
}
table.variations .reset_variations {
  margin-left: 16px;
  display: inline-block;
  position: absolute;
  left: 100%;
  bottom: 25px;
}

.woosq-product .product .woocommerce-grouped-product-list-item__quantity,
.woosq-product .product .woocommerce-grouped-product-list-item__label,
.woosq-product .product .woocommerce-grouped-product-list-item__price {
  width: auto !important;
}

.woocommerce-grouped-product-list-item__label:after,
.woocommerce-grouped-product-list-item__price:after {
  display: none;
}

.woocommerce-variation.single_variation {
  margin-bottom: 30px;
}
.woocommerce-variation.single_variation .price {
  color: var(--title-color);
  font-weight: 700;
}

.wooscp-table-items td.woocommerce-product-attributes-item__value {
  padding-left: 15px !important;
}
.wooscp-table-items a.added_to_cart.wc-forward {
  margin-left: 15px;
  text-decoration: underline;
}

.tinvwl_added_to_wishlist.tinv-modal.tinv-modal-open {
  z-index: 1111;
}

table.woocommerce-product-attributes {
  margin-bottom: 30px;
}

#woosq-popup .product_meta {
  margin-top: 20px;
}
#woosq-popup .product_title {
  font-size: 24px;
  margin-bottom: 5px;
}
#woosq-popup .single-product .product .actions {
  align-items: center;
  display: flex;
  gap: 20px;
}
#woosq-popup .single-product .product .actions > div {
  height: auto;
  overflow: visible;
  width: max-content;
}
#woosq-popup .single-product .product .actions > div .quantity.style2.woocommerce-grouped-product-list-item__quantity {
  width: max-content;
}

.login-tab {
  margin-bottom: 30px;
  justify-content: center;
}
.login-tab button.nav-link {
  background-color: var(--smoke-color);
  color: var(--title-color);
  padding: 11px 39px;
  font-size: 18px;
  font-weight: 500;
  border-radius: 15px ​15px 0;
}
.login-tab button.nav-link.active {
  background-color: var(--theme-color);
  color: var(--white-color);
}

.star-rating {
  overflow: hidden;
  position: relative;
  width: 100px;
  height: 1.2em;
  line-height: 1.2em;
  display: block;
  font-family: var(--icon-font);
  font-weight: 700;
  font-size: 14px;
}
.star-rating:before {
  content: "\e28b\e28b\e28b\e28b\e28b";
  color: #e1e1e1;
  float: left;
  top: 0;
  left: 0;
  position: absolute;
  letter-spacing: 3px;
}
.star-rating span {
  overflow: hidden;
  float: left;
  top: 0;
  left: 0;
  position: absolute;
  padding-top: 1.5em;
}
.star-rating span:before {
  content: "\e28b\e28b\e28b\e28b\e28b";
  top: 0;
  position: absolute;
  left: 0;
  color: var(--theme-color);
  letter-spacing: 3px;
}

.rating-select label {
  margin: 0;
  margin-right: 10px;
}
.rating-select p.stars {
  margin-bottom: 0;
  line-height: 1;
}
.rating-select p.stars a {
  position: relative;
  height: 14px;
  width: 18px;
  text-indent: -999em;
  display: inline-block;
  text-decoration: none;
}
.rating-select p.stars a::before {
  display: block;
  position: absolute;
  top: 0;
  left: 0;
  width: 18px;
  height: 14px;
  line-height: 1;
  font-family: var(--icon-font);
  content: "\e28b";
  font-weight: 400;
  text-indent: 0;
  color: var(--yellow-color);
}
.rating-select p.stars a:hover ~ a::before {
  content: "\e28b";
  font-weight: 400;
}
.rating-select p.stars:hover a::before {
  content: "\e28b";
  font-weight: 700;
}
.rating-select p.stars.selected a.active::before {
  content: "\e28b";
  font-weight: 700;
}
.rating-select p.stars.selected a.active ~ a::before {
  content: "\e28b";
  font-weight: 400;
}
.rating-select p.stars.selected a:not(.active)::before {
  content: "\e28b";
  font-weight: 700;
}

/* Small devices */
@media (max-width: 767px) {
  .woocommerce-message,
  .woocommerce-info {
    font-size: 14px;
    line-height: 22px;
    padding: 10px 15px 10px 37px;
  }
  .woocommerce-message:before,
  .woocommerce-info:before {
    font-size: 16px;
    top: 10px;
    left: 15px;
  }
}
/*------------------- 4.00. Products  -------------------*/
.th-product {
  text-align: center;
  transition: all ease 0.4s;
  background-color: var(--white-color);
  box-shadow: 0px 3px 15px rgba(0, 0, 0, 0.05);
  border-radius: 20px;
  padding: 18px;
}
.th-product .product-title {
  font-size: 20px;
  margin: 4px 0 6px 0;
  font-weight: 500;
}
.th-product .product-title a {
  color: inherit;
}
.th-product .product-title a:hover {
  color: var(--theme-color);
}
.th-product .product-category {
  color: var(--body-color);
  margin-bottom: 0px;
  display: block;
}
.th-product .product-category:hover {
  color: var(--theme-color);
}
.th-product .category,
.th-product .product-tag {
  font-size: 14px;
  position: absolute;
  top: 10px;
  left: 10px;
  background-color: var(--theme-color);
  color: var(--white-color);
  padding: 0 12px;
  min-width: 60px;
  z-index: 3;
  border-radius: 30px;
  line-height: 24px;
}
.th-product .woocommerce-product-rating {
  display: inline-flex;
  align-items: center;
  gap: 5px;
  margin-top: 8px;
  margin-bottom: 5px;
}
.th-product .star-rating {
  margin: 0 auto 0 auto;
  width: 93px;
}
.th-product .price {
  display: block;
  color: var(--body-color);
  font-weight: 500;
  margin-bottom: -0.4em;
  font-family: var(--body-font);
}
.th-product .price del {
  margin-left: 10px;
  color: #a9a9a9;
}
.th-product .product-img {
  --space: 0px;
  background-color: var(--smoke-color2);
  overflow: hidden;
  position: relative;
  margin: 0 0 23px 0;
  text-align: center;
  z-index: 2;
  border-radius: 20px;
}
.th-product .product-img:before {
  --space: 0px;
  content: "";
  height: calc(100% - var(--space) * 2);
  width: calc(100% - var(--space) * 2);
  border-radius: inherit;
  position: absolute;
  top: var(--space);
  left: var(--space);
  background-color: var(--theme-color);
  z-index: 1;
  transform: scaleX(0);
  visibility: hidden;
  opacity: 0;
  transition: 0.4s ease-in-out;
}
.th-product .product-img img {
  width: 100%;
  transition: all ease 0.4s;
  transform: scale(1);
}
.th-product .actions {
  height: 100%;
  position: absolute;
  top: 5px;
  right: 10px;
  text-align: center;
  z-index: 3;
  opacity: 0;
  visibility: hidden;
  transition: 0.4s ease-in-out;
  display: grid;
  align-content: center;
  width: 40px;
}
.th-product .actions .icon-btn {
  --btn-size: 40px;
  font-size: 14px;
  border-color: var(--white-color);
  box-shadow: 0px 4px 20px rgba(0, 0, 0, 0.05);
  color: var(--white-color);
  border-radius: 99px;
  margin-bottom: 10px;
  box-shadow: 0px 3px 10px rgba(0, 0, 0, 0.08);
}
.th-product .actions .icon-btn:hover {
  color: var(--white-color);
  border-color: var(--theme-color);
}
.th-product .actions > * {
  margin: 0 0;
}
.th-product .actions > * > a {
  margin: 0;
}
.th-product .icon-btn {
  transform: translateX(30px);
  transition: 0.4s ease-in-out;
}
.th-product .tinv-wishlist .tinvwl_add_to_wishlist_button.tinvwl-icon-heart-plus.no-txt,
.th-product .tinv-wishlist .tinvwl_add_to_wishlist_button.tinvwl-icon-heart.no-txt {
  width: 40px;
  height: 40px;
  line-height: 40px;
  display: inline-block;
}
.th-product .tinv-wishlist .tinvwl_add_to_wishlist_button.tinvwl-icon-heart-plus.no-txt::before,
.th-product .tinv-wishlist .tinvwl_add_to_wishlist_button.tinvwl-icon-heart.no-txt::before {
  position: relative;
  top: 0;
  left: 0;
  line-height: inherit;
  margin: 0;
  font-size: 24px;
}
.th-product .tinv-wishlist a {
  display: inline-block;
  width: 40px;
  height: 40px;
  line-height: 40px;
  background-color: var(--white-color);
  color: var(--title-color);
  border-radius: 50%;
}
.th-product .tinv-wishlist a:hover {
  background-color: var(--theme-color);
  color: var(--white-color);
}
.th-product .add_to_cart_button.added {
  display: none;
}
.th-product .added_to_cart {
  display: block;
  width: 40px;
  height: 40px;
  line-height: 38px;
  background-color: var(--title-color);
  color: var(--white-color);
  font-size: 0;
  text-align: center;
  border-radius: 99px;
  border: none;
  margin-bottom: 10px;
}
.th-product .added_to_cart:after {
  content: "\f07a";
  position: relative;
  font-family: var(--icon-font);
  font-size: 16px;
  font-weight: 700;
}
.th-product .added_to_cart:hover {
  background-color: var(--theme-color);
  color: var(--white-color);
}
.th-product .action-btn {
  background-color: var(--white-color);
  font-size: 14px;
  font-family: var(--title-font);
  text-transform: uppercase;
  font-weight: bold;
  display: inline-block;
  padding: 13px 25px;
}
.th-product:hover .product-img img {
  transform: scale(1.1);
}
.th-product:hover .product-img:before {
  transform: scaleX(1);
  visibility: visible;
  opacity: 0.25;
}
.th-product:hover .actions {
  margin-top: 0;
  opacity: 1;
  visibility: visible;
}
.th-product:hover .icon-btn {
  transform: translateY(0);
}
.th-product.list-view {
  display: flex;
  text-align: left;
  height: 100%;
  padding: 10px;
}
.th-product.list-view .product-img {
  --space: 0px;
  width: 100%;
  width: 87px;
  margin: 0;
  border-radius: 10px;
}
.th-product.list-view .product-img:before {
  --space: 0px;
}
.th-product.list-view .product-category {
  font-size: 13px;
  line-height: 23px;
  margin-top: -3px;
}
.th-product.list-view .star-rating {
  margin: 5px 0 0 0;
  width: 93px;
  font-size: 12px;
}
.th-product.list-view .product-content {
  flex: 1;
  display: flex;
  align-items: center;
  flex-direction: column;
  justify-content: center;
  align-items: flex-start;
  padding: 0 4px 0 13px;
}
.th-product.list-view .actions {
  --icon-gap-x: 2px;
  width: 100%;
  right: 0;
  text-align: center;
  justify-content: center;
}
.th-product.list-view .actions .icon-btn {
  --btn-size: 36px;
  --btn-font-size: 14px;
  background-color: var(--theme-color2);
  color: var(--white-color);
  border: none;
}
.th-product.list-view .actions .icon-btn:hover {
  background-color: var(--title-color);
}
.th-product.list-view .tinv-wishlist .tinvwl_add_to_wishlist_button.tinvwl-icon-heart-plus.no-txt,
.th-product.list-view .tinv-wishlist .tinvwl_add_to_wishlist_button.tinvwl-icon-heart.no-txt {
  width: 35px;
  height: 35px;
  line-height: 35px;
}
.th-product.list-view .tinv-wishlist .tinvwl_add_to_wishlist_button.tinvwl-icon-heart-plus.no-txt::before,
.th-product.list-view .tinv-wishlist .tinvwl_add_to_wishlist_button.tinvwl-icon-heart.no-txt::before {
  font-size: 20px;
}
.th-product.list-view .tinv-wishlist a {
  width: 35px;
  height: 35px;
  line-height: 35px;
}
.th-product.list-view .added_to_cart {
  width: 35px;
  height: 35px;
  line-height: 35px;
}
.th-product.list-view .added_to_cart:after {
  font-size: 16px;
}
.th-product.list-view .tag {
  top: 8px;
  right: 8px;
  padding: 0px 15px;
}
.th-product.list-view .product-title {
  font-size: 14px;
  margin: 0;
}
.th-product.list-view .price {
  font-weight: 600;
  font-size: 13px;
  line-height: 23px;
  color: var(--theme-color);
}

.product-list-area {
  margin-bottom: -24px;
}

#productCarousel .slick-arrow {
  top: 37.5%;
}

.mfp-content {
  margin: 1.5rem auto;
}
.mfp-content .product-details-img {
  padding-top: 15px;
}
.mfp-content .product-about {
  padding-top: 20px;
  padding-bottom: 20px;
}
.mfp-content .container {
  position: relative;
}
.mfp-content .product-big-img {
  margin-top: 12px;
  margin-bottom: 12px;
}

.mfp-fade.mfp-bg {
  opacity: 0;
  transition: all 0.15s ease-out;
}

.mfp-fade.mfp-bg.mfp-ready {
  opacity: 0.8;
}

.mfp-fade.mfp-bg.mfp-removing {
  opacity: 0;
}

.mfp-fade.mfp-wrap .mfp-content {
  opacity: 0;
  transition: all 0.4s ease-out;
}

.mfp-fade.mfp-wrap.mfp-ready .mfp-content {
  opacity: 1;
}

.mfp-fade.mfp-wrap.mfp-removing .mfp-content {
  opacity: 0;
}

.woosq-popup {
  border-radius: 10px;
}
.woosq-popup .product_meta > span > a:after,
.woosq-popup .product_meta > span > span:after {
  display: none;
}

.woosq-product > .product .summary {
  display: grid;
  align-content: center;
}
.woosq-product .thumbnails .slick-list {
  display: block;
}
.woosq-product .thumbnails .slick-dots li button {
  width: 12px;
  height: 12px;
  transform: scale(1);
  background-color: var(--theme-color);
  opacity: 0.4;
}
.woosq-product .thumbnails .slick-dots li button:before {
  display: none;
}
.woosq-product .thumbnails .slick-dots li.slick-active button {
  opacity: 1;
}
.woosq-product .tinv-wraper {
  display: none;
}

.th-sort-bar {
  padding: 10px;
  margin: 0 0 24px 0;
  background-color: var(--smoke-color2);
  border-radius: 8px;
}
.th-sort-bar .row {
  --bs-gutter-x: 0;
  --bs-gutter-y: 15px;
}
.th-sort-bar select {
  height: 50px;
  border: 1px solid var(--th-border-color);
  background-color: var(--white-color);
  width: fit-content;
  min-width: 250px;
  font-size: 16px;
  margin: 0;
  color: var(--body-color);
}
.th-sort-bar .woocommerce-result-count {
  padding-left: 15px;
  margin-bottom: 0;
  color: var(--body-color);
}
.th-sort-bar .nav a {
  display: inline-block;
  height: 50px;
  width: 50px;
  line-height: 50px;
  border: 1px solid var(--th-border-color);
  background-color: var(--white-color);
  text-align: center;
  position: relative;
  font-family: var(--title-font);
  font-weight: 600;
  font-size: 16px;
  text-transform: capitalize;
  color: var(--body-color);
  margin: 0 0 0 10px;
  border-radius: 5px;
}
.th-sort-bar .nav a.active, .th-sort-bar .nav a:hover {
  color: var(--theme-color);
}

.product-thumb-area {
  position: relative;
  margin-right: 10px;
}
.product-thumb-area .product-thumb-tab {
  position: absolute;
  top: 0;
  left: 0;
}

.product-thumb-tab {
  --thumb: 120px;
  --gap: 10px;
  max-width: var(--thumb);
  margin-left: calc(0px - var(--thumb) / 2);
  position: relative;
  z-index: 3;
  display: grid;
  align-content: center;
  height: 100%;
}
.product-thumb-tab .tab-btn {
  background-color: var(--white-color);
  cursor: pointer;
  height: var(--thumb);
  width: var(--thumb);
  padding: var(--gap);
}
.product-thumb-tab .tab-btn img {
  max-width: 100%;
}
.product-thumb-tab .tab-btn:not(:last-of-type) {
  margin-bottom: 20px;
}
.product-thumb-tab .indicator {
  position: absolute;
  top: calc(var(--pos-y) + var(--gap));
  left: calc(var(--pos-x) + var(--gap));
  width: calc(var(--width-set) - var(--gap) * 2);
  height: calc(var(--height-set) - var(--gap) * 2);
  border: 2px solid var(--theme-color);
  pointer-events: none;
  transition: 0.4s ease-in-out;
}

.product-big-img {
  background-color: var(--smoke-color2);
  text-align: center;
  overflow: hidden;
  border-radius: 20px;
}
.product-big-img .img {
  width: 100%;
}
.product-big-img .img img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: 0.4s ease-in-out;
}
.product-big-img .slider-pagination {
  position: absolute;
  bottom: 40px;
  left: 0;
}
.product-big-img .slick-dots {
  margin: -11px 0 0 0 !important;
  transform: translateY(-30px);
  --border-color: #c0c0c0;
}

.img-magnifier-container {
  position: relative;
}

.img-magnifier-glass {
  position: absolute;
  box-shadow: inset 0 10px 18px rgba(0, 0, 0, 0.2);
  border-radius: 50%;
  cursor: none;
  width: 200px;
  height: 200px;
}

.magnify {
  position: relative;
  cursor: none;
}
.magnify .magnifier {
  height: 180px;
  width: 180px;
  position: absolute;
  z-index: 20;
  box-shadow: inset 0 10px 18px rgba(0, 0, 0, 0.2);
  border-radius: 50%;
  background-size: 700%;
  background-repeat: no-repeat;
  margin-left: -90px !important;
  margin-top: -90px !important;
  pointer-events: none;
  display: none;
}
.magnify .magnified {
  display: block;
  z-index: 10;
  margin: auto;
  width: 100%;
  height: 100%;
}
.magnify img {
  width: 100%;
  height: 100%;
}

.quantity {
  position: relative;
  display: inline-flex;
  vertical-align: middle;
}
.quantity > .screen-reader-text {
  display: inline-block;
  font-weight: 600;
  color: var(--title-color);
  font-family: var(--title-font);
  margin: 0;
  align-self: center;
  margin-right: 10px;
}
.quantity .qty-btn,
.quantity .qty-input {
  display: inline-block;
  width: 50px;
  height: 50px;
  border: none;
  border-right: none;
  background-color: transparent;
  padding: 0;
  border-radius: 0;
  text-align: center;
  color: var(--body-color);
  font-size: 18px;
  font-weight: 600;
}
.quantity .qty-btn:last-child,
.quantity .qty-input:last-child {
  border-right: none;
}
.quantity .qty-btn {
  font-size: 16px;
}

.product_meta {
  font-weight: 700;
  font-size: 16px;
  font-family: var(--body-font);
  margin: 35px 0 0 0;
}
.product_meta > span {
  display: block;
  margin-bottom: 5px;
  color: var(--title-color);
  font-weight: bold;
}
.product_meta > span:last-child {
  margin-bottom: 0;
}
.product_meta > span a {
  color: inherit;
}
.product_meta > span a:hover {
  color: var(--theme-color);
}
.product_meta > span > a,
.product_meta > span > span {
  position: relative;
  color: var(--body-color);
  font-weight: 400;
}
.product_meta > span > a:after,
.product_meta > span > span:after {
  content: ",";
  margin-right: 5px;
}
.product_meta > span > a:last-child:after,
.product_meta > span > span:last-child:after {
  display: none;
}
.product_meta > span > a:first-child,
.product_meta > span > span:first-child {
  margin-left: 7px;
}

.product-tab-style1 {
  border-bottom: 1px solid var(--smoke-color2);
  margin: 95px auto 40px auto;
  padding-bottom: 40px;
  justify-content: center;
  gap: 24px;
}
.product-tab-style1 .th-btn.active {
  background: var(--title-color);
  color: var(--white-color);
}

#productTabContent {
  margin-bottom: -10px;
}

#additional_information {
  margin-bottom: 40px;
}

.product-inner-list > ul {
  margin: 0;
  padding: 0;
  list-style-type: none;
}
.product-inner-list li {
  position: relative;
  padding-left: 15px;
}
.product-inner-list li:before {
  content: "-";
  position: absolute;
  left: 0;
}

.share-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--title-color);
  margin-bottom: 8px;
}
.share-title i {
  color: var(--theme-color);
}

.summary-content .product-title,
.product-about .product-title {
  margin: 0 0 14px 0;
}
.summary-content .product_title,
.product-about .product_title {
  margin-bottom: 20px !important;
}
.summary-content > .price,
.product-about > .price {
  font-family: var(--body-font);
  font-size: 20px;
  font-weight: 800;
  color: var(--title-color);
  display: block;
  max-width: fit-content;
  margin-bottom: 19px;
  margin-top: -0.2em;
}
.summary-content > .price del,
.product-about > .price del {
  color: var(--body-color);
  font-weight: 500;
  margin-left: 15px;
}
.summary-content .woocommerce-product-rating,
.summary-content .product-rating,
.product-about .woocommerce-product-rating,
.product-about .product-rating {
  display: inline-flex;
  gap: 5px;
  align-items: center;
  position: relative;
  top: 2px;
  font-size: 16px;
  line-height: 20px;
  padding: 0 0 0 0;
  margin: 0 0 0 0;
  margin-bottom: 22px;
}
.summary-content .woocommerce-product-rating .star-rating,
.summary-content .product-rating .star-rating,
.product-about .woocommerce-product-rating .star-rating,
.product-about .product-rating .star-rating {
  width: 80px;
  font-size: 12px;
  margin-right: 8px;
}
.summary-content .woocommerce-review-link,
.product-about .woocommerce-review-link {
  color: var(--body-color);
}
.summary-content .checklist,
.product-about .checklist {
  margin: 29px 0 29px 0;
}
.summary-content .actions,
.product-about .actions {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  margin: 35px 0 27px 0;
}
.summary-content .actions .th-btn .btn-icon,
.product-about .actions .th-btn .btn-icon {
  padding: 10.5px 15px 10.5px 15px;
}
.summary-content .actions .icon-btn,
.product-about .actions .icon-btn {
  border-color: var(--title-color);
  background-color: var(--title-color);
  color: var(--white-color);
  border-radius: 50%;
  --btn-size: 64px;
}
.summary-content .actions .icon-btn:hover,
.product-about .actions .icon-btn:hover {
  background-color: var(--theme-color);
  border-color: var(--theme-color);
}
.summary-content .share,
.product-about .share {
  margin-top: 25px;
}
.summary-content .th-social a,
.product-about .th-social a {
  --icon-size: 40px;
  line-height: 38px;
  font-size: 14px;
  border: 1px solid var(--th-border-color);
  border-radius: 0;
}
.summary-content .th-social a:hover,
.product-about .th-social a:hover {
  border-color: var(--theme-color);
}
.summary-content .tinv-wishlist .tinvwl_add_to_wishlist_button.tinvwl-icon-heart-plus.no-txt,
.summary-content .tinv-wishlist .tinvwl_add_to_wishlist_button.tinvwl-icon-heart.no-txt,
.product-about .tinv-wishlist .tinvwl_add_to_wishlist_button.tinvwl-icon-heart-plus.no-txt,
.product-about .tinv-wishlist .tinvwl_add_to_wishlist_button.tinvwl-icon-heart.no-txt {
  width: 56px;
  height: 56px;
  line-height: 54px;
  display: inline-block;
  border-radius: 99px;
  background-color: var(--theme-color);
  color: var(--white-color);
  border: none;
}
.summary-content .tinv-wishlist .tinvwl_add_to_wishlist_button.tinvwl-icon-heart-plus.no-txt::before,
.summary-content .tinv-wishlist .tinvwl_add_to_wishlist_button.tinvwl-icon-heart.no-txt::before,
.product-about .tinv-wishlist .tinvwl_add_to_wishlist_button.tinvwl-icon-heart-plus.no-txt::before,
.product-about .tinv-wishlist .tinvwl_add_to_wishlist_button.tinvwl-icon-heart.no-txt::before {
  position: relative;
  top: 0;
  left: 0;
  line-height: inherit;
  margin: 0;
  font-size: 24px;
}
.summary-content .tinv-wishlist .tinvwl_add_to_wishlist_button.tinvwl-icon-heart-plus.no-txt:hover,
.summary-content .tinv-wishlist .tinvwl_add_to_wishlist_button.tinvwl-icon-heart.no-txt:hover,
.product-about .tinv-wishlist .tinvwl_add_to_wishlist_button.tinvwl-icon-heart-plus.no-txt:hover,
.product-about .tinv-wishlist .tinvwl_add_to_wishlist_button.tinvwl-icon-heart.no-txt:hover {
  background-color: var(--title-color);
}
.summary-content .tinv-wishlist a,
.product-about .tinv-wishlist a {
  display: inline-block;
  width: 56px;
  height: 56px;
  line-height: 56px;
  border-radius: 0;
}
.summary-content .tinv-wishlist a:hover,
.product-about .tinv-wishlist a:hover {
  background-color: var(--theme-color);
  color: var(--white-color);
}
.summary-content .quantity,
.product-about .quantity {
  position: relative;
}
.summary-content .quantity:after,
.product-about .quantity:after {
  content: "";
  height: 100%;
  width: 1px;
  background-color: var(--th-border-color);
  position: absolute;
  top: 0;
  left: 65px;
}
.summary-content .quantity .qty-input,
.product-about .quantity .qty-input {
  width: 132px;
  height: 64px;
  border: 1px solid var(--th-border-color);
  background-color: transparent;
  color: var(--title-color);
  padding: 0 30px;
  text-align: left;
  font-weight: 500;
  border-radius: 50px;
}
.summary-content .quantity .qty-btn,
.product-about .quantity .qty-btn {
  color: var(--title-color);
  background-color: transparent;
  position: absolute;
  right: 30px;
  height: auto;
  width: auto;
  border: none;
  line-height: 16px;
}
.summary-content .quantity .quantity-minus,
.product-about .quantity .quantity-minus {
  bottom: 12px;
}
.summary-content .quantity .quantity-plus,
.product-about .quantity .quantity-plus {
  top: 12px;
}

.woocommerce-Reviews .th-post-comment {
  border-radius: 20px;
  border: 0 !important;
  padding: 40px !important;
  margin: 12px;
  background: var(--smoke-color2);
}
.woocommerce-Reviews .th-post-comment .text {
  margin-bottom: -0.5em;
}
.woocommerce-Reviews .th-post-comment .name {
  margin-bottom: 3px;
}
.woocommerce-Reviews .th-post-comment .commented-on {
  margin-bottom: 12px;
}
.woocommerce-Reviews .th-comments-wrap {
  padding: 0;
  margin-right: 0 !important;
  box-shadow: none;
  margin: 0;
}
.woocommerce-Reviews .th-comment-form {
  padding: 60px;
  background-color: var(--smoke-color2);
  box-shadow: none;
  margin-right: 0 !important;
  margin-top: 28px;
  margin-bottom: 40px;
  border-radius: 20px;
}
.woocommerce-Reviews .th-comment-form input,
.woocommerce-Reviews .th-comment-form .form-control {
  background-color: var(--white-color);
}
.woocommerce-Reviews .th-comment-form .blog-inner-title {
  margin-bottom: 10px;
}
.woocommerce-Reviews .comment-list {
  display: flex;
  gap: 0 24px;
  margin: -12px !important;
  margin-bottom: 28px !important;
}
.woocommerce-Reviews .comment-list li {
  width: 50%;
  /* Medium devices */
}
@media (max-width: 991px) {
  .woocommerce-Reviews .comment-list li {
    width: 100%;
  }
}

/* Large devices */
@media (max-width: 1199px) {
  .woocommerce-Reviews .th-post-comment {
    padding: 30px !important;
  }
}
/* Medium devices */
@media (max-width: 991px) {
  .woocommerce-Reviews .comment-list {
    grid-template-columns: repeat(1, 1fr);
  }
  .woocommerce-Reviews .th-comment-form {
    padding: 40px;
  }
}
/* Extra small devices */
@media (max-width: 575px) {
  .woocommerce-Reviews .th-comment-form {
    padding: 40px 20px;
  }
}
#description {
  margin-bottom: 30px;
}

.product-details .th-comments-wrap {
  margin-top: 0;
}
.product-details .border-title {
  position: relative;
  padding-bottom: 20px;
  margin-bottom: 40px;
}
.product-details .border-title:before {
  content: "";
  position: absolute;
  left: 0;
  bottom: 0;
  height: 2px;
  width: 80px;
  background-color: var(--theme-color);
}

.product-inner-title {
  font-size: 32px;
  border-bottom: 1px solid var(--th-border-color);
  padding: 0 0 7px 0;
  margin: 0 0 30px 0;
}

.related-product-wrapper {
  padding-top: 115px;
}

/* Medium Large devices */
@media (max-width: 1399px) {
  .th-product.list-view .product-img {
    max-width: 150px;
  }
}
/* Medium Large devices */
@media (max-width: 1299px) {
  .product-thumb-tab {
    --thumb: 100px;
    margin-left: -40px;
  }
  .product-thumb-area {
    margin-right: 0;
  }
}
/* Large devices */
@media (max-width: 1199px) {
  .th-sort-bar select {
    min-width: auto;
  }
  .product-tab-style1 {
    margin: 55px 0 40px 0;
  }
  .summary-content,
  .product-about {
    padding-left: 0;
  }
}
/* Medium devices */
@media (max-width: 991px) {
  .product-big-img {
    margin-bottom: 40px;
  }
  .product-thumb-tab {
    margin-left: -10px;
  }
  .th-product-box.list-view .product-img {
    max-width: 150px;
  }
  .th-sort-bar .row {
    --bs-gutter-x: 20px;
  }
  .th-sort-bar .nav a:last-child {
    margin-right: 0;
    padding-right: 0;
  }
  .th-sort-bar .nav a:last-child:before {
    display: none;
  }
  .woosq-product > .product .thumbnails {
    max-height: 400px;
    min-height: 200px;
    padding: 10px;
  }
}
/* Small devices */
@media (max-width: 767px) {
  .th-sort-bar {
    text-align: center;
    padding: 10px 15px 15px 15px;
  }
  .th-sort-bar .nav {
    justify-content: center;
  }
  .th-sort-bar select {
    margin: 0 auto;
  }
  .th-sort-bar .woocommerce-result-count {
    padding-left: 0;
  }
  .th-product-box.list-view .product-img {
    max-width: 130px;
  }
  .th-product-box.list-view .actions {
    --btn-size: 30px;
    --btn-font-size: 10px;
    --icon-gap-x: 2px;
  }
}
/* Extra small devices */
@media (max-width: 575px) {
  .product-about .actions {
    gap: 15px 15px;
  }
  .product-thumb-tab {
    --thumb: 74px;
    --gap: 6px;
  }
  .product-thumb-tab .tab-btn:not(:last-of-type) {
    margin-bottom: 10px;
  }
  .product-grid {
    max-width: 340px;
    margin-left: auto;
    margin-right: auto;
  }
}
/* Extra small devices */
@media (max-width: 375px) {
  .th-product.list-view .product-img {
    max-width: 130px;
  }
  .th-product.list-view .product-content {
    padding: 0 15px;
  }
}
/*------------------- 4.00. Cart  -------------------*/
.woocommerce-cart-form {
  text-align: center;
}

.cart_table {
  border: 1px solid #eaf0f2;
  margin-bottom: 45px;
}
.cart_table thead {
  background-color: #ecf0f1;
}
.cart_table thead th {
  border: none !important;
}
.cart_table td:before,
.cart_table th {
  font-family: var(--title-font);
  color: var(--title-color);
  font-weight: 600;
  border: none;
  padding: 27px 15px;
}
.cart_table td:before {
  content: attr(data-title);
  position: absolute;
  left: 15px;
  top: 50%;
  vertical-align: top;
  padding: 0;
  transform: translateY(-50%);
  display: none;
}
.cart_table td {
  border: none;
  border-bottom: 1px solid #f3f3f3;
  color: #8b8b8b;
  padding: 20px 10px;
  position: relative;
  vertical-align: middle;
}
.cart_table .product-quantity {
  color: var(--title-color);
}
.cart_table .product-quantity input {
  position: relative;
  top: -2px;
}
.cart_table .cart-productname {
  font-weight: 400;
  font-family: var(--body-font);
  color: var(--body-color);
}
.cart_table .cart-productimage {
  display: inline-block;
  border: 2px solid var(--smoke-color);
}
.cart_table .remove {
  color: var(--theme-color);
  font-size: 18px;
}
.cart_table .quantity {
  display: inline-flex;
  align-items: center;
}
.cart_table td.product-quantity {
  min-width: 155px;
}
.cart_table .qty-btn {
  border: 2px solid var(--smoke-color) !important;
  background-color: transparent;
  color: #b8c6d0;
  padding: 0;
  width: 30px;
  height: 30px;
  line-height: 28px;
  font-size: 14px;
  border-radius: 4px;
}
.cart_table .qty-btn:hover {
  background-color: var(--theme-color);
  color: var(--white-color);
}
.cart_table .qty-input {
  vertical-align: middle;
  border: 2px solid var(--smoke-color);
  width: 60px;
  height: 30px;
  font-size: 14px;
  text-align: center;
  color: var(--title-color);
  font-weight: 700;
  margin: 0 3px;
  border-radius: 4px;
  padding: 0;
  /* Firefox */
}
.cart_table .qty-input::-moz-placeholder {
  color: var(--title-color);
}
.cart_table .qty-input::-webkit-input-placeholder {
  color: var(--title-color);
}
.cart_table .qty-input:-ms-input-placeholder {
  color: var(--title-color);
}
.cart_table .qty-input::placeholder {
  color: var(--title-color);
}
.cart_table .qty-input::-webkit-outer-spin-button, .cart_table .qty-input::-webkit-inner-spin-button {
  -webkit-appearance: none;
  margin: 0;
}
.cart_table .qty-input[type=number] {
  -moz-appearance: textfield;
}
.cart_table .actions {
  text-align: right;
  vertical-align: middle;
  padding-left: 20px;
  padding-right: 20px;
}
.cart_table .actions > .th-btn {
  padding: 20px 28px;
  margin-right: 15px;
}
.cart_table .actions > .th-btn:last-child {
  margin-right: 0;
}
.cart_table .th-cart-coupon {
  float: left;
  margin: 0;
  width: 455px;
  max-width: 100%;
  display: flex;
}
.cart_table .th-cart-coupon input {
  width: calc(100% - 200px);
  margin-right: 10px;
}
.cart_table .th-cart-coupon .th-btn {
  padding: 20px 25px;
  width: max-content;
}

.cart_totals {
  border: 1px solid #ecf0f1;
}
.cart_totals th,
.cart_totals td {
  vertical-align: top;
  padding: 20px 20px;
  border: none;
  border-bottom: 1px solid #ecf0f1;
  font-size: 14px;
  color: var(--title-color);
  width: 55%;
}
.cart_totals th:first-child,
.cart_totals td:first-child {
  width: 45%;
  background-color: #f9fbfb;
  font-weight: 700;
  font-size: 14px;
  color: #333333;
}
.cart_totals .shipping-calculator-button {
  display: inline-block;
  border-bottom: 1px solid;
  color: var(--title-color);
  font-weight: 700;
}
.cart_totals .shipping-calculator-button:hover {
  color: var(--theme-color);
}
.cart_totals .woocommerce-shipping-destination {
  margin-bottom: 10px;
}
.cart_totals .woocommerce-shipping-methods {
  margin-bottom: 0;
}
.cart_totals .shipping-calculator-form {
  display: none;
}
.cart_totals .shipping-calculator-form p:first-child {
  margin-top: 20px;
}
.cart_totals .shipping-calculator-form p:last-child {
  margin-bottom: 0;
}
.cart_totals .amount {
  font-weight: 700;
}
.cart_totals .order-total .amount {
  color: var(--theme-color);
}

.empty-notice {
  margin: 40px auto;
}

/* Medium devices */
@media (max-width: 991px) {
  .cart_table th {
    padding: 23px 8px;
    font-size: 14px;
  }
  .cart_table .cart-productname {
    font-size: 14px;
  }
  .cart_table .th-cart-coupon {
    width: 100%;
    margin-bottom: 20px;
    justify-content: center;
  }
  .cart_table .actions {
    text-align: center;
  }
  .cart_table .cart-productimage {
    max-width: 100px;
  }
}
/* Small devices */
@media (max-width: 767px) {
  .cart_table {
    text-align: left;
    min-width: auto;
    border-collapse: separate;
    border-spacing: 0 20px;
    border: none;
  }
  .cart_table thead {
    display: none;
  }
  .cart_table td {
    padding: 15px;
    display: block;
    width: 100%;
    padding-left: 25%;
    text-align: right;
    border: 1px solid #f3f3f3;
    border-bottom: none;
  }
  .cart_table td::before {
    display: block;
  }
  .cart_table td:last-child {
    border-bottom: 1px solid #f3f3f3;
  }
  .cart_table td.actions {
    padding-left: 15px;
    text-align: center;
  }
  .cart_table td.actions > .th-btn {
    margin-top: 10px;
    margin-right: 0;
    display: block;
    width: max-content;
    margin-left: auto;
    margin-right: auto;
  }
  .cart_table td.actions > .th-btn:last-child {
    margin-right: auto;
  }
  .cart_table .th-cart-coupon {
    width: 100%;
    text-align: center;
    float: none;
    justify-content: center;
    display: block;
    padding-bottom: 10px;
  }
  .cart_table .th-cart-coupon input {
    width: 100%;
    margin-bottom: 10px;
  }
  .cart_totals th,
  .cart_totals td {
    padding: 15px 10px;
  }
  .cart_totals th:first-child,
  .cart_totals td:first-child {
    width: 17%;
    line-height: 1.4;
  }
}
/*------------------- 4.00. Checkout  -------------------*/
.woocommerce-checkout .form-group,
.woocommerce-checkout .form-row {
  margin-bottom: 0;
}
.woocommerce-checkout .form-select,
.woocommerce-checkout .select2-container,
.woocommerce-checkout .form-control {
  margin-bottom: 0;
}
.woocommerce-checkout .select2-container--open .select2-dropdown--below {
  margin-top: 0;
}
.woocommerce-checkout .select2-container--open .select2-dropdown--above {
  position: relative;
  bottom: 0;
}
.woocommerce-checkout .select2-dropdown {
  border: none;
  border-top: none;
}
.woocommerce-checkout .select2-container--default .select2-selection--single {
  border-radius: 27px;
}
.woocommerce-checkout .select2-container--default .select2-selection--single .select2-selection__rendered,
.woocommerce-checkout .select2-container--default .select2-selection--single .form-control:focus {
  color: var(--body-color);
}
.woocommerce-checkout .shipping_address {
  margin-top: -3px;
}

.select2-container--default .select2-search--dropdown .select2-search__field {
  border: 1px solid transparent;
  padding: 0;
}

.woocommerce-form-login select,
.woocommerce-form-login .form-select,
.woocommerce-form-login .form-control,
.woocommerce-form-login .select2,
.woocommerce-form-login .select2-container,
.woocommerce-form-coupon select,
.woocommerce-form-coupon .form-select,
.woocommerce-form-coupon .form-control,
.woocommerce-form-coupon .select2,
.woocommerce-form-coupon .select2-container,
.woocommerce-checkout select,
.woocommerce-checkout .form-select,
.woocommerce-checkout .form-control,
.woocommerce-checkout .select2,
.woocommerce-checkout .select2-container {
  margin-bottom: var(--bs-gutter-x);
}

#ship-to-different-address {
  margin-top: 15px;
}

.select2-container--default .select2-selection--single {
  height: 56px;
  border: 1px solid transparent;
  background-color: var(--smoke-color2);
  border-radius: 27px;
}

.select2-container--default .select2-selection--single .select2-selection__rendered {
  line-height: 56px;
  padding-left: 25px;
  padding-right: 25px;
}

.woocommerce-billing-fields .form-row {
  margin-bottom: 0;
}

.select2-container--default .select2-selection--single .select2-selection__arrow b:before {
  content: "\f107";
  font-family: var(--icon-font);
}

.select2-container--default .select2-selection--single .select2-selection__arrow b {
  margin: 0;
  border: none;
  top: 0;
}

.select2-container--default .select2-selection--single .select2-selection__arrow {
  height: 56px;
  line-height: 56px;
  margin-right: 30px;
}

span.select2-selection.select2-selection--single:focus {
  outline: none;
}

.checkout-ordertable th,
.checkout-ordertable td {
  border: 1px solid #ededed;
  text-align: right;
  padding: 5px 20px;
  vertical-align: top;
  font-size: 14px;
  font-weight: 600;
  color: #2c3e50;
}
.checkout-ordertable th {
  font-weight: 600;
  text-align: left;
}
.checkout-ordertable ul {
  margin: 0;
  padding: 0;
}
.checkout-ordertable .order-total .amount {
  color: var(--theme-color);
}
.checkout-ordertable input[type=hidden] ~ label {
  color: var(--theme-color);
}

.woocommerce-checkout .form-group input:not(:last-child) {
  margin-bottom: var(--bs-gutter-x);
}

.woocommerce-checkout-payment {
  text-align: left;
}
.woocommerce-checkout-payment ul {
  margin: 0;
  padding: 0;
  list-style-type: none;
}
.woocommerce-checkout-payment ul li {
  padding-top: 12px;
  border-bottom: 1px solid #d8d8d8;
  border-radius: 0;
  font-size: 16px;
}
.woocommerce-checkout-payment ul input[type=radio] ~ label {
  margin-bottom: 17px;
  color: var(--body-color);
}
.woocommerce-checkout-payment ul input[type=radio] ~ label img {
  margin-bottom: -2px;
  margin-left: 10px;
}
.woocommerce-checkout-payment .place-order {
  padding-top: 30px;
}
.woocommerce-checkout-payment .payment_box {
  color: #a1b1bc;
  background-color: #ecf0f1;
  border: 1px solid #d8d8d8;
  border-bottom: none;
  font-size: 14px;
  padding: 10px 20px;
  border-radius: 4px;
  display: none;
}
.woocommerce-checkout-payment .payment_box p {
  margin: 0;
}

.th-checkout-wrapper form.woocommerce-form {
  margin-bottom: 30px;
}

/* Small devices */
@media (max-width: 767px) {
  tfoot.checkout-ordertable th {
    display: none;
  }
  .woocommerce-checkout-payment ul input[type=radio] ~ label img {
    max-width: 150px;
  }
  .checkout-ordertable th,
  .checkout-ordertable td {
    padding: 5px 20px 5px 60px;
  }
}
/*------------------- 4.00. Wishlist  -------------------*/
.tinv-wishlist input[type=checkbox] {
  display: inline-block;
  opacity: 1;
  visibility: visible;
  vertical-align: middle;
  width: auto;
  height: auto;
}
.tinv-wishlist .tinv-header {
  margin-top: -0.8rem;
  text-transform: capitalize;
}
.tinv-wishlist .cart-empty {
  padding: 14px 25px;
  font-weight: 700;
  font-size: 14px;
  padding-left: 45px;
  border-radius: 8px;
}
.tinv-wishlist p.return-to-shop .button {
  display: inline-block;
  background-color: var(--theme-color);
  color: #fff;
  font-size: 14px;
  padding: 10px 25px;
  margin-top: 10px;
  font-weight: 700;
}
.tinv-wishlist p.return-to-shop .button:hover {
  background-color: var(--title-color);
  color: var(--white-color);
}
.tinv-wishlist table {
  border: none;
}
.tinv-wishlist table th {
  color: var(--title-color);
}
.tinv-wishlist table td,
.tinv-wishlist table th {
  padding: 15.3px 10px;
  border-bottom: 1px solid var(--th-border-color);
  text-align: center;
}
.tinv-wishlist table thead {
  background-color: var(--smoke-color);
}
.tinv-wishlist .product-cb,
.tinv-wishlist .product-remove {
  width: 40px;
  text-align: center;
}
.tinv-wishlist .product-thumbnail {
  width: 110px;
}
.tinv-wishlist .stock.in-stock {
  margin-bottom: 0;
}
.tinv-wishlist ins {
  text-decoration: none;
}
.tinv-wishlist .product-remove button {
  border: none;
  height: 22px;
  width: 22px;
  text-align: center;
  font-size: 12px;
  line-height: 22px;
  border-radius: 0;
  padding-top: 0;
}
.tinv-wishlist .product-remove button i {
  line-height: 22px;
  font-size: 16px;
}
.tinv-wishlist .tinvwl-mobile {
  display: none;
}
.tinv-wishlist .social-buttons {
  display: flex;
  max-width: 295px;
  margin-left: auto;
  align-items: center;
}
.tinv-wishlist .social-buttons ul {
  padding-left: 0;
  margin-bottom: 0;
  margin-left: auto;
  display: flex;
  gap: 6px;
}
.tinv-wishlist table.tinvwl-table-manage-list {
  font-size: 16px;
}
.tinv-wishlist .product-stock .stock {
  display: block;
}
.tinv-wishlist .product-stock span {
  display: inline;
}
.tinv-wishlist .product-stock i {
  margin-right: 5px;
}
.tinv-wishlist .tinv-modal .icon_big_times {
  margin-bottom: 5px;
  color: var(--theme-color);
}
.tinv-wishlist button.button {
  border: none;
  height: 38px;
  line-height: 38px;
  font-size: 14px;
  font-weight: 600;
  background-color: var(--theme-color);
  color: #fff;
  padding: 1px 15px;
  min-width: 140px;
}
.tinv-wishlist button.button i {
  font-size: 18px !important;
  margin-right: 3px !important;
}
.tinv-wishlist button.button:hover {
  background-color: var(--title-color);
  color: #fff;
}
.tinv-wishlist .tinvwl-buttons-group button i {
  font-size: 14px;
}
.tinv-wishlist th,
.tinv-wishlist td.product-name {
  font-size: 16px;
  font-weight: 700;
  font-family: var(--title-font);
}
.tinv-wishlist td.product-name a {
  color: var(--body-color);
}
.tinv-wishlist td.product-name a:hover {
  color: var(--theme-color);
}
.tinv-wishlist td.product-price del {
  margin-left: 8px;
  font-size: 0.9em;
}
.tinv-wishlist .social-buttons > span {
  font-weight: 700;
  margin-right: 10px;
  font-family: var(--title-font);
  color: var(--title-color);
}
.tinv-wishlist .social-buttons li {
  display: inline-block;
  margin-right: 0;
}
.tinv-wishlist .social-buttons li a.social {
  background-color: var(--theme-color);
  color: #fff;
  width: 30px;
  height: 30px;
  line-height: 30px;
  font-size: 14px;
  display: inline-block;
  text-align: center;
  border-radius: 50px;
  margin-left: 3px;
}
.tinv-wishlist .social-buttons li a.social:first-child {
  margin-left: 0;
}
.tinv-wishlist .social-buttons li a.social i {
  line-height: inherit;
}
.tinv-wishlist .social-buttons li a.social:hover {
  background-color: var(--title-color);
  color: var(--white-color);
}

/* Medium devices */
@media (max-width: 991px) {
  .tinvwl-full {
    display: none;
  }
  .tinv-wishlist .tinvwl-mobile {
    display: block;
  }
  .tinvwl-txt {
    display: inline-block !important;
  }
  .product-stock {
    width: 40px;
    text-align: center;
  }
}
/* Small devices */
@media (max-width: 767px) {
  .tinv-wishlist table {
    table-layout: fixed;
    border-bottom: 1px solid var(--th-border-color);
  }
  .tinv-wishlist table.tinvwl-table-manage-list tbody td.product-remove,
  .tinv-wishlist table.tinvwl-table-manage-list thead th:not(.product-name) {
    display: none;
  }
  .tinv-wishlist table td,
  .tinv-wishlist table th {
    border: 1px solid var(--th-border-color);
  }
  .tinv-wishlist table.tinvwl-table-manage-list tbody td {
    display: block;
    width: 100% !important;
    text-align: center;
  }
  .product-name {
    text-align: center;
  }
  .tinv-wishlist table td,
  .tinv-wishlist table th {
    border-bottom: none;
  }
  .tinv-wishlist table tfoot {
    border-bottom: 1px solid var(--th-border-color);
  }
  .tinv-wishlist .social-buttons {
    max-width: 100%;
    margin-left: unset;
    flex-direction: column;
  }
  .tinv-wishlist .social-buttons ul {
    margin-left: unset;
    margin-top: 5px;
  }
  .tinvwl-txt {
    display: inline-block !important;
  }
}
/*------------------- 4.00. Contact  -------------------*/
/* Contact Area 1 ---------------------------------- */
.contact-thumb1-1 {
  position: absolute;
  right: 0;
  top: 0;
  bottom: 0;
  width: 45%;
  /* Large devices */
  /* Medium devices */
}
.contact-thumb1-1 img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
@media (max-width: 1199px) {
  .contact-thumb1-1 {
    position: relative;
    width: 100%;
    top: -150px;
  }
}
@media (max-width: 991px) {
  .contact-thumb1-1 {
    top: -80px;
  }
}

.contact-form.style-border {
  border: 1px solid var(--th-border-color);
  border-radius: 50px;
  padding: 60px;
  /* Medium devices */
  /* Extra small devices */
}
@media (max-width: 991px) {
  .contact-form.style-border {
    border-radius: 40px;
    padding: 40px;
  }
}
@media (max-width: 575px) {
  .contact-form.style-border {
    padding: 30px;
  }
}

/* Contact Area 2 ---------------------------------- */
.contact-title-wrap {
  max-width: 713px;
}

.contact-thumb2-1 {
  /* Large devices */
}
@media (max-width: 1199px) {
  .contact-thumb2-1 {
    display: none;
  }
}

.contact-form-v2 {
  background: var(--title-color);
  padding: 56px;
  /* Extra small devices */
}
.contact-form-v2 .title-area .sub-title {
  font-size: 18px;
  padding-bottom: 0;
  margin-bottom: 15px;
}
.contact-form-v2 .title-area .title {
  font-size: 40px;
  /* Large devices */
  /* Small devices */
  /* Large devices */
}
@media (max-width: 1199px) {
  .contact-form-v2 .title-area .title {
    font-size: 36px;
  }
}
@media (max-width: 767px) {
  .contact-form-v2 .title-area .title {
    font-size: 34px;
  }
}
@media (max-width: 1199px) {
  .contact-form-v2 .title-area .title {
    font-size: 28px;
  }
}
@media (max-width: 575px) {
  .contact-form-v2 {
    padding: 40px 30px;
  }
  .contact-form-v2 .title-area .sub-title {
    font-size: 16px;
  }
}

.contact-info {
  display: flex;
  gap: 32px;
  align-items: center;
  background: var(--title-color);
  padding: 32px 24px;
  /* Medium devices */
  /* Extra small devices */
}
.contact-info .box-icon {
  flex: none;
  border: 1px solid rgba(87, 88, 95, 0.5);
  align-self: self-start;
  position: relative;
  height: 80px;
  width: 80px;
  justify-content: center;
  display: flex;
  align-items: center;
  z-index: 1;
}
.contact-info .box-icon:after {
  content: "";
  position: absolute;
  inset: 7px;
  background: var(--black-color2);
  z-index: -1;
}
.contact-info .box-title {
  color: var(--white-color);
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 0;
}
.contact-info .box-text {
  font-size: 16px;
  color: var(--light-color);
  margin-top: 10px;
  line-height: 1.4;
}
.contact-info .box-text a {
  color: var(--light-color);
}
.contact-info .box-text a:hover {
  color: var(--theme-color);
}
@media (max-width: 991px) {
  .contact-info .box-title {
    font-size: 24px;
  }
}
@media (max-width: 575px) {
  .contact-info {
    display: block;
  }
  .contact-info .box-icon {
    margin-bottom: 25px;
  }
  .contact-info .box-text {
    margin-bottom: -0.3em;
  }
  .contact-info .box-title {
    font-size: 24px;
  }
}

.contact-info-wrap2 {
  display: flex;
  justify-content: space-between;
  background: var(--title-color);
  flex-wrap: wrap;
  gap: 30px 30px;
  padding: 40px;
  margin: 0 -40px;
  margin-top: -160px;
  position: relative;
  z-index: 1;
  /* Extra large devices */
  /* Large devices */
  /* Medium devices */
  /* Extra small devices */
}
.contact-info-wrap2 .contact-info {
  width: calc(25% - 30px);
  padding: 0;
}
@media (max-width: 1500px) {
  .contact-info-wrap2 {
    margin: -160px 0 0;
  }
  .contact-info-wrap2 .contact-info {
    width: calc(50% - 30px);
  }
}
@media (max-width: 1199px) {
  .contact-info-wrap2 {
    margin-top: 60px;
  }
}
@media (max-width: 991px) {
  .contact-info-wrap2 .contact-info {
    width: 100%;
  }
}
@media (max-width: 575px) {
  .contact-info-wrap2 {
    gap: 40px 30px;
    padding: 40px 30px;
    margin-top: 40px;
  }
}

/* Contact Area 4 ---------------------------------- */
.contact-form-v3 {
  background: var(--title-color);
  padding: 48px;
  border: 1px solid var(--body-color);
  border-radius: 8px;
  /* Extra small devices */
}
.contact-form-v3 .title-area .sub-title {
  font-size: 18px;
  padding-bottom: 0;
  margin-bottom: 15px;
}
.contact-form-v3 .title-area .title {
  font-size: 40px;
  /* Large devices */
  /* Small devices */
  /* Large devices */
}
@media (max-width: 1199px) {
  .contact-form-v3 .title-area .title {
    font-size: 36px;
  }
}
@media (max-width: 767px) {
  .contact-form-v3 .title-area .title {
    font-size: 34px;
  }
}
@media (max-width: 1199px) {
  .contact-form-v3 .title-area .title {
    font-size: 28px;
  }
}
@media (max-width: 575px) {
  .contact-form-v3 {
    padding: 40px 30px;
  }
  .contact-form-v3 .title-area .sub-title {
    font-size: 16px;
  }
}

.contact-info.style2 {
  background: transparent;
  padding: 0;
  gap: 24px;
}
.contact-info.style2 .box-icon {
  border: 1px solid var(--white-color);
  border-radius: 8px;
}
.contact-info.style2 .box-icon:after {
  display: none;
}

/* Team Details Contact Form ---------------------------------- */
.contact-form-v4 {
  padding: 56px;
  /* Extra small devices */
}
@media (max-width: 575px) {
  .contact-form-v4 {
    padding: 40px 30px;
  }
}

/* Contact Page ---------------------------------- */
.contact-page-v1 {
  position: relative;
}
.contact-page-v1 .contact-map {
  position: absolute;
  inset: 0;
  z-index: -1;
}
.contact-page-v1 .contact-map iframe {
  height: 100%;
}

.contact-page-contact-form-wrap {
  display: flex;
  background: var(--smoke-color);
  border-radius: 16px;
  overflow: hidden;
  /* Medium devices */
  /* Extra small devices */
}
.contact-page-contact-form-wrap .contact-form-wrap {
  flex: 1;
  align-self: center;
  padding: 56px;
}
.contact-page-contact-form-wrap .contact-page-thumb1-1 {
  flex: 1;
}
.contact-page-contact-form-wrap .contact-page-thumb1-1 img {
  height: 100%;
  object-fit: cover;
}
@media (max-width: 991px) {
  .contact-page-contact-form-wrap {
    display: block;
  }
}
@media (max-width: 575px) {
  .contact-page-contact-form-wrap .contact-form-wrap {
    padding: 40px 30px;
  }
}

/*------------------- 4.00. About  -------------------*/
/* About 1 ---------------------------------- */
.about-wrap1 {
  /* Extra large devices */
}
.about-wrap1 .btn-wrap {
  gap: 24px 30px;
}
@media (max-width: 1500px) {
  .about-wrap1 .checklist.list-two-column ul {
    grid-template-columns: auto;
  }
}
.about-wrap1 .about-feature-grid {
  margin-left: 20px;
}

.about-profile {
  display: flex;
  gap: 16px;
  align-items: center;
}
.about-profile .avater {
  flex: none;
}
.about-profile .avater img {
  border-radius: 50%;
  overflow: hidden;
}
.about-profile .about-profile-name {
  font-size: 18px;
  font-weight: 500;
  margin-bottom: 0;
  color: var(--theme-color);
  margin-top: -0.2em;
}
.about-profile .desig {
  font-size: 16px;
  font-weight: 400;
  color: var(--title-color);
  margin-bottom: -0.4em;
}

.img-box1 .img1 {
  border-radius: 24px;
  overflow: hidden;
}
.img-box1 .img1 img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.about-feature-grid {
  display: flex;
  gap: 30px;
  align-items: center;
}
.about-feature-grid:not(:last-child) {
  margin-bottom: 40px;
  padding-bottom: 40px;
  border-bottom: 1px solid var(--light-color);
}
.about-feature-grid .box-icon {
  flex: none;
  height: 84px;
  width: 84px;
  text-align: center;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border-right: 1px solid var(--light-color);
  padding-right: 30px;
}
.about-feature-grid .box-icon .color-masking {
  transition: 0.4s;
}
.about-feature-grid .box-title {
  margin-bottom: -0.12em;
  font-size: 40px;
  font-weight: 600;
}
.about-feature-grid .box-text {
  margin-bottom: -0.5em;
}
.about-feature-grid:hover .box-icon .color-masking {
  transform: rotateY(180deg);
}

/* About 2 ---------------------------------- */
.img-box2 {
  position: relative;
  display: inline-block;
}
.img-box2 .about-info-box2-1 {
  left: 0;
  top: 40px;
}
.img-box2 .about-info-box2-2 {
  bottom: 40px;
  right: 0;
}
.img-box2 .about-bg-shape2-3,
.img-box2 .about-bg-shape2-2,
.img-box2 .about-bg-shape2-1 {
  position: absolute;
  top: 0;
  right: 65px;
  z-index: -1;
}
.img-box2 .about-bg-shape2-2 {
  top: auto;
  right: auto;
  bottom: 100px;
  left: -100px;
}
.img-box2 .about-bg-shape2-3 {
  top: auto;
  right: 65px;
  bottom: 60px;
}

.about-info-box2-1,
.about-info-box2-2 {
  background: rgba(19, 24, 43, 0.7);
  border: 1px solid var(--light-color);
  backdrop-filter: blur(5px);
  display: inline-block;
  position: absolute;
  text-align: center;
  padding: 30px 35px;
  z-index: 2;
  /* Extra small devices */
}
.about-info-box2-1 .box-icon,
.about-info-box2-2 .box-icon {
  margin-bottom: 16px;
  transition: 0.4s;
}
.about-info-box2-1 .box-title,
.about-info-box2-2 .box-title {
  font-size: 40px;
  font-weight: 600;
  margin-bottom: -0.32em;
}
.about-info-box2-1 .box-text,
.about-info-box2-2 .box-text {
  font-size: 18px;
  font-weight: 400;
  font-family: var(--title-font);
}
.about-info-box2-1:hover .box-icon,
.about-info-box2-2:hover .box-icon {
  transform: rotateY(180deg);
}
@media (max-width: 575px) {
  .about-info-box2-1,
  .about-info-box2-2 {
    position: initial;
    animation: none !important;
    display: block;
    margin-top: 20px;
  }
}

.about-tab {
  border: 0;
  gap: 15px 0;
  /* Extra small devices */
}
.about-tab .nav-item .nav-link {
  font-size: 18px;
  font-weight: 600;
  font-family: var(--title-font);
  color: var(--light-color);
  background: var(--black-color2);
  border-radius: 0;
  padding: 0 40px;
  line-height: 60px;
  border: 0;
  position: relative;
  margin: 0;
  /* Small devices */
}
.about-tab .nav-item .nav-link:after {
  content: "";
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 1px;
  background: currentColor;
  transform-origin: right top;
  transform: scale(0, 1);
  transition: transform 0.4s cubic-bezier(0.74, 0.72, 0.27, 0.24);
}
.about-tab .nav-item .nav-link.active {
  background: transparent;
  color: var(--theme-color);
}
.about-tab .nav-item .nav-link.active:after {
  transform-origin: left top;
  transform: scale(1, 1);
}
@media (max-width: 767px) {
  .about-tab .nav-item .nav-link {
    padding: 0 30px;
  }
}
@media (max-width: 575px) {
  .about-tab .nav-item {
    width: 100%;
  }
  .about-tab .nav-item .nav-link {
    width: 100%;
  }
  .about-tab .nav-item .nav-link:after {
    display: none;
  }
  .about-tab .nav-item .nav-link.active {
    background: var(--theme-color);
    color: var(--title-color);
  }
}

/* About 3 ---------------------------------- */
.img-box3 {
  text-align: center;
  position: relative;
  z-index: 1;
  /* Large devices */
}
.img-box3 .img1 {
  display: inline-block;
}
.img-box3 .img1 img {
  width: 100%;
}
.img-box3 .about-shape3-1 {
  position: absolute;
  bottom: -30px;
  left: -30px;
  /* Medium Large devices */
}
@media (max-width: 1399px) {
  .img-box3 .about-shape3-1 {
    top: -30px;
    right: -50px;
    bottom: auto;
    left: auto;
  }
}
@media (max-width: 1199px) {
  .img-box3 {
    display: inline-block;
  }
}

/* About 4 ---------------------------------- */
.about-bg-shape4-1 {
  /* Extra large devices */
  /* Medium Large devices */
}
@media (max-width: 1500px) {
  .about-bg-shape4-1 {
    right: 5% !important;
  }
}
@media (max-width: 1299px) {
  .about-bg-shape4-1 {
    display: none !important;
  }
}

.about-wrap4 {
  /* Medium Large devices */
  /* Large devices */
  /* Extra small devices */
}
.about-wrap4 .title-area {
  max-width: 800px;
}
@media (max-width: 1299px) {
  .about-wrap4 .checklist.list-two-column ul {
    grid-template-columns: auto;
  }
}
@media (max-width: 1199px) {
  .about-wrap4 .checklist.list-two-column ul {
    grid-template-columns: auto auto;
  }
}
@media (max-width: 575px) {
  .about-wrap4 .checklist.list-two-column ul {
    grid-template-columns: auto;
  }
}

.img-box4 {
  position: relative;
  z-index: 1;
}
.img-box4 .about-shape4-1 {
  display: inline-block;
  position: absolute;
  left: -65px;
  bottom: 10px;
  z-index: -1;
}
.img-box4 .about-shape4-2 {
  display: inline-block;
  position: absolute;
  left: -80px;
  top: 6px;
  --theme-color: #3282FB;
}

.img-box4-2 {
  position: relative;
  padding-right: 75px;
  /* Medium devices */
}
.img-box4-2 .img1 img {
  width: 100%;
}
.img-box4-2 .img2 {
  position: absolute;
  right: 0;
  bottom: 16px;
  z-index: 2;
  /* Extra large devices */
  /* Medium Large devices */
}
@media (max-width: 1500px) {
  .img-box4-2 .img2 {
    width: 150px;
  }
}
@media (max-width: 1399px) {
  .img-box4-2 .img2 {
    width: 120px;
  }
}
.img-box4-2 .about-shape4-1 {
  position: absolute;
  top: 70px;
  right: 0;
}
@media (max-width: 991px) {
  .img-box4-2 {
    display: inline-block;
  }
}

.about-tab.style2 {
  background: var(--smoke-color);
  border-radius: 8px;
  padding: 24px;
  gap: 24px;
  display: inline-flex;
}
.about-tab.style2 .nav-item .nav-link {
  border: 1px solid var(--light-color);
  border-radius: 4px;
  font-size: 18px;
  font-weight: 400;
  color: var(--body-color);
  line-height: 45px;
  padding: 0 16px;
  overflow: hidden;
  background: transparent;
  z-index: 1;
}
.about-tab.style2 .nav-item .nav-link:after {
  width: 0;
  height: 100%;
  background: var(--theme-color);
  z-index: -1;
}
.about-tab.style2 .nav-item .nav-link.active {
  color: var(--title-color);
  border-color: var(--theme-color);
  /* Extra small devices */
}
.about-tab.style2 .nav-item .nav-link.active:after {
  width: 100%;
}
@media (max-width: 575px) {
  .about-tab.style2 .nav-item .nav-link.active {
    background: var(--theme-color);
  }
}

/* About 5 ---------------------------------- */
.img-box5 {
  display: flex;
  gap: 24px;
  position: relative;
  /* Extra large devices */
}
.img-box5 .img1 {
  margin-top: 180px;
  position: relative;
}
.img-box5 .img1 img {
  width: 100%;
}
.img-box5 .img1 .about-bg-shape5-1 {
  position: absolute;
  left: 24px;
  top: -45px;
}
.img-box5 .img2 {
  position: relative;
}
.img-box5 .img2 img {
  width: 100%;
}
.img-box5 .img2 .about-bg-shape5-2 {
  position: absolute;
  left: 71px;
  bottom: 30px;
}
.img-box5 .client-group-wrap {
  position: absolute;
  display: inline-block;
  background: var(--title-color);
  border-radius: 16px;
  padding: 18px 16px;
  left: 40%;
  bottom: 28px;
}
.img-box5 .client-group-wrap .client-group-content {
  margin-top: 12px;
  display: flex;
  align-items: end;
  gap: 8px;
}
.img-box5 .client-group-wrap .year-counter_number {
  color: var(--white-color);
  font-size: 28px;
  font-weight: 600;
  margin-bottom: 0;
  line-height: 0.8;
}
.img-box5 .client-group-wrap .year-counter_text {
  font-size: 16px;
  font-weight: 400;
  color: var(--white-color);
  margin-bottom: 0;
  line-height: 1;
}
@media (max-width: 1500px) {
  .img-box5 .img1 {
    margin-top: 80px;
  }
}

.about-feature-grid.style2 {
  border: 1px solid var(--light-color);
  padding: 24px;
  gap: 16px;
  align-items: start;
  /* Large devices */
  /* Extra small devices */
}
.about-feature-grid.style2 .box-icon {
  padding: 0;
  border: 0;
  width: auto;
  height: auto;
}
.about-feature-grid.style2 .box-title {
  font-size: 24px;
  font-weight: 600;
}
.about-feature-grid.style2 .box-text {
  margin-top: 10px;
}
@media (max-width: 1199px) {
  .about-feature-grid.style2 {
    padding: 30px;
  }
}
@media (max-width: 375px) {
  .about-feature-grid.style2 {
    padding: 25px;
    display: block;
  }
  .about-feature-grid.style2 .box-icon {
    margin-bottom: 15px;
  }
}

.about-wrap5 {
  /* Extra large devices */
  /* Medium Large devices */
  /* Medium devices */
  /* Small devices */
  /* Extra small devices */
  /* Extra small devices */
}
.about-wrap5 .cta-wrap1 {
  display: flex;
  margin-top: 40px;
}
.about-wrap5 .cta-wrap1 .cta-video-wrap {
  flex: none;
}
.about-wrap5 .cta-wrap1 .cta-video-wrap .play-btn {
  --icon-size: 48px;
  --icon-font-size: 18px;
}
.about-wrap5 .cta-wrap1 .cta-video-wrap .play-btn:after, .about-wrap5 .cta-wrap1 .cta-video-wrap .play-btn:before {
  background: transparent;
  border: 1px solid var(--white-color);
}
.about-wrap5 .cta-wrap1 .cta-video-wrap img {
  height: 100%;
  object-fit: cover;
}
.about-wrap5 .cta-wrap1 .cta-wrap-card {
  background: var(--theme-color);
  padding: 62px 56px;
  display: flex;
  gap: 8px;
  position: relative;
  width: -webkit-fill-available;
  align-items: center;
}
.about-wrap5 .cta-wrap1 .cta-wrap-card .cta-wrap-bg-shape1 {
  position: absolute;
  left: 5px;
  top: 23px;
}
.about-wrap5 .cta-wrap1 .cta-wrap-card .cta-wrap-bg-shape2 {
  position: absolute;
  right: 9px;
  top: 6px;
}
.about-wrap5 .cta-wrap1 .cta-wrap-card .icon-btn {
  flex: none;
  background: var(--title-color);
  color: var(--white-color);
  --btn-size: 64px;
}
.about-wrap5 .cta-wrap1 .cta-wrap-card .box-title {
  font-size: 20px;
  font-weight: 500;
  color: var(--smoke-color2);
  margin-bottom: 10px;
}
.about-wrap5 .cta-wrap1 .cta-wrap-card .box-text {
  font-size: 40px;
  font-weight: 600;
  color: var(--white-color);
  display: block;
  margin-bottom: 0;
}
@media (max-width: 1500px) {
  .about-wrap5 .cta-wrap1 .cta-wrap-card {
    padding: 60px 40px;
  }
  .about-wrap5 .cta-wrap1 .cta-wrap-card .box-text {
    font-size: 30px;
  }
}
@media (max-width: 1399px) {
  .about-wrap5 .cta-wrap1 .cta-wrap-card {
    width: auto;
  }
}
@media (max-width: 991px) {
  .about-wrap5 .cta-wrap1 .cta-wrap-card {
    width: -webkit-fill-available;
  }
}
@media (max-width: 767px) {
  .about-wrap5 .cta-wrap1 {
    display: block;
  }
  .about-wrap5 .cta-wrap1 .cta-video-wrap img {
    height: 100%;
    width: 100%;
  }
  .about-wrap5 .cta-wrap1 .cta-wrap-card {
    justify-content: center;
    padding: 40px;
  }
}
@media (max-width: 575px) {
  .about-wrap5 .cta-wrap1 .cta-wrap-card .box-title {
    font-size: 16px;
  }
  .about-wrap5 .cta-wrap1 .cta-wrap-card .box-text {
    font-size: 24px;
  }
}
@media (max-width: 375px) {
  .about-wrap5 .cta-wrap1 .cta-wrap-card {
    padding: 30px;
    display: block;
  }
  .about-wrap5 .cta-wrap1 .cta-wrap-card .icon-btn {
    margin-bottom: 15px;
  }
}

/*Abour Page *****************/
.about-counter-area .counter-wrap2 {
  margin-right: -120px;
  justify-content: space-evenly;
  /* Hight Resoulation devices */
  /* Extra large devices */
}
.about-counter-area .counter-wrap2 .divider {
  background: var(--light-color);
  opacity: 0.5;
}
@media (min-width: 1922px) {
  .about-counter-area .counter-wrap2 {
    margin-right: 0;
  }
}
@media (max-width: 1500px) {
  .about-counter-area .counter-wrap2 {
    margin-right: 0;
  }
}
.about-counter-area .client-group-wrap.style2 .client-group-details .star-rating {
  width: 123px;
  font-size: 20px;
}
.about-counter-area .client-group-wrap.style2 .client-group-details .star-rating:before {
  color: var(--theme-color);
}

/*------------------- 4.00. Team  -------------------*/
/* Team global ---------------------------------- */
.th-team {
  position: relative;
}
.th-team .team-img {
  position: relative;
  overflow: hidden;
}
.th-team .team-img img {
  width: 100%;
  transition: 0.4s ease-in-out;
}
.th-team .team-desig {
  font-size: 14px;
  font-weight: 500;
  display: block;
  margin-bottom: -0.45em;
  transition: 0.4s ease-in-out;
  color: var(--theme-color);
}
.th-team .th-social {
  transition: 0.4s ease-in-out;
}
.th-team .th-social a {
  --icon-size: 40px;
  background-color: var(--white-color);
  color: var(--theme-color);
}
.th-team .th-social a:hover {
  background-color: var(--theme-color);
  color: var(--white-color);
}
.th-team .box-title {
  margin-bottom: 0;
}

/* Team Card ---------------------------------- */
.team-slider1 {
  margin-right: -315px;
  /* Large devices */
  /* Medium devices */
}
@media (max-width: 1199px) {
  .team-slider1 {
    margin-right: -200px;
  }
}
@media (max-width: 991px) {
  .team-slider1 {
    margin-right: 0;
  }
}

.team-card {
  position: relative;
  overflow: hidden;
  /* Team Card 2---------------------------------- */
  /* Team Card 3---------------------------------- */
  /* Small devices */
  /* Team Card 4---------------------------------- */
}
.team-card:after {
  content: "";
  position: absolute;
}
.team-card .team-img img {
  width: 100%;
  transform: scale(1);
  transition: 0.4s;
}
.team-card .team-card-content {
  position: absolute;
  background: var(--black-color2);
  padding: 0;
  bottom: 24px;
  left: 0;
  right: 24px;
  display: flex;
  transition: 0.7s;
  justify-content: space-between;
  transform: translate(-100%, 0);
}
.team-card .team-card-content .media-body {
  padding: 56px 60px;
  align-self: center;
  flex: none;
  /* Extra large devices */
  /* Extra small devices */
}
@media (max-width: 1500px) {
  .team-card .team-card-content .media-body {
    padding: 56px 30px;
  }
}
@media (max-width: 375px) {
  .team-card .team-card-content .media-body {
    padding: 56px 20px;
  }
}
.team-card .team-card-content:after {
  content: "";
  position: absolute;
  height: 100%;
  width: 3px;
  background: var(--theme-color);
  right: 0;
  top: 0;
  bottom: 0;
  z-index: 2;
  transform: translate(100%, 0);
}
.team-card .box-title {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 0px;
  position: relative;
  z-index: 1;
  /* Small devices */
}
.team-card .box-title a {
  color: var(--white-color);
}
.team-card .box-title a:hover {
  color: var(--theme-color);
}
@media (max-width: 767px) {
  .team-card .box-title {
    font-size: 20px;
  }
}
.team-card .team-desig {
  font-size: 14px;
  font-weight: 400;
  font-family: var(--body-font);
  margin-bottom: -0.5em;
  display: block;
  color: var(--light-color);
  position: relative;
  z-index: 1;
}
.team-card .th-social {
  width: 60px;
  z-index: 1;
  transition: 0.4s;
  z-index: 2;
  background: var(--theme-color);
  display: flex;
  flex-wrap: wrap;
  align-content: space-between;
}
.team-card .th-social .line {
  display: block;
  width: 100%;
  height: 1px;
  background: var(--white-color);
}
.team-card .th-social a {
  display: block;
  text-align: center;
  height: 52px;
  width: 60px;
  padding: 0;
  line-height: 52px;
  color: var(--white-color);
  margin: 0;
  background: var(--theme-color);
  border: 0;
  border-radius: 0;
}
.team-card .th-social a:hover {
  color: var(--theme-color);
  background: var(--white-color);
}
.team-card:hover .team-img img {
  transform: scale(1.05) translate(0, 0);
}
.team-card:hover .team-card-content {
  transform: translate(0, 0);
}
.team-card:hover .team-card-content:after {
  display: none;
}
.team-card.style2 .team-card-content {
  background: var(--title-color);
}
.team-card.style2 .th-social a {
  color: var(--title-color);
}
.team-card.style2 .th-social .line {
  background: var(--title-color);
}
.team-card.style3 .team-img {
  position: relative;
}
.team-card.style3 .team-img:after {
  content: "";
  position: absolute;
  inset: 0;
  background: var(--title-color);
  opacity: 0;
  transition: 0.4s;
}
.team-card.style3 .team-card-content {
  background: var(--title-color);
  bottom: 0;
  right: 0;
  border-bottom: 2px solid var(--theme-color);
}
.team-card.style3 .team-card-content:after {
  display: none;
}
.team-card.style3 .th-social a {
  color: var(--title-color);
}
.team-card.style3 .th-social .line {
  background: var(--title-color);
}
.team-card.style3:hover .team-img:after {
  opacity: 0.2;
}
@media (max-width: 767px) {
  .team-card .team-card-content {
    transform: translate(0, 0);
  }
  .team-card .team-card-content:after {
    display: none;
  }
}
.team-card.style4 {
  border-radius: 16px;
}

/* Team Card 4---------------------------------- */
.team-img-box {
  display: inline-block;
  /* Large devices */
  /* Medium devices */
  /* Extra small devices */
}
.team-img-box .team-content-wrap {
  display: flex;
  gap: 24px;
}
.team-img-box .team-content-wrap .team-card-content {
  flex: 1;
}
.team-img-box .team-content-wrap .team-card-content .team-social-wrap {
  background: var(--theme-color);
  padding: 73px 40px;
  position: relative;
  height: 50%;
  display: flex;
  align-content: center;
  flex-wrap: wrap;
}
.team-img-box .team-content-wrap .team-card-content .team-social-wrap .box-title {
  color: var(--white-color);
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 14px;
  margin-top: -0.3em;
  width: 100%;
}
.team-img-box .team-content-wrap .team-card-content .team-social-wrap .circle-tag {
  position: absolute;
  right: 40px;
  bottom: 0;
  padding: 35px;
  transform: translate(0, 50%);
}
.team-img-box .team-content-wrap .team-card-content .team-social-wrap .circle-tag .circle-anime-tag {
  height: 160px;
  width: 160px;
  margin-left: -80px;
  margin-top: -80px;
  font-size: 16px;
  font-weight: 600;
}
.team-img-box .team-content-wrap .team-card-content .team-social-wrap .circle-tag .circle-anime-tag span {
  --rotate-letter: 10deg;
  height: 83px;
  left: 39%;
}
.team-img-box .team-content-wrap .team-card-content .team-social-wrap .circle-tag .icon-btn {
  position: relative;
  z-index: 3;
  --btn-size: 90px;
  --btn-font-size: 30px;
  background: var(--white-color);
  color: var(--title-color);
}
.team-img-box .team-content-wrap .team-card-content .team-social-wrap .circle-tag .icon-btn:hover {
  background: var(--black-color2);
  color: var(--white-color);
}
.team-img-box .team-content-wrap .team-card-content .team-card-title {
  background: var(--title-color);
  margin-bottom: 0;
  padding: 38px 40px;
  color: var(--white-color);
  font-size: 40px;
  font-weight: 600;
  line-height: 1.2em;
  height: 50%;
  display: flex;
  align-items: center;
}
.team-img-box .team-content-wrap .img1 {
  flex: 1;
}
.team-img-box .team-content-wrap .img1 img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.team-img-box .img2 {
  margin-top: 24px;
}
.team-img-box .img2 img {
  width: 100%;
}
@media (max-width: 1199px) {
  .team-img-box .team-content-wrap .team-card-content .team-card-title {
    font-size: 30px;
  }
}
@media (max-width: 991px) {
  .team-img-box .team-content-wrap {
    display: block;
  }
  .team-img-box .team-content-wrap .img1 {
    margin-top: 24px;
  }
  .team-img-box .team-content-wrap .team-card-content .team-social-wrap {
    padding: 40px;
    justify-content: space-between;
  }
  .team-img-box .team-content-wrap .team-card-content .team-social-wrap .circle-tag {
    position: relative;
    bottom: auto;
    right: auto;
    transform: none;
  }
  .team-img-box .team-content-wrap .team-card-content .team-card-title {
    font-size: 24px;
    padding: 40px;
  }
}
@media (max-width: 575px) {
  .team-img-box .team-content-wrap .team-card-content .team-social-wrap .circle-tag {
    margin-top: 30px;
  }
}

.team-info-card {
  border: 1px solid var(--light-color);
  padding: 32px;
  transition: 0.4s;
  background: var(--white-color);
  /* Extra small devices */
}
.team-info-card:not(:last-child) {
  margin-bottom: 40px;
}
.team-info-card .card-title-wrap {
  display: flex;
  align-items: center;
  gap: 16px;
}
.team-info-card .card-title-wrap .box-icon {
  flex: none;
}
.team-info-card .card-title-wrap .box-icon .color-masking {
  transition: 0.4s;
}
.team-info-card .box-title {
  font-size: 28px;
  font-weight: 600;
  margin-bottom: -0.3em;
  transition: 0.4s;
  /* Extra small devices */
}
@media (max-width: 575px) {
  .team-info-card .box-title {
    font-size: 24px;
  }
}
.team-info-card .box-text {
  color: var(--title-color);
  margin-top: 15px;
  transition: 0.4s;
}
.team-info-card:hover {
  background: var(--title-color);
  border-color: var(--title-color);
}
.team-info-card:hover .box-icon .color-masking {
  transform: rotateY(180deg);
}
.team-info-card:hover .box-title {
  color: var(--white-color);
}
.team-info-card:hover .box-text {
  color: var(--light-color);
}
@media (max-width: 575px) {
  .team-info-card .card-title-wrap {
    display: block;
  }
  .team-info-card .card-title-wrap .box-title {
    margin-top: 15px;
  }
}

.feature-wrap1 {
  position: absolute;
  height: 200px;
  width: 100%;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: -1;
  /* Medium devices */
  /* Extra small devices */
}
.feature-wrap1 p {
  margin-bottom: 0;
  position: absolute;
  display: inline-block;
  left: 0;
  top: 0;
  user-select: none;
  pointer-events: auto;
  transition: none;
}
.feature-wrap1 .feature-item {
  display: inline-block;
  font-size: 18px;
  font-weight: 600;
  font-family: var(--title-font);
  border-radius: 48px;
  padding: 13.5px 32px;
  line-height: 1;
  color: var(--title-color);
  background: var(--theme-color2);
}
@media (max-width: 991px) {
  .feature-wrap1 .feature-item {
    padding: 10px 25px;
    font-size: 16px;
  }
}
@media (max-width: 575px) {
  .feature-wrap1 {
    position: relative;
    margin-top: -200px;
  }
}

/* Team Card 5---------------------------------- */
.team-bg-img-5 {
  position: absolute;
  inset: 0 0 35%;
  z-index: -1;
  overflow: hidden;
}
.team-bg-img-5 img {
  height: 100%;
  width: 100%;
  object-fit: cover;
}

.team-slider5 .slider-pagination {
  height: 35px;
  /* Extra small devices */
}
@media (max-width: 575px) {
  .team-slider5 .slider-pagination {
    display: none;
  }
}

/* Team Details ---------------------------------- */
.team-details-wrap {
  background: var(--smoke-color2);
  border-radius: 50px;
  padding: 60px;
  /* Small devices */
  /* Extra small devices */
}
@media (max-width: 767px) {
  .team-details-wrap {
    padding: 40px;
  }
}
@media (max-width: 575px) {
  .team-details-wrap {
    padding: 30px;
  }
}

.about-card-img {
  position: relative;
  border-radius: 30px;
  height: 100%;
}
.about-card-img img {
  border-radius: 30px;
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.about-card-img .th-social {
  text-align: center;
  margin-top: -20px;
}
.about-card-img .th-social a {
  --icon-size: 40px;
  border-radius: 50%;
  background: var(--white-color);
  box-shadow: 0px 10px 50px rgba(0, 0, 0, 0.08);
}
.about-card-img .th-social a:hover {
  background: var(--theme-color);
}

.about-card_title {
  margin-bottom: 8px;
}
.about-card_desig {
  color: var(--theme-color);
  margin-bottom: 25px;
  font-weight: 600;
  margin-top: -0.5em;
}
.about-card_text {
  margin-bottom: 30px;
}

.about-card-title-wrap {
  display: flex;
  justify-content: space-between;
  /* Extra small devices */
}
@media (max-width: 575px) {
  .about-card-title-wrap {
    display: block;
    margin-bottom: 30px;
  }
}

.team-details-about-info {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  border-top: 1px solid var(--th-border-color);
  border-bottom: 1px solid var(--th-border-color);
  margin-bottom: 40px;
  /* Medium Large devices */
  /* Small devices */
}
.team-details-about-info .about-contact {
  display: flex;
  gap: 5px 20px;
  align-items: center;
  flex-wrap: wrap;
  --space-x: 60px;
  --space-y: 30px;
  /* Extra large devices */
  /* Medium Large devices */
  /* Large devices */
  /* Medium devices */
  /* Small devices */
}
.team-details-about-info .about-contact:not(:nth-child(3n)) {
  border-right: unset;
}
.team-details-about-info .about-contact:not(:nth-last-child(-n+2)) {
  padding-bottom: var(--space-y);
  padding-top: var(--space-y);
}
.team-details-about-info .about-contact:not(:nth-child(-n+2)) {
  padding-top: var(--space-y);
  padding-bottom: var(--space-y);
  border-top: 2px solid var(--th-border-color);
}
.team-details-about-info .about-contact:nth-child(odd) {
  padding-right: var(--space-x);
}
.team-details-about-info .about-contact:nth-child(even) {
  padding-left: var(--space-x);
  border-left: 2px solid var(--th-border-color);
}
.team-details-about-info .about-contact:not(:nth-last-child(-n+4)) {
  border-top: 0;
}
@media (max-width: 1500px) {
  .team-details-about-info .about-contact {
    --space-x: 40px;
  }
}
@media (max-width: 1299px) {
  .team-details-about-info .about-contact {
    --space-x: 30px;
    --space-y: 30px;
  }
}
@media (max-width: 1199px) {
  .team-details-about-info .about-contact {
    --space-x: 20px;
    --space-y: 25px;
  }
}
@media (max-width: 991px) {
  .team-details-about-info .about-contact {
    --space-x: 30px;
    --space-y: 30px;
  }
}
@media (max-width: 767px) {
  .team-details-about-info .about-contact {
    padding: 0;
    border: 0;
  }
  .team-details-about-info .about-contact:nth-child(odd), .team-details-about-info .about-contact:nth-child(even) {
    padding: 0;
    border: 0;
  }
  .team-details-about-info .about-contact:not(:last-child) {
    padding-bottom: 20px;
  }
}
.team-details-about-info .about-contact .icon {
  width: 50px;
  height: 50px;
  box-shadow: 0px 10px 50px 10px #E8E8E8;
  border-radius: 50%;
  display: flex;
  align-items: center;
  text-align: center;
  justify-content: center;
  color: var(--theme-color);
  background: var(--white-color);
}
.team-details-about-info .about-contact .about-contact-title {
  font-size: 16px;
  font-weight: 400;
  font-family: var(--title-font);
  min-width: 112px;
  margin-bottom: 0;
  color: var(--body-color);
}
.team-details-about-info .about-contact .about-contact-text {
  color: var(--title-color);
  font-size: 18px;
  font-weight: 600;
  margin-bottom: -0.25em;
}
@media (max-width: 320px) {
  .team-details-about-info .about-contact {
    display: block;
  }
  .team-details-about-info .about-contact .icon {
    margin-bottom: 10px;
  }
}
@media (max-width: 1299px) {
  .team-details-about-info .about-contact .icon {
    width: 40px;
    height: 40px;
  }
}
@media (max-width: 767px) {
  .team-details-about-info {
    display: block;
    padding: 20px 0;
  }
}

.circle-progressbar {
  text-align: center;
}

.circular-progress {
  position: relative;
  width: 190px;
  height: 190px;
  text-align: center;
  display: inline-block;
  /* Medium Large devices */
}
.circular-progress svg {
  width: 100%;
  height: 100%;
}
.circular-progress .circle-bg {
  fill: none;
  stroke: var(--smoke-color2);
  stroke-width: 3;
}
.circular-progress .circle {
  fill: none;
  stroke-width: 3;
  stroke-dasharray: 100;
  stroke-dashoffset: 100;
  transition: stroke-dashoffset 1s ease;
  stroke-linecap: round;
  stroke: var(--theme-color);
}
.circular-progress .circle-progressbar-content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: 36px;
  font-weight: 700;
  color: var(--title-color);
  width: 100%;
}
.circular-progress .box-title {
  font-size: 16px;
  font-weight: 400;
  color: var(--body-color);
  margin-bottom: -0.3em;
  margin-top: 13px;
}
@media (max-width: 1299px) {
  .circular-progress {
    width: 170px;
    height: 170px;
  }
  .circular-progress .circle-bg {
    stroke-width: 2;
  }
  .circular-progress .circle {
    stroke-width: 2;
  }
  .circular-progress .box-title {
    font-size: 14px;
  }
}

/*Add Team Page*****************/
.add-team-form {
  border: 1px solid var(--th-border-color);
  border-radius: 50px;
  padding: 60px;
  /* Small devices */
  /* Extra small devices */
}
@media (max-width: 767px) {
  .add-team-form {
    padding: 40px;
    border-radius: 20px;
  }
}
@media (max-width: 575px) {
  .add-team-form {
    padding: 30px;
  }
}

/*------------------- 4.00. Testimonial  -------------------*/
/* Testimonial Area ---------------------------------- */
.testi-thumb1-1 {
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: calc(50% - 128px);
  /* Medium devices */
  /* Extra small devices */
  /* Extra small devices */
}
.testi-thumb1-1 img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.testi-thumb1-1 .testi-grid-thumb1 {
  position: absolute;
  right: 0;
  top: 50%;
  transform: translate(50%, -50%);
  height: 450px;
}
.testi-thumb1-1 .testi-grid-thumb1 .swiper-slide {
  text-align: center;
}
.testi-thumb1-1 .testi-grid-thumb1 .swiper-slide.swiper-slide-thumb-active {
  position: relative;
  z-index: 1;
}
.testi-thumb1-1 .testi-grid-thumb1 .swiper-slide.swiper-slide-thumb-active .box-img {
  filter: none;
}
.testi-thumb1-1 .testi-grid-thumb1 .swiper-slide.swiper-slide-thumb-active .box-img:after {
  opacity: 1;
  inset: -7px;
}
.testi-thumb1-1 .testi-grid-thumb1 .box-img {
  border: 7px solid var(--white-color);
  border-radius: 50%;
  display: inline-block;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  transition: 0.4s;
  filter: blur(1.5px);
}
.testi-thumb1-1 .testi-grid-thumb1 .box-img:after {
  content: "";
  position: absolute;
  inset: 0px;
  border: 2px solid var(--theme-color);
  border-radius: 50%;
  transition: 0.4s;
  opacity: 0;
}
.testi-thumb1-1 .testi-grid-thumb1 .box-img img {
  width: 96px;
  transition: 0.4s;
}
@media (max-width: 991px) {
  .testi-thumb1-1 {
    position: relative;
    width: 100%;
    top: -80px;
    bottom: auto;
    margin-bottom: 55px;
  }
  .testi-thumb1-1 .testi-grid-thumb1 {
    right: auto;
    top: auto;
    bottom: 0;
    left: 0;
    transform: translate(0, 50%);
    height: auto;
    width: inherit;
    padding: 0 60px 0px 30px;
    z-index: 3;
  }
  .testi-thumb1-1 .testi-grid-thumb1 .swiper-wrapper {
    align-items: center;
    height: 134px !important;
    justify-content: center;
  }
  .testi-thumb1-1 .testi-grid-thumb1 .swiper-slide {
    display: inline-block;
    width: auto !important;
  }
  .testi-thumb1-1 .testi-grid-thumb1 .swiper-slide.swiper-slide-thumb-active .box-img img {
    width: 96px;
  }
}
@media (max-width: 575px) {
  .testi-thumb1-1 .testi-grid-thumb1 .swiper-wrapper {
    height: 110px !important;
  }
  .testi-thumb1-1 .testi-grid-thumb1 .swiper-slide .box-img img {
    width: 80px;
  }
}
@media (max-width: 375px) {
  .testi-thumb1-1 {
    margin-bottom: 42px;
  }
  .testi-thumb1-1 .testi-grid-thumb1 .swiper-wrapper {
    height: 84px !important;
  }
  .testi-thumb1-1 .testi-grid-thumb1 .swiper-slide .box-img img {
    width: 60px;
  }
  .testi-thumb1-1 .testi-grid-thumb1 .swiper-slide.swiper-slide-thumb-active .box-img img {
    width: 70px;
  }
}

.testi-slider1 {
  position: relative;
}
.testi-slider1 .swiper-slide {
  opacity: 0 !important;
}
.testi-slider1 .swiper-slide.swiper-slide-active {
  opacity: 1 !important;
}
.testi-slider1 .slider-scrollbar {
  margin-top: 70px;
  height: 10px;
}
.testi-slider1 .slider-scrollbar .swiper-scrollbar-drag {
  height: 10px;
}

/* Testimonial card ---------------------------------- */
.testi-card {
  position: relative;
  padding: 80px 0px 0px 0;
  /* Medium devices */
  /* Extra small devices */
}
.testi-card .quote-icon {
  width: 65px;
  height: 56px;
  background: var(--theme-color);
  margin-bottom: 40px;
}
.testi-card .box-text {
  font-size: 24px;
  font-weight: 600;
  font-family: var(--title-font);
  color: var(--title-color);
  line-height: 1.555em;
  margin: 0px 0 50px 0;
  margin-top: -0.4em;
  /* Medium Large devices */
  /* Large devices */
}
@media (max-width: 1299px) {
  .testi-card .box-text {
    font-size: 18px;
  }
}
@media (max-width: 1199px) {
  .testi-card .box-text {
    font-size: 20px;
  }
}
.testi-card .testi-card_review {
  color: var(--yellow-color);
  display: flex;
  gap: 8px;
  margin-bottom: 24px;
}
.testi-card .box-title {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: -0.3em;
  color: var(--title-color);
  /* Extra small devices */
}
@media (max-width: 375px) {
  .testi-card .box-title {
    font-size: 20px;
  }
}
.testi-card .box-desig {
  margin-bottom: -0.5em;
  display: block;
  font-size: 18px;
  font-weight: 400;
  margin-top: 7px;
  /* Medium Large devices */
}
@media (max-width: 1299px) {
  .testi-card .box-desig {
    font-size: 16px;
  }
}
@media (max-width: 991px) {
  .testi-card {
    padding-top: 0;
    padding-right: 0;
  }
}
@media (max-width: 575px) {
  .testi-card .box-text {
    font-size: 16px;
  }
  .testi-card .quote-icon {
    right: 30px;
    bottom: 30px;
    width: 35px;
    height: 35px;
  }
}

/* Testimonial Area 2---------------------------------- */
.testi-thumb-slider-wrap2 {
  position: relative;
  display: inline-block;
  width: 294px;
  height: 302px;
  transform: rotate(-15deg);
  margin-bottom: -160px;
  margin-top: -100px;
  /* Extra large devices */
  /* Medium Large devices */
  /* Large devices */
  /* Small devices */
}
.testi-thumb-slider-wrap2 .testi-box-img {
  position: relative;
  border-radius: 0;
  display: inline-block;
}
.testi-thumb-slider-wrap2 .testi-box-img .testi-img {
  height: 302px;
  width: 257px;
  object-fit: cover;
  transition: 0.4s;
  padding-bottom: 30px;
}
.testi-thumb-slider-wrap2 .testi-box-img:after {
  content: "";
  position: absolute;
  inset: 30px -35px -30px 35px;
  background: var(--light-color);
  z-index: -1;
  transition: 0.4s;
}
.testi-thumb-slider-wrap2 .swiper-slide {
  transition: 0.4s;
  margin-top: 0;
  opacity: 0 !important;
}
.testi-thumb-slider-wrap2 .swiper-slide.swiper-slide-active {
  opacity: 1 !important;
}
@media (max-width: 1500px) {
  .testi-thumb-slider-wrap2 {
    left: 30px;
    top: -40px;
  }
}
@media (max-width: 1299px) {
  .testi-thumb-slider-wrap2 {
    top: -30px;
    left: 20px;
    margin-top: 0;
    width: 260px;
    height: 280px;
  }
  .testi-thumb-slider-wrap2 .testi-box-img .testi-img {
    height: 280px;
    width: 240px;
  }
}
@media (max-width: 1199px) {
  .testi-thumb-slider-wrap2 {
    top: 0;
    left: 0;
    width: auto;
    height: auto;
    margin-bottom: 0;
    transform: none;
    display: block;
    text-align: center;
  }
  .testi-thumb-slider-wrap2 .swiper-slide {
    margin-top: 0 !important;
  }
}
@media (max-width: 767px) {
  .testi-thumb-slider-wrap2 {
    text-align: center;
  }
  .testi-thumb-slider-wrap2 .testimonial-bg-shape2-1 {
    display: none;
  }
}

.testi-slider2 {
  /* Medium devices */
}
.testi-slider2 .swiper-slide {
  opacity: 0 !important;
}
.testi-slider2 .swiper-slide.swiper-slide-active {
  opacity: 1 !important;
}
.testi-slider2 .slider-pagination {
  position: absolute;
  right: 14px;
  left: auto;
  bottom: 50%;
  margin: 0;
  width: auto;
  height: auto;
  transform: translate(0, 50%);
}
.testi-slider2 .slider-pagination .swiper-pagination-bullet {
  display: block;
  margin: 48px 0;
  background: transparent;
  border: 1px solid var(--light-color);
  transition: 0.4s;
}
.testi-slider2 .slider-pagination .swiper-pagination-bullet:before {
  inset: -15px;
}
.testi-slider2 .slider-pagination .swiper-pagination-bullet.swiper-pagination-bullet-active {
  background: var(--theme-color);
  border-color: var(--theme-color);
}
@media (max-width: 991px) {
  .testi-slider2 .slider-pagination {
    display: none;
  }
}

.testi-card2 {
  text-align: center;
  max-width: 931px;
  margin-left: auto;
  margin-right: auto;
  /* Extra large devices */
  /* Medium Large devices */
  /* Large devices */
  /* Medium devices */
  /* Small devices */
  /* Extra small devices */
}
.testi-card2 .quote-icon {
  width: 65px;
  height: 56px;
  background: var(--theme-color);
  margin-bottom: 42px;
  display: inline-block;
}
.testi-card2 .box-text {
  font-size: 28px;
  font-weight: 600;
  font-family: var(--title-font);
  color: var(--white-color);
  line-height: 1.5em;
  margin-bottom: 47px;
}
.testi-card2 .testi-card_review {
  color: var(--white-color);
  margin-bottom: 20px;
  display: block;
  font-size: 24px;
  font-weight: 600;
  font-family: var(--title-font);
  display: inline-flex;
  gap: 10px;
}
.testi-card2 .testi-card_review i {
  color: var(--theme-color);
  font-size: 18px;
  color: var(--yellow-color);
}
.testi-card2 .box-title {
  font-size: 28px;
  font-weight: 600;
  margin-bottom: 0;
  color: var(--white-color);
}
.testi-card2 .box-desig {
  margin-bottom: -0.5em;
  font-size: 18px;
  font-weight: 400;
  color: var(--light-color);
}
@media (max-width: 1500px) {
  .testi-card2 {
    max-width: 800px;
  }
  .testi-card2 .box-text {
    font-size: 24px;
  }
}
@media (max-width: 1299px) {
  .testi-card2 {
    max-width: 650px;
  }
  .testi-card2 .box-text {
    font-size: 20px;
  }
}
@media (max-width: 1199px) {
  .testi-card2 {
    margin-top: 60px;
  }
}
@media (max-width: 991px) {
  .testi-card2 .quote-icon {
    margin-bottom: 20px;
  }
  .testi-card2 .box-text {
    font-size: 18px;
    line-height: normal;
    margin-bottom: 37px;
  }
}
@media (max-width: 767px) {
  .testi-card2 {
    margin-top: 50px;
  }
  .testi-card2 .box-title {
    font-size: 24px;
  }
}
@media (max-width: 575px) {
  .testi-card2 .box-text {
    font-size: 16px;
    font-weight: 400;
  }
  .testi-card2 .box-desig {
    font-size: 16px;
  }
}

/* Testimonial Area 3---------------------------------- */
.testi-slider3 .swiper-slide {
  opacity: 0 !important;
}
.testi-slider3 .swiper-slide.swiper-slide-active {
  opacity: 1 !important;
}

.testi-thumb-slider-wrap3 {
  width: 520px;
  margin-left: auto;
  /* Medium Large devices */
  /* Large devices */
}
.testi-thumb-slider-wrap3 .testi-thumb-slider3 {
  padding-right: 95px;
  /* Medium Large devices */
  /* Large devices */
}
@media (max-width: 1399px) {
  .testi-thumb-slider-wrap3 .testi-thumb-slider3 {
    padding-right: 70px;
  }
}
@media (max-width: 1199px) {
  .testi-thumb-slider-wrap3 .testi-thumb-slider3 {
    padding-right: 0;
  }
}
.testi-thumb-slider-wrap3 .testi-thumb-slider3 .swiper-slide-shadow {
  display: none;
}
@media (max-width: 1299px) {
  .testi-thumb-slider-wrap3 {
    width: 460px;
  }
}
@media (max-width: 1199px) {
  .testi-thumb-slider-wrap3 {
    margin-right: auto;
    width: auto;
    text-align: center;
  }
}

/* Testimonial Area 4---------------------------------- */
.testi-bg-shape4-1 {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  width: 664px;
  background-color: var(--title-color);
  /* Extra large devices */
  /* Medium Large devices */
}
@media (max-width: 1500px) {
  .testi-bg-shape4-1 {
    width: 500px;
  }
}
@media (max-width: 1399px) {
  .testi-bg-shape4-1 {
    width: 100%;
    height: 50%;
    bottom: 0;
    top: auto;
  }
}

.testi-bg-shape4-2 {
  z-index: 1;
}

.testi-slider4 {
  margin-left: 105px;
  margin-right: -130px;
  /* Hight Resoulation devices */
  /* Extra large devices */
  /* Medium Large devices */
}
@media (min-width: 1922px) {
  .testi-slider4 {
    margin-right: 0;
    margin-left: 0;
  }
}
@media (max-width: 1500px) {
  .testi-slider4 {
    margin-right: 0;
  }
}
@media (max-width: 1399px) {
  .testi-slider4 {
    margin-left: 0;
  }
  .testi-slider4 .slider-pagination.style3 .swiper-pagination-bullet.swiper-pagination-bullet-active:before {
    background: var(--white-color);
    border-color: var(--white-color);
  }
}
.testi-slider4 .swiper-slide {
  filter: blur(10px);
  transition: 0.4s;
}
.testi-slider4 .swiper-slide.swiper-slide-active {
  filter: none;
}

.testi-card3 {
  display: flex;
  border-radius: 8px;
  overflow: hidden;
  background: var(--gray-color2);
  /* Medium Large devices */
  /* Medium Large devices */
  /* Large devices */
  /* Medium devices */
  /* Small devices */
  /* Extra small devices */
}
.testi-card3 .box-img {
  flex: 1;
}
.testi-card3 .box-img img {
  height: 100%;
  width: 100%;
  object-fit: cover;
}
.testi-card3 .box-content {
  padding: 60px;
  flex: 1.2;
  align-self: center;
}
.testi-card3 .quote-icon {
  width: 65px;
  height: 56px;
  background: var(--title-color);
  margin-bottom: 42px;
  display: inline-block;
}
.testi-card3 .box-text {
  font-size: 28px;
  font-weight: 600;
  font-family: var(--title-font);
  color: var(--title-color);
  line-height: 1.5em;
  margin-bottom: 47px;
}
.testi-card3 .testi-card_review {
  color: var(--title-color);
  margin-bottom: 20px;
  display: block;
  font-size: 24px;
  font-weight: 600;
  font-family: var(--title-font);
  display: inline-flex;
  gap: 10px;
}
.testi-card3 .testi-card_review i {
  color: var(--theme-color);
  font-size: 18px;
  color: var(--yellow-color);
}
.testi-card3 .box-title {
  font-size: 28px;
  font-weight: 600;
  margin-bottom: 0;
  color: var(--title-color);
}
.testi-card3 .box-desig {
  margin-bottom: -0.5em;
  font-size: 18px;
  font-weight: 400;
  color: var(--body-color);
}
@media (max-width: 1399px) {
  .testi-card3 .box-text {
    font-size: 20px;
  }
}
@media (max-width: 1299px) {
  .testi-card3 .box-text {
    font-size: 18px;
  }
}
@media (max-width: 1199px) {
  .testi-card3 .box-content {
    padding: 50px;
  }
  .testi-card3 .quote-icon {
    margin-bottom: 22px;
  }
  .testi-card3 .box-text {
    margin-bottom: 27px;
  }
}
@media (max-width: 991px) {
  .testi-card3 .box-text {
    font-size: 16px;
  }
}
@media (max-width: 767px) {
  .testi-card3 {
    display: block;
  }
}
@media (max-width: 575px) {
  .testi-card3 .box-content {
    padding: 30px;
  }
  .testi-card3 .quote-icon {
    width: 45px;
    height: 36px;
  }
  .testi-card3 .box-title {
    font-size: 24px;
  }
}

/*------------------- 4.00. Counter  -------------------*/
/* Counter 1 ---------------------------------- */
.counter-card {
  text-align: center;
  padding: 37.6px 0;
}
.counter-card .box-number {
  margin-top: -0.22em;
  margin-bottom: -0.1em;
}
.counter-card .box-text {
  max-width: 186px;
  font-size: 18px;
  font-weight: 500;
  display: inline-block;
  /* Small devices */
}
@media (max-width: 767px) {
  .counter-card .box-text {
    font-size: 16px;
    max-width: 170px;
  }
}

.counter-wrap1 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: var(--title-color);
  padding: 0 80px;
  border-radius: 0 0 24px 24px;
  border-bottom: 1px solid var(--theme-color);
  /* Large devices */
  /* Medium devices */
  /* Small devices */
  /* Extra small devices */
  /* Extra small devices */
}
.counter-wrap1 .divider {
  width: 1px;
  height: 180px;
  background: var(--body-color);
  /* Medium devices */
}
.counter-wrap1 .divider:last-of-type {
  display: none;
}
@media (max-width: 991px) {
  .counter-wrap1 .divider {
    display: none;
  }
}
@media (max-width: 1199px) {
  .counter-wrap1 {
    padding: 0 40px;
  }
}
@media (max-width: 991px) {
  .counter-wrap1 {
    flex-wrap: wrap;
    gap: 40px 0;
    padding: 60px;
  }
  .counter-wrap1 .counter-card {
    flex: 50%;
    padding: 0;
  }
}
@media (max-width: 767px) {
  .counter-wrap1 {
    padding: 40px;
  }
}
@media (max-width: 575px) {
  .counter-wrap1 {
    padding: 40px 20px;
  }
}
@media (max-width: 375px) {
  .counter-wrap1 .counter-card {
    flex: 100%;
  }
}

/* Counter 2 ---------------------------------- */
.counter-area-2 {
  background: var(--black-color2);
  margin-right: 120px;
  /* Extra large devices */
}
@media (max-width: 1500px) {
  .counter-area-2 {
    margin-right: 0;
  }
}

.counter-card2 {
  text-align: center;
  padding: 37.6px 0;
}
.counter-card2 .box-number {
  margin-top: -0.22em;
  margin-bottom: -0.1em;
  letter-spacing: 0.02em;
}
.counter-card2 .box-text {
  font-size: 16px;
  font-weight: 400;
  display: inline-block;
  /* Small devices */
}
@media (max-width: 767px) {
  .counter-card2 .box-text {
    font-size: 16px;
  }
}

.counter-wrap2 {
  display: flex;
  justify-content: space-around;
  align-items: center;
  /* Medium devices */
  /* Extra small devices */
  /* Extra small devices */
}
.counter-wrap2 .divider {
  width: 1px;
  height: 220px;
  background: var(--white-color);
  opacity: 0.1;
  /* Medium devices */
}
.counter-wrap2 .divider:last-of-type {
  display: none;
}
@media (max-width: 991px) {
  .counter-wrap2 .divider {
    display: none;
  }
}
.counter-wrap2 .client-group-wrap .client-group-details {
  display: flex;
  gap: 24px;
  align-items: center;
  margin-top: 10px;
  justify-content: center;
  /* Large devices */
}
@media (max-width: 1199px) {
  .counter-wrap2 .client-group-wrap .client-group-details {
    margin-top: 20px;
    gap: 15px;
  }
}
.counter-wrap2 .client-group-wrap .client-group-details .box_number {
  font-size: 56px;
  font-weight: 700;
  color: var(--white-color);
  margin-bottom: 0;
  /* Large devices */
}
@media (max-width: 1199px) {
  .counter-wrap2 .client-group-wrap .client-group-details .box_number {
    font-size: 40px;
  }
}
.counter-wrap2 .client-group-wrap .client-group-details .star-rating:before {
  color: var(--theme-color2);
}
.counter-wrap2 .client-group-wrap .client-group-details .box_text {
  margin-bottom: -0.3em;
  color: var(--white-color);
  font-size: 18px;
  font-weight: 600;
  font-family: var(--title-font);
  margin-top: 8px;
  /* Large devices */
}
@media (max-width: 1199px) {
  .counter-wrap2 .client-group-wrap .client-group-details .box_text {
    font-size: 16px;
    font-weight: 400;
  }
}
@media (max-width: 991px) {
  .counter-wrap2 {
    flex-wrap: wrap;
    gap: 40px 0;
    padding: 60px 0;
  }
  .counter-wrap2 .counter-card2 {
    flex: 50%;
    padding: 0;
  }
}
@media (max-width: 575px) {
  .counter-wrap2 {
    padding: 40px 0px;
  }
}
@media (max-width: 375px) {
  .counter-wrap2 .counter-card2 {
    flex: 100%;
  }
}

/* Counter 3 ---------------------------------- */
.counter-card3 {
  background: var(--black-color2);
  padding: 65px 20px;
  text-align: center;
  /* Medium Large devices */
  /* Small devices */
  /* Extra small devices */
}
.counter-card3 .box-number {
  font-size: 40px;
  font-weight: 600;
  margin-top: -0.25em;
  margin-bottom: 0;
}
.counter-card3 .box-text {
  font-size: 18px;
}
@media (max-width: 1399px) {
  .counter-card3 .box-number {
    font-size: 36px;
  }
  .counter-card3 .box-text {
    font-size: 16px;
  }
}
@media (max-width: 767px) {
  .counter-card3 .box-number {
    font-size: 28px;
  }
}
@media (max-width: 575px) {
  .counter-card3 {
    padding: 40px 20px;
  }
}

/* Counter 4 ---------------------------------- */
.counter-card4 {
  background: var(--black-color2);
  padding: 48px 20px;
  text-align: center;
  /* Medium Large devices */
  /* Small devices */
}
.counter-card4 .box-icon {
  height: 100px;
  width: 100px;
  border-radius: 50%;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  background: var(--theme-color);
  margin-bottom: 18px;
}
.counter-card4 .box-icon img {
  transition: 0.4s;
}
.counter-card4 .box-number {
  font-size: 56px;
  font-weight: 700;
  color: var(--white-color);
  letter-spacing: 0.02em;
  margin-bottom: -0.2em;
}
.counter-card4 .box-text {
  font-size: 24px;
  color: var(--light-color);
}
.counter-card4:hover .box-icon img {
  transform: rotateY(180deg);
}
@media (max-width: 1399px) {
  .counter-card4 .box-number {
    font-size: 36px;
  }
  .counter-card4 .box-text {
    font-size: 18px;
    margin-top: 10px;
  }
}
@media (max-width: 767px) {
  .counter-card4 .box-number {
    font-size: 30px;
  }
}

/* Counter 5 ---------------------------------- */
.counter-card5 {
  border: 1px solid var(--light-color);
  border-radius: 8px;
  text-align: center;
  padding: 55px 0;
  /* Medium Large devices */
}
.counter-card5 .box-title {
  font-size: 56px;
  font-weight: 700;
  margin-bottom: -0.2em;
}
.counter-card5 .box-text {
  font-size: 20px;
  font-weight: 500;
}
@media (max-width: 1299px) {
  .counter-card5 {
    padding: 40px 0;
  }
  .counter-card5 .box-title {
    font-size: 40px;
  }
  .counter-card5 .box-text {
    font-size: 16px;
  }
}

/*------------------- 4.00. Blog  -------------------*/
/* Blog Card ---------------------------------- */
.blog-title {
  font-size: 30px;
}

.blog-card {
  border-radius: 16px;
  background-color: var(--white-color);
  display: flex;
  overflow: hidden;
  border: 1px solid var(--light-color);
  padding: 24px;
  gap: 24px;
  transition: 0.4s;
  /* Medium Large devices */
  /* Large devices */
  /* Medium devices */
  /* Small devices */
  /* Extra small devices */
}
.blog-card .blog-img {
  overflow: hidden;
  border-radius: 8px;
  flex: 1;
}
.blog-card .blog-img img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: 0.4s ease-in-out;
}
.blog-card .blog-content {
  flex: 1;
  align-self: center;
}
.blog-card .blog-meta {
  gap: 5px 0;
}
.blog-card .blog-meta span,
.blog-card .blog-meta a {
  font-weight: 400;
  color: var(--body-color);
  margin-right: 24px;
}
.blog-card .blog-meta span:after,
.blog-card .blog-meta a:after {
  display: none;
}
.blog-card .blog-meta span:last-child,
.blog-card .blog-meta a:last-child {
  margin-right: 0;
}
.blog-card .blog-meta span:hover,
.blog-card .blog-meta a:hover {
  color: var(--theme-color);
}
.blog-card .box-title {
  margin-top: 16px;
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 0;
}
.blog-card .box-text {
  margin-top: 7px;
  margin-bottom: 0;
}
.blog-card .th-btn {
  padding: 16px 30px;
  min-width: auto;
  margin-top: 32px;
}
.blog-card:hover {
  border-bottom-color: var(--theme-color);
  box-shadow: 0px 10px 20px rgba(202, 188, 186, 0.5);
}
.blog-card:hover .blog-img img {
  transform: scale(1.05);
  filter: none;
}
.blog-card:hover .blog-img .icon-btn {
  opacity: 1;
}
.blog-card:hover .blog-img:after {
  opacity: 0.8;
}
@media (max-width: 1299px) {
  .blog-card .box-title {
    font-size: 22px;
  }
}
@media (max-width: 1199px) {
  .blog-card .box-title {
    font-size: 24px;
  }
}
@media (max-width: 991px) {
  .blog-card .box-title {
    font-size: 20px;
  }
}
@media (max-width: 767px) {
  .blog-card {
    display: block;
  }
  .blog-card .blog-img {
    margin-bottom: 24px;
  }
  .blog-card .box-title {
    font-size: 24px;
  }
}
@media (max-width: 575px) {
  .blog-card .box-title {
    font-size: 20px;
  }
}

/* Blog Card 2---------------------------------- */
.blog-card2 .blog-img {
  margin-bottom: 32px;
}
.blog-card2 .blog-img img {
  width: 100%;
}
.blog-card2 .blog-meta {
  margin-bottom: 30px;
}
.blog-card2 .blog-meta span,
.blog-card2 .blog-meta a {
  color: var(--light-color);
  font-weight: 400;
}
.blog-card2 .blog-meta span:hover,
.blog-card2 .blog-meta a:hover {
  color: var(--theme-color);
}
.blog-card2 .box-title {
  margin-bottom: 38px;
  font-size: 28px;
  font-weight: 600;
  line-height: 1.42em;
  /* Medium Large devices */
  /* Extra small devices */
}
.blog-card2 .box-title a {
  color: var(--white-color);
  background-image: linear-gradient(to left, var(--theme-color), var(--theme-color));
  background-repeat: no-repeat;
  background-position: bottom left;
  background-size: 0 2px;
  transition: 0.5s ease-in-out;
}
.blog-card2 .box-title a:hover {
  color: var(--theme-color);
  background-size: 100% 2px;
}
@media (max-width: 1399px) {
  .blog-card2 .box-title {
    font-size: 24px;
  }
}
@media (max-width: 575px) {
  .blog-card2 .box-title {
    font-size: 22px;
  }
}

/* Blog Card 3---------------------------------- */
.blog-slider3 .swiper-slide.swiper-slide-active .blog-card3 {
  margin-top: 0;
}
.blog-slider3 .swiper-slide.swiper-slide-active .blog-card3 .blog-content {
  transform: scaleX(1);
}
.blog-slider3 .slider-pagination {
  margin-top: -60px;
  /* Medium devices */
}
@media (max-width: 991px) {
  .blog-slider3 .slider-pagination {
    margin-top: 30px;
  }
}

.blog-card3 {
  position: relative;
  margin-top: 110px;
  transition: 0.4s;
  /* Medium devices */
  /* Extra small devices */
}
.blog-card3 .blog-img img {
  width: 100%;
  object-fit: cover;
}
.blog-card3 .blog-content {
  position: absolute;
  left: 0;
  bottom: 24px;
  background: rgba(19, 24, 43, 0.8);
  padding: 24px;
  transition: 0.4s;
  transform: scaleX(0);
  transform-origin: left;
}
.blog-card3 .blog-meta {
  margin-bottom: 24px;
  gap: 10px 24px;
}
.blog-card3 .blog-meta span,
.blog-card3 .blog-meta a {
  color: var(--light-color);
  font-weight: 400;
  margin-right: 0;
}
.blog-card3 .blog-meta span:after,
.blog-card3 .blog-meta a:after {
  display: none;
}
.blog-card3 .blog-meta span:hover,
.blog-card3 .blog-meta a:hover {
  color: var(--theme-color);
}
.blog-card3 .box-title {
  margin-bottom: 38px;
  font-size: 28px;
  font-weight: 600;
  line-height: 1.42em;
  max-width: 402px;
  /* Medium Large devices */
  /* Extra small devices */
}
.blog-card3 .box-title a {
  color: var(--white-color);
  background-image: linear-gradient(to left, var(--theme-color), var(--theme-color));
  background-repeat: no-repeat;
  background-position: bottom left;
  background-size: 0 2px;
  transition: 0.5s ease-in-out;
}
.blog-card3 .box-title a:hover {
  color: var(--theme-color);
  background-size: 100% 2px;
}
@media (max-width: 1399px) {
  .blog-card3 .box-title {
    font-size: 24px;
  }
}
@media (max-width: 575px) {
  .blog-card3 .box-title {
    font-size: 22px;
  }
}
@media (max-width: 991px) {
  .blog-card3 {
    margin-top: 0;
  }
  .blog-card3 .box-title {
    margin-bottom: 25px;
  }
}
@media (max-width: 575px) {
  .blog-card3 .blog-img img {
    height: 450px;
  }
  .blog-card3 .blog-content {
    right: 24px;
  }
}

/* Blog Card 4---------------------------------- */
.blog-grid {
  display: flex;
  background-color: transparent;
  position: relative;
  border-radius: 0px;
  overflow: hidden;
  gap: 32px;
}
.blog-grid-wrap {
  display: grid;
  gap: 24px;
  grid-template-areas: "one one one one one two two two two two two two" "one one one one one three three three three three three three" "one one one one one four four four four four four four";
}
.blog-grid .blog-img {
  position: relative;
  min-width: 280px;
  height: 100%;
  overflow: hidden;
  margin: -1px;
  border-radius: 8px;
}
.blog-grid .blog-img img {
  height: 100%;
  width: 100%;
  object-fit: cover;
  object-position: center center;
  transition: 0.4s ease-in-out;
}
.blog-grid .blog-content {
  padding: 0;
  align-self: center;
}
.blog-grid .box-title {
  font-size: 28px;
  margin-bottom: 37px;
  margin-top: 15px;
  font-weight: 600;
}
.blog-grid .box-title a {
  background-image: linear-gradient(to left, var(--title-color), var(--title-color));
  background-repeat: no-repeat;
  background-position: bottom left;
  background-size: 0 2px;
  transition: 0.5s ease-in-out;
}
.blog-grid .box-title a:hover {
  color: var(--title-color);
  background-size: 100% 2px;
}
.blog-grid .blog-meta span:after, .blog-grid .blog-meta a:after {
  display: none;
}
.blog-grid .blog-meta span, .blog-grid .blog-meta a {
  margin-right: 0;
  padding-right: 24px;
  color: var(--body-color);
}
.blog-grid .blog-meta span:last-child, .blog-grid .blog-meta a:last-child {
  padding-right: 0;
}
.blog-grid .blog-meta span:hover, .blog-grid .blog-meta a:hover {
  color: var(--title-color);
}
.blog-grid:nth-child(1) {
  grid-area: one;
  margin-right: 32px;
  display: block;
}
.blog-grid:nth-child(1) .blog-img {
  min-width: 100%;
  height: auto;
  margin-bottom: 32px;
}
.blog-grid:nth-child(2) {
  grid-area: two;
}
.blog-grid:nth-child(3) {
  grid-area: three;
}
.blog-grid:nth-child(4) {
  grid-area: four;
}
.blog-grid:hover .blog-img img {
  transform: scale(1.1);
}

/* Extra large devices */
@media (max-width: 1500px) {
  .blog-grid:nth-child(1) {
    margin-right: 0;
  }
}
/* Large devices */
@media (max-width: 1199px) {
  .blog-grid-wrap {
    grid-template-areas: "one one one" "two two two" "three three three" "four four four";
  }
  .blog-grid:nth-child(1) {
    margin-bottom: 24px;
  }
  .blog-grid .blog-img {
    min-width: 210px;
  }
}
/* Small devices */
@media (max-width: 767px) {
  .blog-grid {
    flex-direction: column;
  }
  .blog-grid-wrap {
    gap: 40px;
  }
  .blog-grid .blog-img {
    height: 100%;
  }
  .blog-grid .blog-content {
    align-self: flex-start;
  }
  .blog-grid .box-title {
    margin-bottom: 27px;
    font-size: 24px;
  }
  .blog-grid:nth-child(1) {
    margin-bottom: 0px;
  }
}
/* Extra small devices */
@media (max-width: 575px) {
  .blog-grid .box-title {
    font-size: 22px;
  }
}
/* Blog Card 5---------------------------------- */
.blog-card.style5 {
  display: block;
  border-radius: 0;
  padding: 32px;
  /* Medium Large devices */
}
.blog-card.style5 .blog-img {
  border-radius: 0;
  margin-bottom: 32px;
}
.blog-card.style5:hover {
  border: 1px solid var(--theme-color);
}
@media (max-width: 1399px) {
  .blog-card.style5 {
    padding: 24px;
  }
}

/* Blog Card 6---------------------------------- */
.blog-card6 .blog-img {
  margin-bottom: 30px;
}
.blog-card6 .blog-img img {
  width: 100%;
}
.blog-card6 .blog-meta {
  margin-bottom: 24px;
}
.blog-card6 .blog-meta span,
.blog-card6 .blog-meta a {
  color: var(--body-color);
  font-weight: 400;
}
.blog-card6 .blog-meta span:hover,
.blog-card6 .blog-meta a:hover {
  color: var(--theme-color);
}
.blog-card6 .box-title {
  margin-bottom: 38px;
  font-size: 28px;
  font-weight: 600;
  line-height: 1.42em;
  /* Medium Large devices */
  /* Extra small devices */
}
.blog-card6 .box-title a {
  color: var(--title-color);
  background-image: linear-gradient(to left, var(--theme-color), var(--theme-color));
  background-repeat: no-repeat;
  background-position: bottom left;
  background-size: 0 2px;
  transition: 0.5s ease-in-out;
}
.blog-card6 .box-title a:hover {
  color: var(--theme-color);
  background-size: 100% 2px;
}
@media (max-width: 1399px) {
  .blog-card6 .box-title {
    font-size: 24px;
  }
}
@media (max-width: 575px) {
  .blog-card6 .box-title {
    font-size: 22px;
  }
}

/*------------------- 4.00. Brand  -------------------*/
/* Brand 1 ---------------------------------- */
.brand-wrap1 .brand-wrap-title {
  font-size: 24px;
  font-weight: 600;
  margin-top: -0.25em;
  margin-bottom: 40px;
}
.brand-wrap1 .brand-wrap-title span {
  font-size: 30px;
}

.brand-box {
  display: grid;
  align-content: center;
  text-align: center;
  justify-content: center;
  transition: 0.4s ease-in-out;
  min-height: 32px;
}
.brand-box img {
  transition: 0.4s;
}
.brand-box:hover img {
  filter: none;
  transform: scale(1.05);
}

/* Brand 3 ---------------------------------- */
.brand-wrap3 {
  position: relative;
  z-index: 1;
  padding: 23px 0;
  /* Large devices */
  /* Medium devices */
}
.brand-wrap3:after {
  content: "";
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  background: var(--theme-color2);
  width: calc(50% - 150px);
  z-index: -1;
  clip-path: polygon(0 0, 100% 0, 100% 100%, 50px 100%);
}
.brand-wrap3 .counter-wrap3 {
  display: flex;
  align-items: center;
  gap: 24px;
  padding-left: 140px;
}
.brand-wrap3 .counter-wrap3 .counter-number {
  color: var(--white-color);
  font-size: 56px;
  font-weight: 700;
  margin-bottom: 0;
}
.brand-wrap3 .counter-wrap3 .counter-rating {
  color: var(--yellow-color);
  display: flex;
  gap: 8px;
}
.brand-wrap3 .counter-wrap3 .counter-text {
  font-size: 18px;
  font-weight: 600;
  color: var(--white-color);
  margin-bottom: -0.3em;
  margin-top: 10px;
}
@media (max-width: 1199px) {
  .brand-wrap3 .counter-wrap3 {
    padding-left: 50px;
  }
  .brand-wrap3:after {
    display: none;
  }
}
@media (max-width: 991px) {
  .brand-wrap3 {
    text-align: center;
  }
  .brand-wrap3 .counter-wrap3 {
    padding-left: 0;
    display: inline-flex;
    text-align: start;
  }
}

/*------------------- 4.00. Simple Sections  -------------------*/
.checklist ul {
  padding-left: 0;
  list-style: none;
  text-align: left;
  margin-bottom: 0;
}
.checklist li {
  color: var(--title-color);
  font-weight: 500;
  font-size: 20px;
  font-family: var(--title-font);
  padding-left: 40px;
  display: flex;
  gap: 12px;
  line-height: normal;
  position: relative;
  /* Extra small devices */
}
.checklist li:before {
  content: "";
  position: absolute;
  left: 0;
  top: 2px;
  width: 24px;
  height: 24px;
  background: var(--theme-color);
  clip-path: path("M15.6245 3.38693C15.983 3.59433 16.4418 3.47181 16.6492 3.11326C16.8566 2.75471 16.7341 2.29592 16.3755 2.08851L15.6245 3.38693ZM21.535 9.25078C21.4527 8.84484 21.0568 8.58252 20.6509 8.66488C20.2449 8.74723 19.9826 9.14308 20.065 9.54902L21.535 9.25078ZM7 11.1499C6.58579 11.1499 6.25 11.4857 6.25 11.8999C6.25 12.3141 6.58579 12.6499 7 12.6499V11.1499ZM10.5 15.3999L9.84882 15.772C9.98062 16.0027 10.2246 16.1464 10.4903 16.1498C10.7559 16.1533 11.0035 16.016 11.1413 15.7888L10.5 15.3999ZM21.2609 5.10306C21.6492 4.95898 21.8472 4.52735 21.7032 4.13901C21.5591 3.75066 21.1275 3.55265 20.7391 3.69674L21.2609 5.10306ZM16.3755 2.08851C14.7936 1.17341 12.9568 0.649902 11 0.649902V2.1499C12.686 2.1499 14.2646 2.60033 15.6245 3.38693L16.3755 2.08851ZM11 0.649902C5.06294 0.649902 0.25 5.46284 0.25 11.3999H1.75C1.75 6.29127 5.89136 2.1499 11 2.1499V0.649902ZM0.25 11.3999C0.25 17.3369 5.06293 22.1499 11 22.1499V20.6499C5.89137 20.6499 1.75 16.5085 1.75 11.3999H0.25ZM11 22.1499C16.937 22.1499 21.75 17.3369 21.75 11.3999H20.25C20.25 16.5085 16.1086 20.6499 11 20.6499V22.1499ZM21.75 11.3999C21.75 10.6646 21.676 9.94573 21.535 9.25078L20.065 9.54902C20.1862 10.1465 20.25 10.7654 20.25 11.3999H21.75ZM7 11.8999C7 12.6499 6.99915 12.6499 6.99831 12.6499C6.99803 12.6499 6.99719 12.6499 6.99665 12.6499C6.99555 12.6499 6.99447 12.6499 6.99342 12.6499C6.99131 12.6499 6.98929 12.6498 6.98735 12.6498C6.98348 12.6497 6.97995 12.6496 6.97675 12.6495C6.97036 12.6493 6.96533 12.649 6.96164 12.6487C6.95424 12.6481 6.95226 12.6477 6.95553 12.6482C6.96192 12.6492 6.99 12.6539 7.03822 12.6693C7.13381 12.6997 7.31593 12.774 7.5699 12.9518C8.08002 13.3089 8.88689 14.0886 9.84882 15.772L11.1512 15.0278C10.1131 13.2112 9.16998 12.2409 8.4301 11.723C8.05907 11.4633 7.74119 11.3189 7.49303 11.2399C7.36937 11.2006 7.26465 11.178 7.18119 11.1653C7.13953 11.1589 7.10338 11.1551 7.07303 11.1528C7.05786 11.1517 7.04417 11.151 7.03198 11.1505C7.02588 11.1503 7.02017 11.1501 7.01484 11.15C7.01217 11.15 7.0096 11.15 7.00713 11.1499C7.00589 11.1499 7.00468 11.1499 7.00349 11.1499C7.0029 11.1499 7.00202 11.1499 7.00173 11.1499C7.00086 11.1499 7 11.1499 7 11.8999ZM10.5 15.3999C11.1413 15.7888 11.1413 15.7889 11.1412 15.7889C11.1413 15.7888 11.1413 15.7888 11.1413 15.7888C11.1414 15.7886 11.1416 15.7883 11.1418 15.7879C11.1424 15.787 11.1432 15.7856 11.1444 15.7837C11.1468 15.7798 11.1505 15.7737 11.1555 15.7656C11.1655 15.7492 11.1807 15.7245 11.201 15.6918C11.2415 15.6265 11.3022 15.5295 11.3814 15.4049C11.5399 15.1556 11.7727 14.7959 12.0678 14.3581C12.6583 13.4815 13.4957 12.2954 14.4838 11.0546C15.4732 9.81199 16.6046 8.5263 17.7822 7.44438C18.9682 6.35476 20.1579 5.51229 21.2609 5.10306L20.7391 3.69674C19.3715 4.20418 18.0134 5.19505 16.7674 6.33979C15.513 7.49225 14.3282 8.84197 13.3103 10.1202C12.291 11.4003 11.4299 12.6204 10.8238 13.5199C10.5205 13.97 10.2804 14.3409 10.1156 14.6C10.0332 14.7296 9.96959 14.8314 9.92628 14.9012C9.90462 14.9361 9.88804 14.9631 9.87671 14.9815C9.87105 14.9908 9.8667 14.9979 9.86369 15.0028C9.86219 15.0053 9.86102 15.0072 9.86018 15.0086C9.85977 15.0093 9.85944 15.0098 9.85919 15.0102C9.85907 15.0104 9.85894 15.0106 9.85888 15.0107C9.85878 15.0109 9.8587 15.011 10.5 15.3999Z");
}
.checklist li > i {
  color: var(--theme-color);
  position: relative;
  top: 2px;
}
.checklist li:not(:last-child) {
  margin-bottom: 14px;
}
@media (max-width: 575px) {
  .checklist li {
    font-size: 18px;
  }
}
.checklist.mb-45 {
  /* Large devices */
}
@media (max-width: 1199px) {
  .checklist.mb-45 {
    margin-bottom: 40px;
  }
}
.checklist.style2 li {
  font-size: 20px;
  font-weight: 500;
  font-family: var(--title-font);
  color: var(--title-color);
  padding-left: 37px;
}
.checklist.style2 li:before {
  clip-path: path("M8.55186 18.5479C11.0008 14.7831 20.008 5.09279 27.4776 0.0585812C27.7949 -0.155222 28.1531 0.264464 27.8901 0.542246C20.7938 8.03629 13.2087 16.513 8.85249 23.8428C8.73114 24.047 8.43819 24.0541 8.31139 23.8533C6.11928 20.381 4.2392 15.3898 0.209389 13.8603C-0.089979 13.7467 -0.0612074 13.3235 0.250089 13.2485C4.1119 12.318 5.92146 15.6208 8.55186 18.5475V18.5479Z");
  width: 27px;
}
.checklist.style2 li:not(:last-child) {
  margin-bottom: 18px;
}
.checklist.style3 li {
  color: var(--title-color);
  padding-left: 37px;
}
.checklist.style3 li:before {
  clip-path: path("M8.55186 18.5479C11.0008 14.7831 20.008 5.09279 27.4776 0.0585812C27.7949 -0.155222 28.1531 0.264464 27.8901 0.542246C20.7938 8.03629 13.2087 16.513 8.85249 23.8428C8.73114 24.047 8.43819 24.0541 8.31139 23.8533C6.11928 20.381 4.2392 15.3898 0.209389 13.8603C-0.089979 13.7467 -0.0612074 13.3235 0.250089 13.2485C4.1119 12.318 5.92146 15.6208 8.55186 18.5475V18.5479Z");
  width: 27px;
  background: var(--title-color);
}
.checklist.style3 li:not(:last-child) {
  margin-bottom: 18px;
}
.checklist.style4 li {
  color: var(--body-color);
  padding-left: 20px;
  font-size: 16px;
  font-weight: 400;
  font-family: var(--body-font);
  line-height: inherit;
}
.checklist.style4 li:before {
  width: 7px;
  height: 7px;
  border-radius: 50%;
  background: var(--body-color);
  clip-path: none;
  top: 12px;
  transform: translate(0, -50%);
}
.checklist.style4 li:not(:last-child) {
  margin-bottom: 10px;
}
.checklist.style-line li {
  font-size: 16px;
  font-family: var(--body-font);
  position: relative;
  gap: 15px;
}
.checklist.style-line li::before {
  content: "";
  position: relative;
  height: 1px;
  width: 30px;
  background: var(--theme-color);
}
.checklist.style-white li {
  color: var(--white-color);
}
.checklist.list-two-column ul {
  display: inline-grid;
  grid-template-columns: auto auto;
  gap: 16px 40px;
  /* Small devices */
}
.checklist.list-two-column ul li {
  margin-bottom: 0px !important;
}
@media (max-width: 767px) {
  .checklist.list-two-column ul {
    grid-template-columns: auto;
    gap: 20px;
  }
  .checklist.list-two-column ul li {
    text-align: initial;
  }
}

.mega-hover {
  position: relative;
  overflow: hidden;
  z-index: 2;
}
.mega-hover:after, .mega-hover:before {
  content: "";
  position: absolute;
  pointer-events: none;
  opacity: 1;
  z-index: -1;
}
.mega-hover:before {
  top: 0;
  right: 51%;
  bottom: 0;
  left: 50%;
  background: rgba(255, 255, 255, 0.2);
}
.mega-hover:after {
  top: 50%;
  right: 0;
  bottom: 50%;
  left: 0;
  background: rgba(255, 255, 255, 0.3);
}
.mega-hover:hover:before {
  left: 0;
  right: 0;
  opacity: 0;
  transition: all 900ms linear;
}
.mega-hover:hover:after {
  top: 0;
  bottom: 0;
  opacity: 0;
  transition: all 900ms linear;
}

.bg-img {
  position: absolute;
  inset: 0;
  height: 100%;
  width: 100%;
}
.bg-img img {
  width: 100%;
  height: 100%;
}

.th-video {
  position: relative;
  border-radius: 10px;
}
.th-video img {
  border-radius: inherit;
}
.th-video .play-btn {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.rounded-10 {
  border-radius: 10px;
}

.rounded-20 {
  border-radius: 20px;
  /* Small devices */
}
@media (max-width: 767px) {
  .rounded-20 {
    border-radius: 10px;
  }
}

.btn-wrap {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 20px 30px;
}
.btn-wrap.style2 {
  gap: 20px 40px;
}

.filter-menu {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 15px;
  text-align: center;
  margin-bottom: 60px;
  /* Large devices */
  /* Medium devices */
}
@media (max-width: 1199px) {
  .filter-menu {
    margin-bottom: 50px;
  }
}
@media (max-width: 991px) {
  .filter-menu {
    margin-top: -10px;
    margin-bottom: 45px;
    gap: 6px;
  }
}
.filter-menu .th-btn {
  border-radius: 5px;
  padding: 15px 30px;
  background-color: transparent;
  color: var(--body-color);
  border: 1px solid var(--th-border-color);
  min-width: auto;
  /* Medium devices */
}
.filter-menu .th-btn:before {
  background-color: var(--theme-color);
}
.filter-menu .th-btn:hover, .filter-menu .th-btn.active {
  border-color: var(--theme-color);
  color: var(--white-color);
}
.filter-menu .th-btn:hover:before, .filter-menu .th-btn.active:before {
  border-radius: 3px;
}
@media (max-width: 991px) {
  .filter-menu .th-btn {
    padding: 13px 20px;
  }
}

/* Large devices */
@media (max-width: 1199px) {
  p.mb-40 {
    margin-bottom: 35px;
  }
  p.mb-45 {
    margin-bottom: 38px;
  }
}
.modal-backdrop.show {
  opacity: 0.7;
  z-index: 99;
}

.modal {
  z-index: 999;
  padding-right: 0 !important;
}

.modal-dialog {
  max-width: 100%;
}
.modal-dialog .modal-content {
  background: transparent;
  border: 0;
}
.modal-dialog .modal-content .modal-header {
  border: 0;
}
.modal-dialog .btn-close {
  padding: 0;
  outline: 0;
  box-shadow: none;
  margin: 0 10px 0 auto;
  border-radius: 50%;
  background: var(--theme-color);
  color: var(--white-color);
  border: 0;
  opacity: 1;
}
.modal-dialog .btn-close:hover i {
  animation: toTopFromBottom 0.5s forwards;
}

/*------------------- 4.00. Why -------------------*/
/* Why Choose Us Area ---------------------------------- */
.circle-tag {
  display: inline-block;
  position: relative;
  padding: 22px;
}

.circle-anime-tag {
  display: inline-block;
  height: 250px;
  width: 250px;
  border-radius: 50%;
  z-index: 1;
  font-size: 20px;
  font-weight: 300;
  font-family: var(--body-font);
  color: var(--white-color);
  line-height: normal;
  text-align: center;
  animation: spin 20s linear infinite;
  position: absolute;
  top: 50%;
  left: 50%;
  margin-left: -125px;
  margin-top: -125px;
}
.circle-anime-tag span {
  --rotate-letter: 7deg;
  height: 127px;
  position: absolute;
  width: 20px;
  left: 42%;
  top: -2px;
  transform-origin: bottom;
  transform: rotate(var(--rotate-letter));
}
.circle-anime-tag span.char2 {
  transform: rotate(calc(var(--rotate-letter) * 2));
}
.circle-anime-tag span.char3 {
  transform: rotate(calc(var(--rotate-letter) * 3));
}
.circle-anime-tag span.char4 {
  transform: rotate(calc(var(--rotate-letter) * 4));
}
.circle-anime-tag span.char5 {
  transform: rotate(calc(var(--rotate-letter) * 5));
}
.circle-anime-tag span.char6 {
  transform: rotate(calc(var(--rotate-letter) * 6));
}
.circle-anime-tag span.char7 {
  transform: rotate(calc(var(--rotate-letter) * 7));
}
.circle-anime-tag span.char8 {
  transform: rotate(calc(var(--rotate-letter) * 8));
}
.circle-anime-tag span.char9 {
  transform: rotate(calc(var(--rotate-letter) * 9));
}
.circle-anime-tag span.char10 {
  transform: rotate(calc(var(--rotate-letter) * 10));
}
.circle-anime-tag span.char11 {
  transform: rotate(calc(var(--rotate-letter) * 11));
}
.circle-anime-tag span.char12 {
  transform: rotate(calc(var(--rotate-letter) * 12));
}
.circle-anime-tag span.char13 {
  transform: rotate(calc(var(--rotate-letter) * 13));
}
.circle-anime-tag span.char14 {
  transform: rotate(calc(var(--rotate-letter) * 14));
}
.circle-anime-tag span.char15 {
  transform: rotate(calc(var(--rotate-letter) * 15));
}
.circle-anime-tag span.char16 {
  transform: rotate(calc(var(--rotate-letter) * 16));
}
.circle-anime-tag span.char17 {
  transform: rotate(calc(var(--rotate-letter) * 17));
}
.circle-anime-tag span.char18 {
  transform: rotate(calc(var(--rotate-letter) * 18));
}
.circle-anime-tag span.char19 {
  transform: rotate(calc(var(--rotate-letter) * 19));
}
.circle-anime-tag span.char20 {
  transform: rotate(calc(var(--rotate-letter) * 20));
}
.circle-anime-tag span.char21 {
  transform: rotate(calc(var(--rotate-letter) * 21));
}
.circle-anime-tag span.char22 {
  transform: rotate(calc(var(--rotate-letter) * 22));
}
.circle-anime-tag span.char23 {
  transform: rotate(calc(var(--rotate-letter) * 23));
}
.circle-anime-tag span.char24 {
  transform: rotate(calc(var(--rotate-letter) * 24));
}
.circle-anime-tag span.char25 {
  transform: rotate(calc(var(--rotate-letter) * 25));
}
.circle-anime-tag span.char26 {
  transform: rotate(calc(var(--rotate-letter) * 26));
}
.circle-anime-tag span.char27 {
  transform: rotate(calc(var(--rotate-letter) * 27));
}
.circle-anime-tag span.char28 {
  transform: rotate(calc(var(--rotate-letter) * 28));
}
.circle-anime-tag span.char29 {
  transform: rotate(calc(var(--rotate-letter) * 29));
}
.circle-anime-tag span.char30 {
  transform: rotate(calc(var(--rotate-letter) * 30));
}
.circle-anime-tag span.char31 {
  transform: rotate(calc(var(--rotate-letter) * 31));
}
.circle-anime-tag span.char32 {
  transform: rotate(calc(var(--rotate-letter) * 32));
}
.circle-anime-tag span.char33 {
  transform: rotate(calc(var(--rotate-letter) * 33));
}
.circle-anime-tag span.char34 {
  transform: rotate(calc(var(--rotate-letter) * 34));
}
.circle-anime-tag span.char35 {
  transform: rotate(calc(var(--rotate-letter) * 35));
}
.circle-anime-tag span.char36 {
  transform: rotate(calc(var(--rotate-letter) * 36));
}
.circle-anime-tag span.char37 {
  transform: rotate(calc(var(--rotate-letter) * 37));
}
.circle-anime-tag span.char38 {
  transform: rotate(calc(var(--rotate-letter) * 38));
}
.circle-anime-tag span.char39 {
  transform: rotate(calc(var(--rotate-letter) * 39));
}
.circle-anime-tag span.char40 {
  transform: rotate(calc(var(--rotate-letter) * 40));
}

.why-img-box1 {
  position: relative;
  overflow: hidden;
  /* Extra large devices */
  /* Medium Large devices */
  /* Medium Large devices */
  /* Large devices */
  /* Medium devices */
  /* Small devices */
}
.why-img-box1 .circle-tag {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(calc(50% - 126px), -50%);
  z-index: 2;
  /* Extra large devices */
  /* Medium Large devices */
  /* Large devices */
  /* Medium devices */
  /* Small devices */
  /* Extra small devices */
}
.why-img-box1 .circle-tag .circle-anime-tag {
  color: var(--white-color);
  font-size: 16px;
  font-weight: 600;
  font-family: var(--title-font);
  width: 170px;
  height: 170px;
  margin-left: -85px;
  margin-top: -85px;
}
.why-img-box1 .circle-tag .circle-anime-tag span {
  height: 86px;
  left: 40%;
  top: 0px;
  --rotate-letter: 10deg;
}
@media (max-width: 1500px) {
  .why-img-box1 .circle-tag {
    transform: translate(calc(50% - 145px), -50%);
  }
}
@media (max-width: 1399px) {
  .why-img-box1 .circle-tag {
    transform: translate(calc(50% - 155px), -50%);
  }
}
@media (max-width: 1199px) {
  .why-img-box1 .circle-tag {
    transform: translate(calc(50% - 134px), -50%);
  }
}
@media (max-width: 991px) {
  .why-img-box1 .circle-tag {
    transform: translate(calc(50% - 143px), -50%);
  }
}
@media (max-width: 767px) {
  .why-img-box1 .circle-tag {
    display: none;
  }
}
@media (max-width: 575px) {
  .why-img-box1 .circle-tag {
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1;
  }
}
.why-img-box1 .slider-area {
  overflow: hidden;
}
.why-img-box1 .why-thumb-slider {
  margin-right: -866px;
  margin-left: 130px;
  transform: translate(-342px, 0);
}
.why-img-box1 .why-thumb-slider .swiper-slide .swiper-slide-shadow-right,
.why-img-box1 .why-thumb-slider .swiper-slide .swiper-slide-shadow-left {
  background-image: none;
}
.why-img-box1 .why-thumb-slider .swiper-slide-active {
  border-right: 3px solid var(--white-color);
  /* Small devices */
}
.why-img-box1 .why-thumb-slider .swiper-slide-active .project-card .box-img:after {
  opacity: 1;
}
.why-img-box1 .why-thumb-slider .swiper-slide-active .project-card .box-img img {
  filter: none;
}
.why-img-box1 .why-thumb-slider .swiper-slide-active .project-card .box-content {
  opacity: 1;
  visibility: visible;
}
@media (max-width: 767px) {
  .why-img-box1 .why-thumb-slider .swiper-slide-active {
    border-right: 0;
  }
}
.why-img-box1 .slider-arrow {
  --pos-x: 270px;
  --icon-size: 100px;
  --icon-font-size: 30px;
  opacity: 1;
  visibility: visible;
  transform: none;
  background: var(--theme-color);
  border: 0;
}
.why-img-box1 .slider-arrow img {
  width: 24px;
}
.why-img-box1 .slider-arrow:hover {
  background: var(--white-color);
  color: var(--theme-color);
}
@media (max-width: 1500px) {
  .why-img-box1 .why-thumb-slider {
    margin-right: -835px;
    transform: translate(-364px, 0);
  }
  .why-img-box1 .slider-arrow {
    --pos-x: 185px;
  }
}
@media (max-width: 1399px) {
  .why-img-box1 .why-thumb-slider {
    margin-right: -810px;
    transform: translate(-378px, 0);
  }
  .why-img-box1 .slider-arrow {
    --pos-x: 135px;
  }
}
@media (max-width: 1299px) {
  .why-img-box1 .why-thumb-slider {
    margin-right: -800px;
    transform: translate(-383px, 0);
  }
  .why-img-box1 .slider-arrow {
    --pos-x: 110px;
  }
}
@media (max-width: 1199px) {
  .why-img-box1 .why-thumb-slider {
    margin-right: -900px;
    transform: translate(-323px, 0);
  }
  .why-img-box1 .slider-arrow {
    --pos-x: 330px;
  }
}
@media (max-width: 991px) {
  .why-img-box1 .why-thumb-slider {
    margin-right: -840px;
    transform: translate(-360px, 0);
  }
  .why-img-box1 .slider-arrow {
    --pos-x: 212px;
    --icon-size: 70px;
    font-size: 20px;
    display: block;
  }
}
@media (max-width: 767px) {
  .why-img-box1 .why-thumb-slider {
    margin-right: 0;
    margin-left: 0;
    transform: none;
  }
  .why-img-box1 .slider-arrow {
    display: none;
  }
}

/*progress bar**********************/
.progress-bar-wrap:not(:last-child) {
  margin-bottom: 28px;
}
.progress-bar-wrap .progress-bar_title {
  font-size: 18px;
  font-weight: 600;
  font-family: var(--title-font);
  margin-bottom: 12px;
  color: var(--title-color);
}
.progress-bar-wrap .progress {
  background: transparent;
  border: 1px solid var(--light-color);
  height: 17px;
  border-radius: 30px;
  overflow: visible;
  position: relative;
  padding: 3px 4px 3px 3px;
}
.progress-bar-wrap .progress .progress-bar {
  background: var(--theme-color);
  border-radius: 30px;
  overflow: visible;
}
.progress-bar-wrap .progress .progress-value {
  position: absolute;
  right: 0;
  top: -38px;
  font-size: 18px;
  font-weight: 600;
  font-family: var(--title-font);
  color: var(--title-color);
}

/* Why Choose Us Area 2---------------------------------- */
.wcu-grid-wrap {
  display: flex;
  gap: 32px;
  max-width: 578px;
  /* Small devices */
  /* Extra small devices */
  /* Extra small devices */
}
.wcu-grid-wrap:not(:last-child) {
  margin-bottom: 40px;
}
.wcu-grid-wrap .box-icon {
  width: 64px;
  height: 64px;
  border-radius: 8px;
  background: var(--title-color);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  flex: none;
  margin: 9px 0 0 9px;
  position: relative;
}
.wcu-grid-wrap .box-icon:after {
  content: "";
  position: absolute;
  inset: -9px 9px 9px -9px;
  border: 1px dashed var(--title-color);
  border-radius: 8px;
}
.wcu-grid-wrap .box-title {
  font-size: 28px;
  font-weight: 600;
  margin-bottom: 8px;
}
@media (max-width: 767px) {
  .wcu-grid-wrap .box-title {
    font-size: 24px;
  }
}
@media (max-width: 575px) {
  .wcu-grid-wrap .box-title {
    font-size: 20px;
  }
}
@media (max-width: 375px) {
  .wcu-grid-wrap {
    display: block;
  }
  .wcu-grid-wrap .box-icon {
    margin-bottom: 25px;
  }
}

/*------------------- 4.00. Faq -------------------*/
/* Faq 1 ---------------------------------- */
.faq-wrap1 {
  background: var(--black-color2);
  padding: 32px;
}

.accordion-card {
  transition: 0.4s ease-in-out;
  border-radius: 0;
  overflow: hidden;
  background-color: var(--title-color);
  border: 1px solid var(--title-color);
  text-align: left;
  position: relative;
  z-index: 3;
  padding-bottom: 13px;
}
.accordion-card:not(:last-child) {
  margin-bottom: 16px;
}
.accordion-card:has(.accordion-button.collapsed) {
  background-color: transparent;
  border: 1px solid rgba(87, 88, 95, 0.5);
}
.accordion-card .accordion-button {
  font-size: 24px;
  font-weight: 600;
  font-family: var(--title-font);
  border: 0;
  color: var(--white-color);
  background-color: transparent;
  border-radius: 0;
  padding: 27px 55px 12px 30px;
  gap: 10px;
  margin-bottom: 0;
  text-align: left;
  transition: 0.3s;
  position: relative;
}
.accordion-card .accordion-button:after {
  content: "+";
  height: 100%;
  width: auto;
  line-height: 1;
  background-color: transparent;
  background-image: none;
  font-family: var(--icon-font);
  color: var(--white-color);
  font-weight: 500;
  font-size: 1em;
  display: grid;
  justify-content: center;
  align-items: center;
  text-align: center;
  position: absolute;
  top: 6px;
  right: 30px;
  transition: 0.3s ease-in-out;
}
.accordion-card .accordion-button:focus {
  outline: none;
  box-shadow: none;
}
.accordion-card .accordion-button:not(.collapsed):after {
  content: "\f00d";
  transform: rotate(90deg);
}
.accordion-card .accordion-collapse {
  border: none;
}
.accordion-card .accordion-body {
  border: none;
  padding: 0px 0 14px;
  margin: 0 30px;
}
.accordion-card .faq-text {
  margin-bottom: -0.48em;
  color: var(--light-color);
}

/* Extra small devices */
@media (max-width: 575px) {
  .accordion-card {
    padding-bottom: 6px;
  }
  .accordion-card .accordion-button {
    font-size: 16px;
    padding: 20px 55px 12px 20px;
  }
  .accordion-card .accordion-button:after {
    right: 20px;
  }
  .accordion-card .accordion-body {
    padding: 0px 0 20px;
    margin: 0 20px;
  }
  .accordion-card .faq-text {
    font-size: 14px;
  }
}
/* Faq 2 ---------------------------------- */
.faq-img-box2 .img1 {
  border-radius: 8px;
  overflow: hidden;
}
.faq-img-box2 .img1 img {
  height: 100%;
  width: 100%;
  object-fit: cover;
}

.accordion-card.style2 {
  border-radius: 8px;
}
.accordion-card.style2 .accordion-button {
  color: var(--title-color);
  transition: inherit;
}
.accordion-card.style2 .accordion-button:after {
  color: var(--title-color);
}
.accordion-card.style2 .accordion-button:not(.collapsed) {
  color: var(--white-color);
}
.accordion-card.style2 .accordion-button:not(.collapsed):after {
  color: var(--white-color);
}
.accordion-card.style2:has(.accordion-collapse.show) {
  background-color: var(--title-color);
  border: 1px solid var(--title-color);
}
.accordion-card.style2:has(.accordion-collapse.show) .accordion-button {
  color: var(--white-color);
}
.accordion-card.style2:has(.accordion-collapse.show) .accordion-button:after {
  color: var(--white-color);
}

/* Faq 2 ---------------------------------- */
.accordion-card.style3 {
  border: 1px solid var(--light-color);
  border-radius: 16px;
  background: var(--white-color);
  padding-bottom: 23px;
  /* Large devices */
  /* Medium devices */
}
.accordion-card.style3 .accordion-button {
  color: var(--title-color);
  padding: 37px 70px 12px 48px;
}
.accordion-card.style3 .accordion-button:after {
  color: var(--title-color);
  top: 11px;
  right: 48px;
}
.accordion-card.style3 .faq-text {
  color: var(--body-color);
}
.accordion-card.style3 .accordion-body {
  margin: 0 48px;
}
.accordion-card.style3:has(.accordion-collapse.show) {
  background-color: var(--white-color);
  border: 1px solid var(--light-color);
  box-shadow: 0px 30px 80px rgba(19, 24, 43, 0.2);
}
.accordion-card.style3:has(.accordion-collapse.show) .accordion-button {
  color: var(--title-color);
}
.accordion-card.style3:has(.accordion-button.collapsed) {
  background-color: var(--white-color);
  border: 1px solid var(--light-color);
}
@media (max-width: 1199px) {
  .accordion-card.style3 {
    padding-bottom: 13px;
  }
  .accordion-card.style3 .accordion-button {
    padding: 27px 55px 12px 30px;
    font-size: 20px;
  }
  .accordion-card.style3 .accordion-button:after {
    right: 30px;
  }
  .accordion-card.style3 .accordion-body {
    margin: 0 30px;
  }
}
@media (max-width: 991px) {
  .accordion-card.style3 {
    border-radius: 8px;
  }
}

/*------------------- 4.00. Service -------------------*/
/* Service Card ---------------------------------- */
.service-card {
  position: relative;
  padding: 0;
  overflow: hidden;
  /* Medium Large devices */
  /* Medium devices */
  /* Small devices */
  /* Extra small devices */
}
.service-card .box-thumb {
  border-radius: 16px;
}
.service-card .box-thumb img {
  border-radius: 16px;
  transition: 0.4s;
  width: 402px;
  height: 593px;
  object-fit: cover;
}
.service-card .box-content {
  background: rgba(19, 24, 43, 0.7);
  backdrop-filter: blur(5px);
  border-radius: 16px;
  padding: 62px;
  position: absolute;
  left: 0;
  bottom: 0;
  right: 0;
  margin: 8px;
  transform: rotate(-90deg) translate(100%, -190px);
  transform-origin: bottom right;
  width: 577px;
  z-index: 1;
  transition: 0.4s;
}
.service-card .box-content.style2 {
  transform: rotate(0);
  width: -webkit-fill-available;
  margin: 32px;
  opacity: 0;
  bottom: -20px;
}
.service-card .box-number {
  font-size: 40px;
  font-weight: 700;
  font-family: var(--title-font);
  color: var(--white-color);
  line-height: 0.75em;
  margin-bottom: 22px;
}
.service-card .box-title {
  margin-bottom: -0.3em;
  max-width: 300px;
}
.service-card .box-title a {
  font-size: 28px;
  font-weight: 600;
  color: var(--white-color);
}
.service-card .box-title a:hover {
  color: var(--theme-color);
}
.service-card .icon-btn {
  position: absolute;
  right: 8px;
  top: 8px;
  border-radius: 8px;
  font-size: 36px;
  opacity: 0;
  visibility: hidden;
}
.service-card .icon-btn:hover {
  background: var(--white-color);
  color: var(--theme-color);
}
@media (max-width: 1299px) {
  .service-card .box-thumb img {
    width: 100%;
  }
  .service-card .box-content {
    transform: rotate(0);
    width: -webkit-fill-available;
    margin: 32px;
    opacity: 0;
  }
}
@media (max-width: 991px) {
  .service-card .box-content {
    padding: 50px;
  }
}
@media (max-width: 767px) {
  .service-card .box-thumb img {
    height: 400px;
  }
  .service-card .box-content {
    padding: 40px;
  }
  .service-card .box-number {
    font-size: 30px;
  }
  .service-card .box-title a {
    font-size: 24px;
  }
  .service-card .icon-btn {
    --btn-size: 40px;
    font-size: 24px;
  }
}
@media (max-width: 575px) {
  .service-card .box-content {
    padding: 30px;
  }
  .service-card .box-number {
    font-size: 24px;
  }
  .service-card .box-title {
    max-width: 210px;
  }
  .service-card .box-title a {
    font-size: 20px;
  }
}

.service-slider1 {
  margin-right: -315px;
  /* Hight Resoulation devices */
  /* Medium Large devices */
  /* Small devices */
}
.service-slider1 .swiper-wrapper:has(.swiper-slide.swiper-slide-prev) {
  margin-left: 428px;
}
.service-slider1 .swiper-slide {
  display: inline-block;
  width: auto;
}
.service-slider1 .swiper-slide.swiper-slide-active .service-card .box-thumb img {
  width: 828px;
}
.service-slider1 .swiper-slide.swiper-slide-active .service-card .box-content {
  opacity: 0;
}
.service-slider1 .swiper-slide.swiper-slide-active .service-card .box-content.style2 {
  opacity: 1;
  bottom: 0px;
}
.service-slider1 .swiper-slide.swiper-slide-active .service-card .icon-btn {
  opacity: 1;
  visibility: visible;
}
.service-slider1 .slider-scrollbar {
  max-width: calc(100% - 315px);
}
@media (min-width: 1922px) {
  .service-slider1 {
    margin-right: 0;
  }
}
@media (max-width: 1299px) {
  .service-slider1 {
    margin-right: 0;
  }
  .service-slider1 .swiper-wrapper:has(.swiper-slide.swiper-slide-prev) {
    margin-left: 0;
  }
  .service-slider1 .swiper-slide.swiper-slide-active .service-card .box-thumb img {
    width: 100%;
  }
  .service-slider1 .slider-scrollbar {
    max-width: 100%;
  }
}
@media (max-width: 767px) {
  .service-slider1 .swiper-slide.swiper-slide-active .service-card .box-content {
    margin: 20px;
  }
}

/* Service Card 2---------------------------------- */
.service-card2 {
  position: relative;
  background: var(--title-color);
  overflow: hidden;
  z-index: 1;
  padding: 40px;
  /* Small devices */
  /* Extra small devices */
}
.service-card2 .box-img {
  position: absolute;
  inset: 0;
  z-index: -1;
  transition: 0.4s;
  opacity: 0;
}
.service-card2 .box-img img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.service-card2 .box-img:after {
  content: "";
  position: absolute;
  inset: 0;
  background: linear-gradient(180deg, rgba(32, 40, 68, 0.16) -25.28%, rgba(32, 40, 68, 0.8) 50.11%);
}
.service-card2 .box-icon {
  transition: 0.4s;
  width: 100px;
  height: 100px;
  line-height: 100px;
  border-radius: 50%;
  background: var(--black-color2);
  text-align: center;
  margin-bottom: 40px;
  position: relative;
  z-index: 1;
}
.service-card2 .box-icon:after {
  content: "";
  position: absolute;
  inset: 0;
  transform: scale(0);
  border-radius: 50%;
  background-color: var(--theme-color);
  transform-origin: center;
  transform-style: preserve-3d;
  transition: all 0.5s cubic-bezier(0.62, 0.21, 0.45, 1.52);
  z-index: -1;
}
.service-card2 .box-icon img {
  transition: all 0.4s ease-in-out;
  filter: brightness(99);
}
.service-card2 .box-title {
  margin-top: -0.35em;
  margin-bottom: 15px;
  font-weight: 600;
  font-size: 28px;
}
.service-card2 .box-title a {
  color: var(--white-color);
}
.service-card2 .box-title:hover a {
  color: var(--theme-color);
}
.service-card2 .box-text {
  margin-bottom: -0.5em;
  color: var(--light-color);
}
.service-card2 .th-btn {
  margin-top: 40px;
}
.service-card2 .th-btn:after {
  transition: 0.4s;
}
.service-card2:hover .box-img {
  opacity: 1;
}
.service-card2:hover .box-icon:after {
  transform: scaleX(1);
}
.service-card2:hover .box-icon img {
  filter: none;
  transform: scale(-1) rotate(180deg);
}
.service-card2:hover .th-btn {
  color: var(--title-color);
}
.service-card2:hover .th-btn:after {
  width: 100%;
}
.service-card2:hover .th-btn:hover:after {
  background: var(--white-color);
}
@media (max-width: 767px) {
  .service-card2 .box-title {
    font-size: 24px;
  }
}
@media (max-width: 375px) {
  .service-card2 {
    padding: 35px;
  }
  .service-card2 .box-icon {
    margin-bottom: 30px;
  }
}

/* Service Card 3---------------------------------- */
.service-slider3 {
  margin-right: -140px;
  /* Hight Resoulation devices */
  /* Extra large devices */
}
.service-slider3 .slider-scrollbar {
  max-width: calc(100% - 140px);
}
@media (min-width: 1922px) {
  .service-slider3 {
    margin-right: 0;
  }
  .service-slider3 .slider-scrollbar {
    max-width: 100%;
  }
}
@media (max-width: 1500px) {
  .service-slider3 {
    margin-right: 0;
  }
  .service-slider3 .slider-scrollbar {
    max-width: 100%;
  }
}

.service-card3 {
  background: var(--black-color2);
  padding: 48px 32px;
  /* Medium devices */
  /* Small devices */
}
.service-card3 .box-icon {
  height: 100px;
  width: 100px;
  border-radius: 50%;
  display: inline-block;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  border: 1px solid var(--light-color);
  margin-bottom: 48px;
  position: relative;
  z-index: 1;
  transition: 0.4s;
}
.service-card3 .box-icon:after {
  content: "";
  position: absolute;
  inset: 0;
  transform: scale(0);
  border-radius: 50%;
  background-color: var(--theme-color);
  transform-origin: center;
  transform-style: preserve-3d;
  transition: all 0.5s cubic-bezier(0.62, 0.21, 0.45, 1.52);
  z-index: -1;
}
.service-card3 .box-icon .color-masking {
  transition: 0.4s;
}
.service-card3 .box-title {
  font-size: 28px;
}
.service-card3 .box-title a {
  color: var(--white-color);
}
.service-card3 .box-title a:hover {
  color: var(--theme-color);
}
.service-card3 .box-text {
  color: var(--light-color);
  margin-bottom: 40px;
  font-size: 18px;
}
.service-card3:hover .box-icon {
  border-color: var(--theme-color);
}
.service-card3:hover .box-icon:after {
  transform: scaleX(1);
}
.service-card3:hover .box-icon .color-masking {
  transform: scale(-1) rotate(180deg);
  filter: brightness(99);
}
.service-card3:hover .th-btn {
  color: var(--title-color);
}
.service-card3:hover .th-btn:after {
  width: 100%;
}
.service-card3:hover .th-btn:hover:after {
  background: var(--white-color);
}
@media (max-width: 991px) {
  .service-card3 {
    padding: 40px 30px;
  }
  .service-card3 .box-icon {
    margin-bottom: 30px;
  }
  .service-card3 .box-title {
    font-size: 24px;
  }
  .service-card3 .box-text {
    font-size: 15px;
  }
}
@media (max-width: 767px) {
  .service-card3 .box-text {
    font-size: 16px;
  }
}

/* Service Card 4---------------------------------- */
.service-area-4 {
  background-repeat: no-repeat;
  background-size: 100% 50%;
  background-position: top;
}

.service-slider4 {
  margin-right: -140px;
  /* Hight Resoulation devices */
  /* Extra large devices */
}
@media (min-width: 1922px) {
  .service-slider4 {
    margin-right: 0;
  }
}
@media (max-width: 1500px) {
  .service-slider4 {
    margin-right: 0;
  }
}

.service-card4 {
  background: var(--smoke-color);
  padding: 56px;
  position: relative;
  z-index: 1;
  /* Extra large devices */
  /* Medium Large devices */
  /* Extra small devices */
}
.service-card4 .box-img {
  position: absolute;
  inset: 0;
  z-index: -1;
  transition: 0.4s;
  opacity: 0;
}
.service-card4 .box-img img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.service-card4 .box-img:after {
  content: "";
  position: absolute;
  inset: 0;
  background: var(--title-color);
  opacity: 0.6;
}
.service-card4 .box-icon {
  transition: 0.4s;
  width: 80px;
  height: 80px;
  line-height: 78px;
  border-radius: 50%;
  background: transparent;
  border: 1px solid var(--light-color);
  text-align: center;
  margin-bottom: 40px;
  position: relative;
  z-index: 1;
}
.service-card4 .box-icon:after {
  content: "";
  position: absolute;
  inset: 0;
  transform: scale(0);
  border-radius: 50%;
  background-color: var(--theme-color);
  transform-origin: center;
  transform-style: preserve-3d;
  transition: all 0.5s cubic-bezier(0.62, 0.21, 0.45, 1.52);
  z-index: -1;
}
.service-card4 .box-icon img {
  transition: all 0.4s ease-in-out;
}
.service-card4 .box-number {
  font-size: 72px;
  font-weight: 800;
  font-family: var(--title-font);
  position: absolute;
  top: 70px;
  right: 56px;
  line-height: 0.75em;
  color: transparent;
  -webkit-text-stroke: 1px var(--light-color);
}
.service-card4 .box-title {
  font-size: 28px;
  font-weight: 600;
  margin-bottom: 7px;
}
.service-card4 .box-text {
  transition: 0.4s;
}
.service-card4 .th-btn {
  margin-top: 32px;
}
.service-card4:hover .box-img {
  opacity: 1;
}
.service-card4:hover .box-icon {
  border-color: var(--theme-color);
}
.service-card4:hover .box-icon:after {
  transform: scaleX(1);
}
.service-card4:hover .box-icon img {
  filter: none;
  transform: scale(-1) rotate(180deg);
}
.service-card4:hover .box-title a {
  color: var(--white-color);
}
.service-card4:hover .box-title a:hover {
  color: var(--theme-color);
}
.service-card4:hover .box-text {
  color: var(--light-color);
}
.service-card4:hover .th-btn {
  border-color: var(--theme-color);
}
.service-card4:hover .th-btn:after {
  width: 100%;
  background: var(--theme-color);
}
.service-card4:hover .th-btn:hover {
  border-color: var(--theme-color2);
}
.service-card4:hover .th-btn:hover:after {
  background: var(--theme-color2);
}
@media (max-width: 1500px) {
  .service-card4 {
    padding: 50px;
  }
  .service-card4 .box-title {
    font-size: 24px;
  }
}
@media (max-width: 1399px) {
  .service-card4 {
    padding: 40px;
  }
}
@media (max-width: 575px) {
  .service-card4 {
    padding: 30px;
  }
  .service-card4 .box-icon {
    margin-bottom: 30px;
  }
  .service-card4 .box-number {
    top: 45px;
    right: 30px;
  }
}

/* Service Card 5---------------------------------- */
.service-bg-shape5-1 {
  width: 158px;
}

.service-tabs {
  border: 0;
  gap: 1px;
  padding-bottom: 53px;
  /* Large devices */
  /* Medium devices */
  /* Small devices */
  /* Extra small devices */
}
.service-tabs .nav-item {
  width: 33.26%;
}
.service-tabs .nav-item .nav-link {
  margin-bottom: 0;
  width: 100%;
  padding: 48px 20px;
  border-radius: 0;
  border: 0;
  background: var(--title-color);
  font-size: 28px;
  font-weight: 600;
  color: var(--white-color);
  position: relative;
  height: 100%;
}
.service-tabs .nav-item .nav-link:after {
  content: "";
  position: absolute;
  bottom: 40px;
  background: transparent;
  height: 40px;
  width: 60px;
  left: 50%;
  transform: translate(-50%, 100%);
  border-top: solid 40px var(--theme-color);
  border-left: solid 30px transparent;
  border-right: solid 30px transparent;
  transition: 0.4s;
  opacity: 0;
  z-index: -1;
}
.service-tabs .nav-item .nav-link .box-icon {
  position: relative;
  display: flex;
  width: 100px;
  height: 100px;
  border-radius: 50%;
  margin: auto;
  text-align: center;
  justify-content: center;
  align-items: center;
  background: var(--white-color);
  margin-bottom: 28px;
}
.service-tabs .nav-item .nav-link .box-icon .color-masking {
  filter: brightness(0.2);
  transition: 0.4s;
}
.service-tabs .nav-item .nav-link .box-icon .masking-src {
  background: var(--title-color);
  transition: 0.4s;
}
.service-tabs .nav-item .nav-link.active {
  background: var(--theme-color);
}
.service-tabs .nav-item .nav-link.active:after {
  bottom: 0;
  opacity: 1;
}
.service-tabs .nav-item .nav-link.active .box-icon .color-masking {
  filter: none;
}
.service-tabs .nav-item .nav-link.active .box-icon .masking-src {
  background: var(--theme-color);
}
@media (max-width: 1199px) {
  .service-tabs .nav-item .nav-link {
    font-size: 24px;
  }
}
@media (max-width: 991px) {
  .service-tabs .nav-item {
    width: 33.23%;
  }
  .service-tabs .nav-item .nav-link {
    padding: 30px 20px;
    font-size: 18px;
  }
  .service-tabs .nav-item .nav-link .box-icon {
    width: 90px;
    height: 90px;
    margin-bottom: 20px;
  }
  .service-tabs .nav-item .nav-link:after {
    height: 25px;
    width: 40px;
    border-top: solid 25px var(--theme-color);
    border-left: solid 20px transparent;
    border-right: solid 20px transparent;
  }
}
@media (max-width: 767px) {
  .service-tabs {
    justify-content: center;
  }
  .service-tabs .nav-item {
    width: 49.9%;
  }
  .service-tabs .nav-item .nav-link:after {
    display: none;
  }
}
@media (max-width: 575px) {
  .service-tabs .nav-item {
    width: 100%;
  }
}

.service-tab-content {
  display: flex;
  background: var(--title-color);
  /* Medium Large devices */
  /* Medium Large devices */
  /* Medium devices */
  /* Small devices */
  /* Extra small devices */
}
.service-tab-content .service-tab-list-wrap {
  padding: 80px 48px;
  width: 33.333%;
  flex: none;
}
.service-tab-content .service-tab-list-wrap .service-tab-list {
  position: relative;
  padding-left: 80px;
  z-index: 1;
}
.service-tab-content .service-tab-list-wrap .service-tab-list:before {
  content: "";
  position: absolute;
  left: 15px;
  top: 25px;
  height: 24px;
  background: var(--theme-color);
  clip-path: path("M8.55186 18.5479C11.0008 14.7831 20.008 5.09279 27.4776 0.0585812C27.7949 -0.155222 28.1531 0.264464 27.8901 0.542246C20.7938 8.03629 13.2087 16.513 8.85249 23.8428C8.73114 24.047 8.43819 24.0541 8.31139 23.8533C6.11928 20.381 4.2392 15.3898 0.209389 13.8603C-0.089979 13.7467 -0.0612074 13.3235 0.250089 13.2485C4.1119 12.318 5.92146 15.6208 8.55186 18.5475V18.5479Z");
  width: 27px;
}
.service-tab-content .service-tab-list-wrap .service-tab-list:after {
  content: "";
  position: absolute;
  height: 56px;
  width: 56px;
  border-radius: 50%;
  background: var(--black-color2);
  left: 0;
  top: 9px;
  z-index: -1;
}
.service-tab-content .service-tab-list-wrap .service-tab-list:not(:last-child) {
  border-bottom: 1px solid var(--body-color);
  padding-bottom: 64px;
  margin-bottom: 64px;
}
.service-tab-content .service-tab-list-wrap .service-tab-list .box-title {
  font-size: 28px;
  font-weight: 600;
  color: var(--white-color);
}
.service-tab-content .service-tab-list-wrap .service-tab-list .box-text {
  font-size: 18px;
  color: var(--light-color);
}
.service-tab-content .service-tab-thumb img {
  height: 100%;
  object-fit: cover;
}
@media (max-width: 1399px) {
  .service-tab-content .service-tab-list-wrap {
    padding: 40px;
  }
  .service-tab-content .service-tab-list-wrap .service-tab-list:not(:last-child) {
    padding-bottom: 40px;
    margin-bottom: 40px;
  }
}
@media (max-width: 1299px) {
  .service-tab-content .service-tab-list-wrap {
    padding: 40px;
  }
  .service-tab-content .service-tab-list-wrap .service-tab-list {
    padding-left: 0;
    padding-top: 80px;
  }
  .service-tab-content .service-tab-list-wrap .service-tab-list:after {
    top: 0px;
  }
  .service-tab-content .service-tab-list-wrap .service-tab-list:before {
    top: 15px;
  }
  .service-tab-content .service-tab-list-wrap .service-tab-list:not(:last-child) {
    padding-bottom: 40px;
    margin-bottom: 40px;
  }
  .service-tab-content .service-tab-list-wrap .service-tab-list .box-text {
    font-size: 16px;
  }
}
@media (max-width: 991px) {
  .service-tab-content {
    flex-direction: column-reverse;
    flex-wrap: wrap;
  }
  .service-tab-content .service-tab-list-wrap {
    width: 100%;
  }
  .service-tab-content .service-tab-list-wrap .service-tab-list {
    padding-left: 80px;
    padding-top: 0;
  }
  .service-tab-content .service-tab-list-wrap .service-tab-list:after {
    top: 9px;
  }
  .service-tab-content .service-tab-list-wrap .service-tab-list:before {
    top: 25px;
  }
}
@media (max-width: 767px) {
  .service-tab-content .service-tab-list-wrap .service-tab-list .box-title {
    font-size: 24px;
  }
}
@media (max-width: 575px) {
  .service-tab-content .service-tab-list-wrap .service-tab-list {
    padding-left: 0;
    padding-top: 80px;
  }
  .service-tab-content .service-tab-list-wrap .service-tab-list:after {
    top: 0px;
  }
  .service-tab-content .service-tab-list-wrap .service-tab-list:before {
    top: 15px;
  }
  .service-tab-content .service-tab-list-wrap .service-tab-list:not(:last-child) {
    padding-bottom: 40px;
    margin-bottom: 40px;
  }
  .service-tab-content .service-tab-list-wrap .service-tab-list .box-text {
    font-size: 16px;
  }
}

/* Service card 2 style2 ---------------------------------- */
.service-card2.style2 {
  background: var(--smoke-color2);
}
.service-card2.style2 .box-icon {
  background: var(--white-color);
  border: 1px solid var(--light-color);
}
.service-card2.style2 .box-icon img {
  filter: none;
}
.service-card2.style2 .box-icon:after {
  display: none;
}
.service-card2.style2 .box-title a {
  color: var(--title-color);
}
.service-card2.style2 .box-title a:hover {
  color: var(--theme-color);
}
.service-card2.style2 .box-text {
  color: var(--body-color);
  transition: 0.4sth;
}
.service-card2.style2:hover .box-icon {
  border: 1px solid var(--white-color);
}
.service-card2.style2:hover .box-title a {
  color: var(--white-color);
}
.service-card2.style2:hover .box-title a:hover {
  color: var(--theme-color);
}
.service-card2.style2:hover .box-text {
  color: var(--light-color);
}
.service-card2.style2:hover .th-btn {
  color: var(--white-color);
}
.service-card2.style2:hover .th-btn:hover {
  color: var(--theme-color);
}

/* Service Details ---------------------------------- */
.page-title {
  margin-bottom: 20px;
}

.page-img {
  margin-bottom: 40px;
  overflow: hidden;
  border-radius: 16px;
  position: relative;
  /* Small devices */
}
.page-img img {
  width: 100%;
}
.page-img .tag {
  position: absolute;
  top: 40px;
  left: 40px;
  background: var(--theme-color2);
  color: var(--white-color);
  border-radius: 30px;
  padding: 2px 17px;
}
@media (max-width: 767px) {
  .page-img {
    border-radius: 10px;
  }
  .page-img .tag {
    top: 20px;
    left: 20px;
  }
}

.page-single {
  margin-bottom: 30px;
}

.service-page-list-wrap {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 40px;
  /* Large devices */
}
.service-page-list-wrap .single-service-list {
  max-width: 550px;
}
.service-page-list-wrap .single-service-list .box-title {
  margin-bottom: 10px;
}
.service-page-list-wrap .single-service-list .box-text {
  font-size: 18px;
}
@media (max-width: 1199px) {
  .service-page-list-wrap {
    grid-template-columns: repeat(1, 1fr);
  }
  .service-page-list-wrap .single-service-list {
    max-width: none;
  }
}

.service-process-wrap {
  display: flex;
  position: relative;
  gap: 30px 15px;
  /* Extra large devices */
  /* Large devices */
  /* Extra small devices */
}
.service-process-wrap .service-process-line {
  position: absolute;
  top: 4px;
  left: 40px;
  z-index: -1;
  /* Extra large devices */
}
@media (max-width: 1500px) {
  .service-process-wrap .service-process-line {
    display: none;
  }
}
.service-process-wrap .service-process-card .box-icon {
  width: 100px;
  height: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: var(--smoke-color2);
  margin-bottom: 24px;
}
.service-process-wrap .service-process-card .box-title {
  font-size: 18px;
  font-weight: 600;
  margin-bottom: 12px;
}
.service-process-wrap .service-process-card .box-text {
  font-size: 14px;
  font-family: var(--title-font);
}
@media (max-width: 1500px) {
  .service-process-wrap {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
  }
}
@media (max-width: 1199px) {
  .service-process-wrap {
    grid-template-columns: repeat(2, 1fr);
  }
}
@media (max-width: 575px) {
  .service-process-wrap {
    grid-template-columns: repeat(1, 1fr);
  }
}

.page-img-grid {
  display: flex;
  gap: 24px;
  /* Extra small devices */
}
.page-img-grid .page-img {
  height: 100%;
}
.page-img-grid .page-img img {
  height: 100%;
}
@media (max-width: 575px) {
  .page-img-grid {
    display: block;
  }
}

/*------------------- 4.00. Process -------------------*/
/* Process Card ---------------------------------- */
.process-thumb1-1 {
  position: absolute;
  right: 0;
  top: 150px;
  bottom: 0;
  max-width: 747px;
  /* Hight Resoulation devices */
  /* Extra large devices */
  /* Medium Large devices */
  /* Medium Large devices */
  /* Large devices */
}
.process-thumb1-1 img {
  height: 100%;
  object-fit: cover;
  max-width: fit-content;
}
@media (min-width: 1922px) {
  .process-thumb1-1 {
    width: 42%;
    max-width: none;
  }
  .process-thumb1-1 img {
    max-width: none;
    width: 100%;
  }
}
@media (max-width: 1500px) {
  .process-thumb1-1 {
    max-width: 537px;
  }
}
@media (max-width: 1399px) {
  .process-thumb1-1 {
    max-width: 520px;
  }
}
@media (max-width: 1299px) {
  .process-thumb1-1 {
    max-width: 460px;
  }
}
@media (max-width: 1199px) {
  .process-thumb1-1 {
    display: none;
  }
}

.process-card-wrap {
  margin-top: -60px;
  /* Medium devices */
}
.process-card-wrap:nth-child(2) .process-card {
  margin-top: 150px;
}
.process-card-wrap:nth-child(1) .process-card {
  margin-top: 220px;
}
@media (max-width: 991px) {
  .process-card-wrap {
    margin-top: 24px;
  }
  .process-card-wrap:nth-child(2) .process-card {
    margin-top: 50px;
  }
  .process-card-wrap:nth-child(1) {
    margin-top: 0;
  }
  .process-card-wrap:nth-child(1) .process-card {
    margin-top: 50px;
  }
}

.process-card {
  position: relative;
  z-index: 2;
  text-align: center;
  background: var(--black-color2);
  padding: 0 20px 90px;
  margin-top: 50px;
  /* Medium Large devices */
  /* Extra small devices */
}
.process-card .box-number {
  width: 100px;
  height: 100px;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  border-radius: 50%;
  background: var(--theme-color);
  color: var(--title-color);
  font-size: 40px;
  font-weight: 600;
  font-family: var(--title-font);
  border: 5px solid var(--title-color);
  margin-bottom: -20px;
  transform: translate(0, -50px);
}
.process-card .box-text {
  font-size: 18px;
  font-weight: 600;
  font-family: var(--title-font);
  color: var(--white-color);
  margin-bottom: 25px;
}
.process-card .box-title {
  margin-bottom: 0;
  font-size: 40px;
  font-weight: 600;
  color: var(--white-color);
  line-height: 0.8;
}
@media (max-width: 1299px) {
  .process-card {
    padding-bottom: 60px;
  }
  .process-card .box-title {
    font-size: 30px;
  }
}
@media (max-width: 375px) {
  .process-card .box-text {
    font-size: 14px;
    margin-bottom: 20px;
  }
  .process-card .box-title {
    font-size: 24px;
  }
}

/* Process Card 2---------------------------------- */
.process-card-thumb-wrap {
  position: relative;
}
.process-card-thumb-wrap .grid_lines {
  z-index: 1;
}
.process-card-thumb-wrap .grid_lines .grid_line {
  background-color: rgba(255, 255, 255, 0.2);
}
.process-card-thumb-wrap .process-card-thumb {
  position: absolute;
  inset: 0;
  opacity: 0;
  transition: 0.4s;
}
.process-card-thumb-wrap .process-card-thumb img {
  min-height: 300px;
  object-fit: cover;
}
.process-card-thumb-wrap .process-card-thumb:after {
  content: "";
  position: absolute;
  inset: 0;
  background: linear-gradient(178.1deg, rgba(19, 24, 43, 0) -9.68%, rgba(19, 24, 43, 0.7) 55.45%);
}
.process-card-thumb-wrap .process-card-thumb.active {
  position: relative;
  opacity: 1;
}

.process-wrap2 {
  position: relative;
  overflow: hidden;
}
.process-wrap2 .process-wrap-content {
  z-index: 1;
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  /* Large devices */
  /* Small devices */
}
@media (max-width: 1199px) {
  .process-wrap2 .process-wrap-content {
    position: relative;
    bottom: auto;
    left: auto;
    right: auto;
  }
  .process-wrap2 .process-wrap-content:before, .process-wrap2 .process-wrap-content:after {
    content: "";
    position: absolute;
    background: rgba(255, 255, 255, 0.1);
    z-index: 1;
  }
  .process-wrap2 .process-wrap-content:after {
    left: 50%;
    top: 0;
    bottom: 0;
    width: 1px;
    height: 100%;
  }
  .process-wrap2 .process-wrap-content:before {
    top: 50%;
    left: 0;
    right: 0;
    width: 100%;
    height: 1px;
    z-index: 2;
  }
}
@media (max-width: 767px) {
  .process-wrap2 .process-wrap-content:after, .process-wrap2 .process-wrap-content:before {
    display: none;
  }
}

.process-card-wrap2.item-active .process-card2 .box-content {
  opacity: 0;
}
.process-card-wrap2.item-active .process-card2 .active-box-content {
  visibility: visible;
  opacity: 1;
  bottom: 0;
  /* Large devices */
}
@media (max-width: 1199px) {
  .process-card-wrap2.item-active .process-card2 .active-box-content:before {
    opacity: 0.5;
  }
}

.process-card2 {
  position: relative;
  /* Extra large devices */
  /* Medium Large devices */
  /* Large devices */
  /* Small devices */
  /* Extra small devices */
}
.process-card2 .box-number {
  font-size: 40px;
  font-weight: 600;
  color: var(--white-color);
  margin-bottom: 12px;
  margin-top: -0.25em;
}
.process-card2 .box-title {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: -0.4em;
  color: var(--white-color);
}
.process-card2 .box-text {
  color: var(--white-color);
  margin-top: 24px;
}
.process-card2 .box-content {
  opacity: 1;
  padding: 48px 53px 48px 48px;
  transition: 0.4s;
}
.process-card2 .active-box-content {
  padding: 48px 53px 48px 48px;
  position: absolute;
  bottom: -50px;
  visibility: hidden;
  opacity: 0;
  z-index: 1;
  transition: 0.4s;
  backdrop-filter: blur(10px);
}
.process-card2 .active-box-content:before {
  content: "";
  position: absolute;
  inset: 0;
  background: var(--theme-color);
  opacity: 0.5;
  z-index: -1;
  transition: 0.4s;
}
@media (max-width: 1500px) {
  .process-card2 .box-content,
  .process-card2 .active-box-content {
    padding: 35px;
  }
}
@media (max-width: 1399px) {
  .process-card2 .box-number {
    font-size: 30px;
  }
  .process-card2 .box-title {
    font-size: 20px;
  }
}
@media (max-width: 1199px) {
  .process-card2 {
    background: var(--black-color2);
  }
  .process-card2 .box-content {
    opacity: 0;
    display: none;
  }
  .process-card2 .active-box-content {
    position: relative;
    bottom: auto;
    opacity: 1;
    visibility: visible;
  }
  .process-card2 .active-box-content:before {
    opacity: 0;
  }
}
@media (max-width: 767px) {
  .process-card2 {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
  }
}
@media (max-width: 575px) {
  .process-card2 .box-content,
  .process-card2 .active-box-content {
    padding: 30px 20px;
  }
  .process-card2 .box-number {
    font-size: 24px;
  }
  .process-card2 .box-title {
    font-size: 18px;
  }
}

/*------------------- 4.00. Pricing -------------------*/
/* Pricing Card ---------------------------------- */
.pricing-tabs {
  margin-top: 44px;
  margin-bottom: 60px;
  /* Extra small devices */
}
@media (max-width: 575px) {
  .pricing-tabs {
    margin-bottom: 0px;
  }
}
.pricing-tabs.style2 .switch-area .toggler {
  color: var(--white-color);
}
.pricing-tabs.style2 .switch-area .toggle {
  background: var(--black-color2);
  border: 1px solid var(--body-color);
}
.pricing-tabs.style3 .switch-area .toggler {
  color: var(--title-color);
}

.switch-area {
  display: inline-flex;
  align-items: center;
  gap: 20px;
}
.switch-area .toggler {
  transition: 0.2s;
  font-weight: 500;
  font-size: 18px;
  font-family: var(--body-font);
  color: var(--title-color);
  background: transparent;
  margin-bottom: -0.4em;
  cursor: pointer;
}
.switch-area .toggler.toggler--is-active {
  color: var(--theme-color);
}
.switch-area .toggle {
  position: relative;
  width: 90px;
  height: 40px;
  border-radius: 100px;
  background-color: var(--title-color);
  overflow: hidden;
  box-shadow: inset 0 0 2px 1px rgba(0, 0, 0, 0.05);
  /* Extra small devices */
}
@media (max-width: 575px) {
  .switch-area .toggle {
    width: 60px;
    height: 30px;
  }
}
.switch-area .check {
  position: absolute;
  display: block;
  cursor: pointer;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0;
  z-index: 6;
  visibility: visible;
}
.switch-area .check:checked ~ .switch {
  right: 2px;
  left: 57.5%;
  transition: 0.25s cubic-bezier(0.785, 0.135, 0.15, 0.86);
  transition-property: left, right;
  transition-delay: 0.08s, 0s;
}
.switch-area .switch {
  position: absolute;
  left: 4px;
  top: 4px;
  bottom: 4px;
  right: 57.5%;
  background-color: var(--theme-color);
  border-radius: 36px;
  z-index: 1;
  transition: 0.25s cubic-bezier(0.785, 0.135, 0.15, 0.86);
  transition-property: left, right;
  transition-delay: 0s, 0.08s;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.wrapper-full.hide {
  display: none;
}

.price-card {
  position: relative;
  padding: 48px 36px;
  border-radius: 8px;
  background: var(--white-color);
  border: 1px solid var(--light-color);
  z-index: 1;
  /* Medium Large devices */
}
.price-card .card-bg-img {
  position: absolute;
  inset: 0;
  background-color: var(--white-color);
  mask-size: cover;
  mask-position: right;
  z-index: -1;
  opacity: 0.03;
}
.price-card.active {
  background: var(--title-color);
}
.price-card .box-title {
  margin-bottom: -0.15em;
  font-size: 28px;
  font-weight: 600;
}
.price-card .box-subtitle {
  margin-top: 4px;
  margin-bottom: 40px;
  padding-bottom: 40px;
  border-bottom: 1px solid var(--light-color);
}
.price-card .price-card_price {
  font-size: 40px;
  font-weight: 600;
  font-family: var(--title-font);
  margin-bottom: 0;
}
.price-card .price-card_price .duration {
  font-size: 16px;
  font-weight: 400;
  font-family: var(--body-font);
  margin-left: 5px;
  color: var(--body-color);
}
.price-card .box-text {
  margin-top: 8px;
  margin-bottom: 29px;
  font-size: 16px;
}
.price-card .checklist {
  display: inline-block;
}
.price-card .checklist li {
  color: var(--body-color);
  align-items: start;
  font-size: 16px;
  font-weight: 500;
  padding-left: 0;
}
.price-card .checklist li i {
  transition: 0.4s;
  color: var(--title-color);
  top: 3px;
}
.price-card .checklist li:before {
  display: none;
}
.price-card .checklist li.unavailable i {
  color: var(--light-color);
  opacity: 0.5;
}
.price-card .btn-wrap {
  margin-top: 43px;
}
.price-card.active .box-title {
  color: var(--white-color);
}
.price-card.active .box-subtitle {
  color: var(--white-color);
  border-color: var(--body-color);
}
.price-card.active .price-card_price {
  color: var(--white-color);
}
.price-card.active .price-card_price .duration {
  color: var(--light-color);
}
.price-card.active .box-text {
  color: var(--light-color);
}
.price-card.active .checklist li {
  color: var(--light-color);
}
.price-card.active .checklist li i {
  color: var(--light-color);
}
.price-card.active .th-btn {
  background: var(--theme-color);
  border-color: var(--theme-color);
  color: var(--white-color);
}
.price-card.active .th-btn:after {
  background: var(--white-color);
}
.price-card.active .th-btn:hover {
  border-color: var(--white-color);
  color: var(--theme-color);
}
@media (max-width: 1399px) {
  .price-card {
    padding: 40px 30px;
  }
  .price-card .box-title {
    font-size: 24px;
  }
  .price-card .price-card_price {
    font-size: 36px;
  }
  .price-card .box-text {
    font-size: 14px;
  }
}

/* Pricing Card 2---------------------------------- */
.price-card.style2 {
  border-radius: 0;
  background: var(--black-color2);
  border: 0;
  position: relative;
}
.price-card.style2 .card-bg-img {
  background-color: var(--title-color);
  opacity: 0.5;
}
.price-card.style2 .box-title {
  color: var(--white-color);
}
.price-card.style2 .box-subtitle {
  border-bottom: 1px solid var(--body-color);
  color: var(--light-color);
}
.price-card.style2 .price-card_price {
  color: var(--white-color);
}
.price-card.style2 .price-card_price .duration {
  color: var(--light-color);
}
.price-card.style2 .box-text {
  color: var(--light-color);
}
.price-card.style2 .checklist li {
  color: var(--light-color);
}
.price-card.style2 .checklist li:not(:last-child) {
  margin-bottom: 27px;
}
.price-card.style2 .checklist li i {
  color: var(--theme-color);
}
.price-card.style2 .checklist li.unavailable i {
  opacity: 1;
  color: var(--light-color);
}
.price-card.style2.active {
  background: linear-gradient(180deg, #3282FB -49.17%, #13182B 115.56%);
}
.price-card.style2.active .card-bg-img {
  opacity: 0.05;
  background-color: var(--white-color);
}
.price-card.style2.active .box-subtitle {
  border-bottom-color: var(--light-color);
}
.price-card.style2.active .th-btn {
  color: var(--title-color);
}

/* Pricing Card 3---------------------------------- */
.price-card.style3 {
  border-radius: 0;
  background: var(--black-color2);
  border: 0;
  position: relative;
}
.price-card.style3 .card-bg-img {
  background-color: var(--title-color);
  opacity: 0.5;
}
.price-card.style3 .box-title {
  color: var(--white-color);
}
.price-card.style3 .box-subtitle {
  border-bottom: 1px solid var(--body-color);
  color: var(--light-color);
}
.price-card.style3 .price-card_price {
  color: var(--white-color);
}
.price-card.style3 .price-card_price .duration {
  color: var(--light-color);
}
.price-card.style3 .box-text {
  color: var(--light-color);
}
.price-card.style3 .checklist li {
  color: var(--light-color);
}
.price-card.style3 .checklist li:not(:last-child) {
  margin-bottom: 27px;
}
.price-card.style3 .checklist li i {
  color: var(--theme-color);
}
.price-card.style3 .checklist li.unavailable i {
  opacity: 1;
  color: var(--light-color);
}
.price-card.style3.active {
  background: linear-gradient(180.35deg, #FF4F38 -30.23%, #13182B 116.2%);
}
.price-card.style3.active .card-bg-img {
  opacity: 0.05;
  background-color: var(--white-color);
}
.price-card.style3.active .box-text {
  color: var(--white-color);
}
.price-card.style3.active .box-subtitle {
  border-bottom-color: var(--light-color);
}
.price-card.style3.active .checklist li {
  color: var(--white-color);
}
.price-card.style3.active .checklist li i {
  color: var(--white-color);
}

/* Pricing Card 4---------------------------------- */
.price-card.style4 {
  border-radius: 0;
}
.price-card.style4.active .th-btn {
  color: var(--title-color);
}

/*------------------- 4.00. Feature -------------------*/
/* Feature card ---------------------------------- */
.feature-area-1 {
  position: relative;
  padding-top: 60px;
  margin-top: -173px;
  z-index: 2;
  /* Hight Resoulation devices */
  /* Extra large devices */
  /* Medium Large devices */
  /* Medium Large devices */
  /* Large devices */
  /* Medium devices */
}
.feature-area-1 .feature-bg-wrap {
  position: absolute;
  inset: 0;
}
.feature-area-1 .feature-bg-shape1-1 {
  position: absolute;
  width: 788px;
  height: 100%;
  bottom: 0;
  left: 0;
  border-top: solid 285px var(--theme-color);
  border-left: solid 394px var(--theme-color);
  border-right: solid 394px transparent;
  border-bottom: solid 285px transparent;
}
@media (min-width: 1922px) {
  .feature-area-1 {
    margin-top: -200px;
  }
}
@media (max-width: 1500px) {
  .feature-area-1 {
    padding-top: 70px;
    margin-top: -144px;
  }
}
@media (max-width: 1399px) {
  .feature-area-1 {
    margin-top: -130px;
  }
  .feature-area-1 .feature-bg-shape1-1 {
    width: 600px;
    border-top: solid 285px var(--theme-color);
    border-left: solid 300px var(--theme-color);
    border-right: solid 300px transparent;
    border-bottom: solid 285px transparent;
  }
}
@media (max-width: 1299px) {
  .feature-area-1 {
    margin-top: -115px;
  }
}
@media (max-width: 1199px) {
  .feature-area-1 {
    border-radius: 100px 100px 0 0;
    padding-top: 120px;
  }
  .feature-area-1 .feature-bg-wrap {
    mask-image: none !important;
  }
  .feature-area-1 .feature-bg-shape1-1 {
    display: none;
  }
}
@media (max-width: 991px) {
  .feature-area-1 {
    margin-top: 0;
    border-radius: 0;
    padding-top: 80px;
  }
}

.feature-card {
  padding: 30px;
  position: relative;
  box-shadow: 0px 10px 30px -6px rgba(12, 12, 13, 0.12);
  border-radius: 100px;
  background: var(--white-color);
  z-index: 1;
  overflow: hidden;
  text-align: center;
  transition: 0.4s;
  /* Medium Large devices */
  /* Large devices */
}
.feature-card .feature-card-bg-shape {
  position: absolute;
  left: 50%;
  bottom: 22px;
  transform: translate(-50%, 0);
  z-index: -1;
  transition: 0.4s;
  width: max-content;
}
.feature-card .box-icon {
  display: inline-block;
  margin-bottom: 30px;
  transition: 0.4s;
}
.feature-card .box-title {
  font-size: 24px;
  font-weight: 700;
  transition: 0.4s;
  margin-bottom: 6px;
}
.feature-card .box-text {
  margin-bottom: 11px;
  transition: 0.4s;
}
.feature-card .link-btn {
  color: var(--title-color);
  font-size: 14px;
  font-weight: 700;
  text-transform: capitalize;
}
.feature-card .link-btn:before {
  background: var(--title-color);
}
.feature-card:hover .box-icon img {
  transform: rotateY(180deg);
}
@media (max-width: 1299px) {
  .feature-card {
    border-radius: 50px;
  }
}
@media (max-width: 1199px) {
  .feature-card .box-title {
    font-size: 24px;
  }
}

/*------------------- 4.00. Project -------------------*/
/* Project Card -------------------------------*/
.project-card {
  position: relative;
  overflow: hidden;
  border-radius: 24px;
  /* Medium devices */
  /* Extra small devices */
  /* Extra small devices */
}
.project-card .box-img {
  overflow: hidden;
  position: relative;
  border-radius: 24px;
}
.project-card .box-img img {
  width: 100%;
  object-fit: cover;
  transition: 0.4s;
  filter: blur(5px);
}
.project-card .box-img:after {
  content: "";
  position: absolute;
  inset: 0;
  background: linear-gradient(180deg, rgba(19, 24, 43, 0) 17.42%, rgba(19, 24, 43, 0.8) 75.87%);
  transition: 0.4s;
  opacity: 0;
}
.project-card .box-content {
  padding: 40px 36px 40px;
  background-color: transparent;
  border-radius: 0px;
  position: absolute;
  z-index: 3;
  transition: 0.4s ease-in-out;
  left: 0;
  right: 0;
  bottom: 0;
  opacity: 0;
  visibility: hidden;
}
.project-card .box-number {
  font-size: 40px;
  font-weight: 600;
  font-family: var(--title-font);
  color: var(--white-color);
  line-height: 0.7em;
  margin-bottom: 22px;
}
.project-card .box-title {
  font-weight: 600;
  font-size: 28px;
  margin-top: -0.2em;
  margin-bottom: 5px;
}
.project-card .box-title a {
  color: var(--white-color);
}
.project-card .box-title a:hover {
  color: var(--theme-color);
}
.project-card .box-text {
  color: var(--white-color);
}
.project-card:hover .project-img img {
  filter: grayscale(1);
}
.project-card:hover .project-content {
  opacity: 1;
  visibility: visible;
  left: 0;
}
.project-card:hover .project-card-bg-shape {
  width: 100%;
}
@media (max-width: 991px) {
  .project-card .project-content {
    margin-right: 0;
  }
}
@media (max-width: 575px) {
  .project-card .box-number {
    font-size: 24px;
    margin-bottom: 17px;
  }
  .project-card .box-title {
    font-size: 24px;
    margin-bottom: 10px;
  }
  .project-card .box-text {
    font-size: 14px;
  }
}
@media (max-width: 375px) {
  .project-card .box-content {
    padding: 30px 26px 30px;
  }
}

/* Project Card 2 -------------------------------*/
.sticky-wrap {
  display: flex;
  flex-wrap: wrap;
  gap: 80px;
  /* Medium devices */
}
@media (max-width: 991px) {
  .sticky-wrap {
    gap: 50px;
  }
}

.single-sticky-wrap {
  position: sticky;
  top: 140px;
  width: 100%;
  /* Medium devices */
}
.single-sticky-wrap.position-sticky .project-card2 {
  transform: scale3d(0.98, 0.98, 1);
}
@media (max-width: 991px) {
  .single-sticky-wrap {
    position: relative !important;
    top: 0;
  }
  .single-sticky-wrap .project-card2 {
    transform: none !important;
  }
}

.project-card2 {
  display: flex;
  flex-direction: row-reverse;
  gap: 40px;
  justify-content: space-between;
  transition: transform 0.6s cubic-bezier(0.38, 0.005, 0.215, 1);
  /* Medium devices */
  /* Extra small devices */
}
.project-card2 .box-img {
  border-radius: 24px;
  overflow: hidden;
  flex: 1;
}
.project-card2 .box-img img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.project-card2 .box-content {
  max-width: 550px;
  background: var(--white-color);
  flex: 1;
}
.project-card2 .box-number {
  font-size: 56px;
  font-weight: 700;
  font-family: var(--title-font);
  letter-spacing: 0.02em;
  line-height: 0.8em;
  color: var(--white-color);
  -webkit-text-stroke: 1px var(--title-color);
  display: block;
  margin-bottom: 32px;
  /* Large devices */
}
@media (max-width: 1199px) {
  .project-card2 .box-number {
    font-size: 40px;
  }
}
.project-card2 .box-subtitle {
  font-size: 16px;
  font-weight: 500;
  color: var(--theme-color);
  background: var(--smoke-color2);
  padding: 6px 12px;
  display: inline-block;
  margin-bottom: 32px;
}
.project-card2 .box-title {
  font-size: 40px;
  font-weight: 600;
  line-height: 1.2em;
  margin-bottom: 14px;
  /* Large devices */
}
@media (max-width: 1199px) {
  .project-card2 .box-title {
    font-size: 30px;
  }
}
.project-card2 .box-text {
  max-width: 495px;
}
.project-card2 .th-btn {
  margin-top: 64px;
  /* Large devices */
}
@media (max-width: 1199px) {
  .project-card2 .th-btn {
    margin-top: 40px;
  }
}
@media (max-width: 991px) {
  .project-card2 {
    display: block;
  }
  .project-card2 .box-img {
    margin-bottom: 30px;
  }
  .project-card2 .box-content {
    max-width: none;
  }
  .project-card2 .box-text {
    max-width: none;
  }
}
@media (max-width: 575px) {
  .project-card2 .box-img {
    border-radius: 16px;
  }
  .project-card2 .box-number {
    font-size: 30px;
    margin-bottom: 24px;
  }
  .project-card2 .box-subtitle {
    margin-bottom: 30px;
  }
  .project-card2 .box-title {
    font-size: 24px;
  }
  .project-card2 .th-btn {
    margin-top: 30px;
  }
}

/* Project Card 2.1-------------------------------*/
.project-slider2 {
  margin-right: -315px;
  /* Hight Resoulation devices */
  /* Medium Large devices */
}
.project-slider2 .swiper-wrapper:has(.swiper-slide.swiper-slide-prev) {
  margin-left: 428px;
}
.project-slider2 .swiper-slide {
  display: inline-block;
  width: auto;
}
.project-slider2 .swiper-slide.swiper-slide-active .project-card2.style2 .box-thumb {
  filter: none;
}
.project-slider2 .swiper-slide.swiper-slide-active .project-card2.style2 .box-thumb img {
  width: 828px;
}
.project-slider2 .swiper-slide.swiper-slide-active .project-card2.style2 .box-content {
  transform: translate(0, 0);
}
.project-slider2 .swiper-slide.swiper-slide-active .project-card2.style2 .box-content:after {
  left: 0;
  right: auto;
  transform: translate(0, 0);
}
.project-slider2 .slider-pagination {
  padding-right: 315px;
  --swiper-pagination-bottom: 0;
}
@media (min-width: 1922px) {
  .project-slider2 {
    margin-right: 0;
  }
}
@media (max-width: 1299px) {
  .project-slider2 {
    margin-right: 0;
  }
  .project-slider2 .swiper-wrapper:has(.swiper-slide.swiper-slide-prev) {
    margin-left: 0;
  }
  .project-slider2 .slider-pagination {
    padding-right: 0;
  }
  .project-slider2 .swiper-slide.swiper-slide-active .project-card2.style2 .box-thumb img {
    width: 100%;
  }
}

.project-card2.style2 {
  display: block;
  transition: 0.4s;
  position: relative;
  padding: 0;
  overflow: hidden;
  /* Medium Large devices */
  /* Small devices */
  /* Extra small devices */
}
.project-card2.style2 .box-thumb {
  border-radius: 0;
  position: relative;
  filter: blur(7.5px);
  transition: 0.4s;
}
.project-card2.style2 .box-thumb img {
  border-radius: 0;
  transition: 0.4s;
  width: 402px;
  height: 650px;
  object-fit: cover;
}
.project-card2.style2 .box-thumb:after {
  content: "";
  position: absolute;
  inset: 0;
  background: var(--title-color);
  opacity: 0.2;
}
.project-card2.style2 .box-content {
  position: absolute;
  background: var(--black-color2);
  padding: 40px 100px 40px 36px;
  bottom: 24px;
  left: 0;
  transition: 0.4s;
  justify-content: space-between;
  transform: translate(-100%, 0);
  display: inline-block;
}
.project-card2.style2 .box-content:after {
  content: "";
  position: absolute;
  height: 100%;
  width: 3px;
  background: var(--theme-color);
  right: 0;
  top: 0;
  bottom: 0;
  z-index: 2;
  transform: translate(100%, 0);
}
.project-card2.style2 .box-text {
  font-size: 14px;
  font-weight: 400;
  font-family: var(--title-font);
  color: var(--theme-color);
  margin-top: -0.4em;
  margin-bottom: 12px;
}
.project-card2.style2 .box-title {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: -0.3em;
  position: relative;
  z-index: 1;
  /* Small devices */
}
.project-card2.style2 .box-title a {
  color: var(--white-color);
}
.project-card2.style2 .box-title a:hover {
  color: var(--theme-color);
}
@media (max-width: 767px) {
  .project-card2.style2 .box-title {
    font-size: 20px;
  }
}
.project-card2.style2 .icon-btn {
  position: absolute;
  right: 8px;
  top: 8px;
  border-radius: 0;
  color: var(--title-color);
  --btn-size: 48px;
  --btn-font-size: 36px;
}
.project-card2.style2 .icon-btn:hover {
  color: var(--theme-color);
}
@media (max-width: 1299px) {
  .project-card2.style2 .box-thumb img {
    width: 100%;
  }
}
@media (max-width: 767px) {
  .project-card2.style2 .box-thumb img {
    height: auto;
  }
}
@media (max-width: 375px) {
  .project-card2.style2 .icon-btn {
    --btn-size: 40px;
    --btn-font-size: 30px;
  }
  .project-card2.style2 .box-content {
    right: 24px;
    padding: 35px 40px 35px 24px;
  }
}

/* Project Card 3-------------------------------*/
.project-slider3 {
  /* Extra small devices */
}
.project-slider3 .swiper-slide {
  filter: blur(2.5px);
  transition: 0.4s;
}
.project-slider3 .swiper-slide.swiper-slide-active {
  filter: none;
}
.project-slider3 .swiper-slide.swiper-slide-active .project-card3 .project-content {
  transform: scaleY(1);
}
.project-slider3 .slider-scrollbar {
  margin-left: 30px;
  margin-right: 30px;
  width: -webkit-fill-available;
}
@media (max-width: 575px) {
  .project-slider3 {
    margin: 0 15px;
  }
  .project-slider3 .slider-scrollbar {
    margin-left: 0px;
    margin-right: 0px;
    width: -webkit-fill-available;
  }
}

.project-card3 {
  position: relative;
  /* Medium Large devices */
  /* Small devices */
  /* Extra small devices */
}
.project-card3 .project-img {
  transition: 0.4s;
  position: relative;
}
.project-card3 .project-img:after {
  content: "";
  position: absolute;
  inset: 0;
  border: 24px solid var(--white-color);
}
.project-card3 .project-img img {
  width: 100%;
  object-fit: cover;
}
.project-card3 .project-content {
  position: absolute;
  bottom: 24px;
  left: 24px;
  right: 24px;
  background: rgba(19, 24, 43, 0.9);
  backdrop-filter: blur(5px);
  padding: 40px;
  transition: 0.4s;
  transform: scaleY(0);
  transform-origin: bottom;
}
.project-card3 .icon-btn {
  position: absolute;
  right: 8px;
  top: 8px;
  border-radius: 0;
  --btn-font-size: 36px;
  --btn-size: 48px;
}
.project-card3 .project-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}
.project-card3 .project-meta a {
  border-radius: 30px;
  text-transform: capitalize;
  margin-bottom: 0;
}
.project-card3 .project-meta a:hover {
  background: var(--theme-color);
}
.project-card3 .box-title {
  font-size: 28px;
  font-weight: 600;
  margin-bottom: -0.32em;
  margin-top: 15px;
  max-width: 520px;
}
.project-card3 .box-title a {
  color: var(--white-color);
}
.project-card3 .box-title a:hover {
  color: var(--theme-color);
}
@media (max-width: 1299px) {
  .project-card3 .box-title {
    font-size: 24px;
  }
}
@media (max-width: 767px) {
  .project-card3 .project-img:after {
    border: 12px solid var(--white-color);
  }
  .project-card3 .project-content {
    bottom: 12px;
    left: 12px;
    right: 12px;
    padding: 30px;
  }
  .project-card3 .box-title {
    font-size: 20px;
  }
}
@media (max-width: 575px) {
  .project-card3 .project-img img {
    height: 500px;
  }
}

/* Project Card 4-------------------------------*/
.project-slider4 {
  margin-right: -140px;
  /* Hight Resoulation devices */
  /* Extra large devices */
}
@media (min-width: 1922px) {
  .project-slider4 {
    margin-right: 0;
  }
}
@media (max-width: 1500px) {
  .project-slider4 {
    margin-right: 0;
  }
}

.project-card4 {
  display: flex;
  /* Medium Large devices */
  /* Large devices */
  /* Medium devices */
  /* Small devices */
  /* Extra small devices */
  /* Extra small devices */
}
.project-card4 .box-img {
  flex: 1;
}
.project-card4 .box-img img {
  height: 100%;
  object-fit: cover;
}
.project-card4 .box-content {
  background: var(--smoke-color);
  padding: 84px;
  flex: 1;
}
.project-card4 .box-title {
  font-size: 40px;
  font-weight: 600;
  margin-bottom: 9px;
}
.project-card4 .box-title a:hover {
  color: var(--theme-color2);
}
.project-card4 .box-text {
  font-size: 18px;
  margin-bottom: 30px;
}
.project-card4 .checklist {
  margin-bottom: 44px;
}
@media (max-width: 1299px) {
  .project-card4 .box-content {
    padding: 50px;
  }
}
@media (max-width: 1199px) {
  .project-card4 .box-title {
    font-size: 30px;
  }
}
@media (max-width: 991px) {
  .project-card4 {
    display: block;
  }
}
@media (max-width: 767px) {
  .project-card4 .box-content {
    padding: 40px;
  }
  .project-card4 .box-title {
    font-size: 24px;
  }
}
@media (max-width: 575px) {
  .project-card4 .box-text {
    font-size: 16px;
  }
}
@media (max-width: 375px) {
  .project-card4 .box-content {
    padding: 30px;
  }
}

/* Project Card 5-------------------------------*/
.project-title-wrap5 {
  max-width: 485px;
}

.project-card5 {
  /* Medium devices */
  /* Small devices */
}
.project-card5 .box-img {
  position: relative;
  display: inline-block;
}
.project-card5 .box-img.small-img-width {
  max-width: 544px;
  /* Medium devices */
}
@media (max-width: 991px) {
  .project-card5 .box-img.small-img-width {
    max-width: none;
    width: 100%;
  }
}
.project-card5 .box-img img {
  width: 100%;
  filter: brightness(0.8);
}
.project-card5 .box-img .icon-btn {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%) scale(0);
  background: rgba(255, 255, 255, 0.3);
  backdrop-filter: blur(5px);
  color: var(--white-color);
  --btn-size: 80px;
  --btn-font-size: 30px;
  opacity: 0;
}
.project-card5 .box-img .icon-btn:hover {
  background: var(--theme-color);
}
.project-card5 .box-content {
  margin-top: -30px;
  position: relative;
  z-index: 1;
}
.project-card5 .box-number {
  font-size: 56px;
  font-weight: 700;
  font-family: var(--title-font);
  color: transparent;
  letter-spacing: 0.02em;
  -webkit-text-stroke: 1px var(--title-color);
  display: block;
  line-height: 0.75em;
  margin-bottom: 32px;
}
.project-card5 .box-subtitle {
  font-size: 16px;
  font-weight: 500;
  color: var(--theme-color);
  background: var(--smoke-color2);
  padding: 6px 12px;
  display: inline-block;
  margin-bottom: 32px;
}
.project-card5 .box-title {
  font-size: 40px;
  font-weight: 600;
  margin-bottom: 12px;
}
.project-card5 .th-btn {
  margin-top: 48px;
}
.project-card5:hover .box-img {
  opacity: 1;
}
.project-card5:hover .box-img .icon-btn {
  transform: translate(-50%, -50%) scale(1);
  opacity: 1;
}
@media (max-width: 991px) {
  .project-card5 .box-title {
    font-size: 30px;
  }
}
@media (max-width: 767px) {
  .project-card5 .box-title {
    font-size: 24px;
  }
  .project-card5 .th-btn {
    margin-top: 36px;
  }
}

/*------------------- 4.00. Category -------------------*/
/* Category Card -------------------------------------*/
.category-card {
  background-color: var(--smoke-color2);
  padding: 40px 15px 45px 15px;
  text-align: center;
  transition: 0.4s ease-in-out;
}
.category-card .box-shape {
  position: absolute;
  inset: 2px;
  pointer-events: none;
  background-size: 100% 100%;
}
.category-card .box-icon {
  width: 120px;
  height: 120px;
  line-height: 120px;
  background-color: var(--white-color);
  margin: 0 auto 20px auto;
}
.category-card .box-title {
  font-size: 18px;
  margin-bottom: -0.3em;
  transition: 0.1s;
}
.category-card .box-title a:hover {
  color: var(--title-color);
}
.category-card .box-subtitle {
  font-size: 14px;
  font-weight: 500;
  font-family: var(--title-font);
  transition: 0.4s ease-in-out;
}
.category-card:hover {
  background-color: var(--theme-color);
}
.category-card:hover .box-title {
  color: var(--white-color);
}
.category-card:hover .box-subtitle {
  color: var(--white-color);
}
.category-card:hover .box-icon img {
  transform: rotateY(180deg);
}

/* Category Box -------------------------------------*/
.category-box {
  text-align: center;
}
.category-box .box-icon {
  width: 150px;
  height: 150px;
  line-height: 150px;
  margin: 0 auto 20px auto;
  border-radius: 999px;
  position: relative;
  z-index: 2;
  /* Extra small devices */
}
.category-box .box-icon:before, .category-box .box-icon:after {
  content: "";
  position: absolute;
  border-radius: inherit;
  z-index: -1;
}
.category-box .box-icon:before {
  inset: 11px;
  background-color: var(--white-color);
}
.category-box .box-icon:after {
  inset: 0;
  border: 2px dashed var(--th-border-color);
}
@media (max-width: 375px) {
  .category-box .box-icon {
    width: 130px;
    height: 130px;
    line-height: 130px;
  }
}
.category-box .box-title {
  font-size: 20px;
  margin-bottom: 5px;
  /* Extra small devices */
}
@media (max-width: 375px) {
  .category-box .box-title {
    font-size: 18px;
  }
}
.category-box .box-text {
  font-size: 14px;
}
.category-box:hover .box-icon img {
  transform: rotateY(180deg);
}
.category-box:hover .box-icon:after {
  border-color: var(--theme-color);
  animation: spin 10s linear infinite;
}

/*------------------- 4.00. CTA -------------------*/
.cta-card {
  border-radius: 30px;
  padding: 55px 40px;
  position: relative;
  overflow: hidden;
  text-align: center;
  /* Extra small devices */
}
.cta-card .cta-card-bg-shape {
  mix-blend-mode: overlay;
  transition: 0.4s;
}
.cta-card .cta-card-bg-shape:after {
  content: "";
  position: absolute;
  inset: 0;
  background: var(--theme-color2);
  transition: 0.4s;
}
.cta-card:before {
  z-index: -1;
  transition: 0.4s;
  content: "";
  position: absolute;
  inset: 0;
  background: var(--title-color);
  opacity: 0.8;
}
.cta-card .box-title {
  color: var(--white-color);
  font-size: 36px;
  font-weight: 700;
  margin-bottom: 10px;
}
.cta-card .box-text {
  font-size: 16px;
  font-weight: 400;
  color: var(--white-color);
  margin-bottom: 20px;
}
.cta-card.style2 .cta-card-bg-shape {
  transform: rotateY(180deg);
}
.cta-card:hover:before {
  background: var(--theme-color);
}
.cta-card:hover .cta-card-bg-shape:after {
  background: #122F2A;
}
@media (max-width: 575px) {
  .cta-card {
    padding: 40px 30px;
  }
  .cta-card .box-title {
    font-size: 24px;
  }
}

.cta-bg-shape2-4,
.cta-bg-shape2-3 {
  /* Extra large devices */
  /* Medium Large devices */
  /* Medium Large devices */
  /* Medium devices */
}
@media (max-width: 1500px) {
  .cta-bg-shape2-4,
  .cta-bg-shape2-3 {
    max-width: 420px;
  }
}
@media (max-width: 1399px) {
  .cta-bg-shape2-4,
  .cta-bg-shape2-3 {
    max-width: 400px;
  }
}
@media (max-width: 1299px) {
  .cta-bg-shape2-4,
  .cta-bg-shape2-3 {
    max-width: 300px;
  }
}
@media (max-width: 991px) {
  .cta-bg-shape2-4,
  .cta-bg-shape2-3 {
    max-width: 250px;
  }
}

.cta-bg-shape2-2 {
  /* Medium Large devices */
}
@media (max-width: 1399px) {
  .cta-bg-shape2-2 {
    max-width: 200px;
  }
}

.cta-wrap3 {
  border-radius: 0 0 50px 50px;
  overflow: hidden;
  margin: 0 auto;
  max-width: 1720px;
  /* Extra large devices */
  /* Medium Large devices */
  /* Medium Large devices */
  /* Large devices */
  /* Medium devices */
  /* Extra small devices */
}
.cta-wrap3 .cta-content-wrap {
  padding: 80px 80px 56px;
}
.cta-wrap3 .contact-map {
  width: 100%;
  height: 434px;
}
.cta-wrap3 .contact-map iframe {
  height: 100%;
  border-radius: 0;
  filter: invert(0) grayscale(1) contrast(0.6);
}
@media (max-width: 1500px) {
  .cta-wrap3 .cta-content-wrap {
    padding: 80px 60px 56px;
  }
}
@media (max-width: 1399px) {
  .cta-wrap3 .contact-map {
    height: 513px;
  }
}
@media (max-width: 1299px) {
  .cta-wrap3 .cta-content-wrap {
    padding: 60px 60px 36px;
  }
  .cta-wrap3 .contact-map {
    height: 532px;
  }
}
@media (max-width: 1199px) {
  .cta-wrap3 {
    display: block;
  }
  .cta-wrap3 .cta-content-wrap {
    padding: 80px 60px 56px;
  }
  .cta-wrap3 .contact-map {
    height: auto;
  }
  .cta-wrap3 .contact-map iframe {
    height: 400px;
  }
}
@media (max-width: 991px) {
  .cta-wrap3 .cta-content-wrap {
    padding: 60px 40px 36px;
  }
}
@media (max-width: 575px) {
  .cta-wrap3 .cta-content-wrap {
    padding: 60px 15px 36px;
  }
}

.cta-wrap3.style2 {
  border-radius: 0;
  max-width: none;
}

/*------------------- 4.00. Color Scheme -------------------*/
/* color scheme ---------------------------------- */
.color-scheme-wrap {
  position: fixed;
  right: 0;
  top: 50%;
  z-index: 99;
  background: var(--black-color2);
  padding: 20px;
  border-radius: 10px 0 0 10px;
  display: inline-block;
  transition: 0.4s;
}
.color-scheme-wrap .switchIcon {
  position: absolute;
  left: 0;
  top: 10px;
  border: 0;
  background: var(--theme-color);
  color: var(--white-color);
  height: 45px;
  width: 45px;
  border-radius: 5px 0 0 5px;
  transform: translate(-100%, 0);
}
.color-scheme-wrap .color-scheme-wrap-title {
  font-size: 22px;
  border-bottom: 2px solid var(--th-border-color);
  padding-bottom: 6px;
  color: var(--white-color);
  margin-bottom: 20px;
}
.color-scheme-wrap .color-scheme-wrap-title i {
  font-size: 18px;
  margin-right: 3px;
}
.color-scheme-wrap .color-scheme-wrap-subtitle {
  font-size: 18px;
  color: var(--white-color);
  margin-bottom: 20px;
}
.color-scheme-wrap .color-scheme-wrap-subtitle i {
  font-size: 18px;
  margin-right: 3px;
}
.color-scheme-wrap .secondary-color-switch-btns,
.color-scheme-wrap .color-switch-btns {
  display: inline-flex;
  flex-wrap: wrap;
  gap: 18px;
}
.color-scheme-wrap .secondary-color-switch-btns button,
.color-scheme-wrap .color-switch-btns button {
  padding: 0;
  border: 0;
  background: transparent;
  font-size: 24px;
  color: var(--theme-color);
  text-align: left;
}
.color-scheme-wrap .secondary-color-switch-btns button {
  color: var(--theme-color2);
}
.color-scheme-wrap.active {
  transform: translate(100%, 0);
}

/*------------------- 4.00. Appointment -------------------*/
/* Appointment ---------------------------------- */
.appointment-page-form {
  border-radius: 30px;
  padding: 60px;
  /* Small devices */
  /* Extra small devices */
}
@media (max-width: 767px) {
  .appointment-page-form {
    padding: 40px;
  }
}
@media (max-width: 575px) {
  .appointment-page-form {
    padding: 30px 20px;
  }
}

/*------------------- 4.00. Video -------------------*/
.video-box-center {
  position: relative;
}
.video-box-center .play-btn {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}

/* Video Area 1 -------------------------------*/
.video-thumb1-1 {
  position: relative;
  border-radius: 24px 24px 0 0;
  overflow: hidden;
}
.video-thumb1-1 img {
  height: 100%;
  width: 100%;
  object-fit: cover;
}

/* Video Area 2 -------------------------------*/
.video-thumb2-1 {
  position: relative;
}
.video-thumb2-1 .play-btn {
  --icon-size: 80px;
  --icon-font-size: 24px;
  /* Extra small devices */
}
@media (max-width: 575px) {
  .video-thumb2-1 .play-btn {
    --icon-size: 70px;
  }
}
.video-thumb2-1 .play-btn:hover:after, .video-thumb2-1 .play-btn:hover:before {
  background: var(--black-color2);
}
.video-thumb2-1 .play-btn:hover i {
  background: var(--black-color2);
  color: var(--theme-color);
}

/* Video Area 3 -------------------------------*/
.video-thumb3-1 {
  text-align: center;
  position: relative;
  /* Medium Large devices */
  /* Large devices */
  /* Medium devices */
  /* Small devices */
  /* Extra small devices */
}
.video-thumb3-1 .video-trigger-thumb {
  position: relative;
  border-radius: 50%;
  width: 1153px;
  height: 1153px;
  object-fit: cover;
  top: 0;
  display: inline-block;
}
@media (max-width: 1399px) {
  .video-thumb3-1 .video-trigger-thumb {
    width: 850px;
    height: 850px;
  }
}
@media (max-width: 1199px) {
  .video-thumb3-1 .video-trigger-thumb {
    width: 600px;
    height: 600px;
    transform: scale(1);
    top: 0;
  }
}
@media (max-width: 991px) {
  .video-thumb3-1 .video-trigger-thumb {
    width: 450px;
    height: 450px;
  }
}
@media (max-width: 767px) {
  .video-thumb3-1 .video-trigger-thumb {
    width: 350px;
    height: 350px;
  }
}
@media (max-width: 575px) {
  .video-thumb3-1 .video-trigger-thumb {
    width: 100%;
    height: auto;
    border-radius: 0;
  }
}

/* Video Area 4 -------------------------------*/
.video-thumb4-1 {
  margin-right: -140px;
  /* Hight Resoulation devices */
  /* Extra large devices */
}
@media (min-width: 1922px) {
  .video-thumb4-1 {
    margin-right: 0;
  }
}
@media (max-width: 1500px) {
  .video-thumb4-1 {
    margin-right: 0;
  }
}

/*------------------- 4.00. Video List -------------------*/
/* Video Gallery Styles */
.th-video-wrapper {
  position: relative;
}
.th-video-wrapper .filter-menu {
  text-align: center;
  margin-bottom: 60px;
  /* Small devices */
}
.th-video-wrapper .filter-menu .th-btn {
  margin: 0 10px 15px;
  padding: 12px 30px;
  font-size: 14px;
  font-weight: 500;
  border: 1px solid var(--th-border-color);
  background: transparent;
  color: var(--title-color);
  transition: all 0.3s ease;
}
.th-video-wrapper .filter-menu .th-btn:hover, .th-video-wrapper .filter-menu .th-btn.active {
  background: var(--theme-color);
  color: var(--white-color);
  border-color: var(--theme-color);
}
@media (max-width: 767px) {
  .th-video-wrapper .filter-menu .th-btn {
    margin: 0 5px 10px;
    padding: 10px 20px;
    font-size: 13px;
  }
}

/* Video Card Styles */
.th-video.video-single {
  background: var(--white-color);
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 5px 25px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}
.th-video.video-single:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
}

.video-img {
  position: relative;
  overflow: hidden;
  border-radius: 12px 12px 0 0;
}
.video-img img {
  width: 100%;
  height: 250px;
  object-fit: cover;
  transition: transform 0.3s ease;
}
.video-img .play-btn {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 3;
  --icon-size: 70px;
  --icon-font-size: 20px;
  /* Small devices */
}
@media (max-width: 767px) {
  .video-img .play-btn {
    --icon-size: 60px;
    --icon-font-size: 18px;
  }
}
.video-img .video-duration {
  position: absolute;
  bottom: 15px;
  right: 15px;
  background: rgba(var(--black-color), 0.9);
  color: var(--white-color);
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 600;
  z-index: 3;
  border: 1px solid rgba(var(--white-color), 0.2);
  backdrop-filter: blur(4px);
  box-shadow: 0 2px 8px rgba(var(--black-color), 0.3);
}
.video-img:hover img {
  transform: scale(1.05);
}

.video-content {
  padding: 25px;
}
.video-content .video-meta {
  margin-bottom: 15px;
  /* Small devices */
}
.video-content .video-meta a {
  color: var(--body-color);
  font-size: 14px;
  margin-right: 20px;
  transition: color 0.3s ease;
}
.video-content .video-meta a:hover {
  color: var(--theme-color);
}
.video-content .video-meta a i {
  margin-right: 5px;
  color: var(--theme-color);
}
@media (max-width: 767px) {
  .video-content .video-meta a {
    margin-right: 15px;
    font-size: 13px;
  }
}
.video-content .video-title {
  font-size: 20px;
  font-weight: 600;
  line-height: 1.4;
  margin-bottom: 15px;
  /* Small devices */
}
.video-content .video-title a {
  color: var(--title-color);
  transition: color 0.3s ease;
}
.video-content .video-title a:hover {
  color: var(--theme-color);
}
@media (max-width: 767px) {
  .video-content .video-title {
    font-size: 18px;
  }
}
.video-content .video-text {
  color: var(--body-color);
  line-height: 1.6;
  margin-bottom: 20px;
}
.video-content .th-btn.style2.btn-sm {
  padding: 10px 25px;
  font-size: 14px;
}

/* Featured Video Card */
.featured-video-card {
  background: var(--white-color);
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
}
.featured-video-card .video-img {
  border-radius: 16px 16px 0 0;
}
.featured-video-card .video-img img {
  height: 400px;
  /* Large devices */
  /* Medium devices */
  /* Small devices */
}
@media (max-width: 1199px) {
  .featured-video-card .video-img img {
    height: 350px;
  }
}
@media (max-width: 991px) {
  .featured-video-card .video-img img {
    height: 300px;
  }
}
@media (max-width: 767px) {
  .featured-video-card .video-img img {
    height: 250px;
  }
}
.featured-video-card .video-img .play-btn {
  --icon-size: 90px;
  --icon-font-size: 24px;
  z-index: 3;
  /* Small devices */
}
@media (max-width: 767px) {
  .featured-video-card .video-img .play-btn {
    --icon-size: 70px;
    --icon-font-size: 20px;
  }
}
.featured-video-card .video-content {
  padding: 35px;
  /* Small devices */
}
@media (max-width: 767px) {
  .featured-video-card .video-content {
    padding: 25px;
  }
}
.featured-video-card .video-content .video-title {
  font-size: 28px;
  margin-bottom: 20px;
  /* Large devices */
  /* Small devices */
}
@media (max-width: 1199px) {
  .featured-video-card .video-content .video-title {
    font-size: 24px;
  }
}
@media (max-width: 767px) {
  .featured-video-card .video-content .video-title {
    font-size: 20px;
  }
}
.featured-video-card .video-content .video-text {
  font-size: 16px;
  margin-bottom: 25px;
}

/* Video Card for Grid */
.video-card {
  background: var(--white-color);
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 5px 25px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}
.video-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
}
.video-card .video-img img {
  height: 200px;
  /* Small devices */
}
@media (max-width: 767px) {
  .video-card .video-img img {
    height: 180px;
  }
}
.video-card .video-img .play-btn {
  --icon-size: 60px;
  --icon-font-size: 18px;
  z-index: 3;
}
.video-card .video-content {
  padding: 20px;
}
.video-card .video-content .video-title {
  font-size: 18px;
  margin-bottom: 12px;
  /* Small devices */
}
@media (max-width: 767px) {
  .video-card .video-content .video-title {
    font-size: 16px;
  }
}
.video-card .video-content .video-text {
  font-size: 14px;
  margin-bottom: 15px;
}

/* 使用原项目的ripple动画，无需重复定义 */
/* Responsive Adjustments */
/* Large devices */
@media (max-width: 1199px) {
  .th-video-wrapper .filter-menu {
    margin-bottom: 50px;
  }
}
/* Medium devices */
@media (max-width: 991px) {
  .th-video-wrapper .filter-menu {
    margin-bottom: 40px;
  }
}
/* Small devices */
@media (max-width: 767px) {
  .th-video-wrapper .filter-menu {
    margin-bottom: 30px;
  }
}
/*------------------- 4.00. Video Details -------------------*/
/* Video Details Styles */
.th-video-wrapper.video-details .th-video.video-single {
  background: transparent;
  box-shadow: none;
  border-radius: 0;
}
.th-video-wrapper.video-details .th-video.video-single:hover {
  transform: none;
  box-shadow: none;
}

/* Video Player Wrapper */
.video-player-wrapper {
  margin-bottom: 40px;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
}
.video-player-wrapper .video-player {
  position: relative;
  width: 100%;
  height: 0;
  padding-bottom: 56.25%; /* 16:9 aspect ratio */
  /* Small devices */
}
.video-player-wrapper .video-player iframe {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  border: none;
  border-radius: 16px;
}
@media (max-width: 767px) {
  .video-player-wrapper .video-player {
    padding-bottom: 60%; /* Slightly different ratio for mobile */
  }
}

/* Video Details Content */
.video-details .video-content {
  padding: 0;
}
.video-details .video-content .video-meta {
  margin-bottom: 20px;
  padding-bottom: 20px;
  border-bottom: 1px solid var(--th-border-color);
  /* Small devices */
}
.video-details .video-content .video-meta a {
  color: var(--body-color);
  font-size: 15px;
  margin-right: 25px;
  font-weight: 500;
  transition: color 0.3s ease;
}
.video-details .video-content .video-meta a:hover {
  color: var(--theme-color);
}
.video-details .video-content .video-meta a i {
  margin-right: 8px;
  color: var(--theme-color);
}
@media (max-width: 767px) {
  .video-details .video-content .video-meta a {
    margin-right: 20px;
    font-size: 14px;
  }
}
.video-details .video-content .video-title {
  font-size: 32px;
  font-weight: 700;
  line-height: 1.3;
  margin-bottom: 25px;
  color: var(--title-color);
  /* Large devices */
  /* Medium devices */
  /* Small devices */
}
@media (max-width: 1199px) {
  .video-details .video-content .video-title {
    font-size: 28px;
  }
}
@media (max-width: 991px) {
  .video-details .video-content .video-title {
    font-size: 24px;
  }
}
@media (max-width: 767px) {
  .video-details .video-content .video-title {
    font-size: 20px;
    margin-bottom: 20px;
  }
}
.video-details .video-content .page-subtitle {
  color: var(--theme-color);
  font-size: 14px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 1px;
  margin-bottom: 10px;
}
.video-details .video-content .page-title {
  font-size: 24px;
  font-weight: 600;
  color: var(--title-color);
  margin-bottom: 40px;
  /* Small devices */
}
@media (max-width: 767px) {
  .video-details .video-content .page-title {
    font-size: 20px;
    margin-bottom: 30px;
  }
}
.video-details .video-content .fs-20 {
  font-size: 18px;
  line-height: 1.7;
  color: var(--body-color);
  /* Small devices */
}
.video-details .video-content .fs-20.title-font {
  font-family: var(--title-font);
  font-weight: 500;
}
@media (max-width: 767px) {
  .video-details .video-content .fs-20 {
    font-size: 16px;
  }
}
.video-details .video-content .checklist ul {
  list-style: none;
  padding: 0;
  margin: 0;
}
.video-details .video-content .checklist ul li {
  position: relative;
  padding-left: 30px;
  margin-bottom: 12px;
  color: var(--body-color);
  font-size: 16px;
  line-height: 1.6;
  /* Small devices */
}
.video-details .video-content .checklist ul li::before {
  content: "\f058";
  font-family: var(--icon-font);
  position: absolute;
  left: 0;
  top: 0;
  color: var(--theme-color);
  font-size: 16px;
}
@media (max-width: 767px) {
  .video-details .video-content .checklist ul li {
    font-size: 15px;
    padding-left: 25px;
  }
}
.video-details .video-content blockquote {
  background: var(--smoke-color);
  border-left: 4px solid var(--theme-color);
  padding: 30px;
  margin: 40px 0;
  border-radius: 8px;
  /* Small devices */
}
.video-details .video-content blockquote p {
  font-size: 18px;
  line-height: 1.7;
  color: var(--title-color);
  font-style: italic;
  margin-bottom: 20px;
  /* Small devices */
}
@media (max-width: 767px) {
  .video-details .video-content blockquote p {
    font-size: 16px;
  }
}
.video-details .video-content blockquote cite {
  font-size: 16px;
  font-weight: 600;
  color: var(--theme-color);
  font-style: normal;
}
.video-details .video-content blockquote cite::before {
  content: "— ";
}
@media (max-width: 767px) {
  .video-details .video-content blockquote {
    padding: 20px;
    margin: 30px 0;
  }
}
.video-details .video-content .blog-radius-img {
  border-radius: 12px;
  overflow: hidden;
}
.video-details .video-content .blog-radius-img img {
  width: 100%;
  height: 250px;
  object-fit: cover;
  transition: transform 0.3s ease;
  /* Small devices */
}
.video-details .video-content .blog-radius-img img:hover {
  transform: scale(1.05);
}
@media (max-width: 767px) {
  .video-details .video-content .blog-radius-img img {
    height: 200px;
  }
}
.video-details .video-content .share-links {
  margin-top: 50px;
  padding-top: 30px;
  border-top: 1px solid var(--th-border-color);
  /* Medium devices */
}
.video-details .video-content .share-links .share-links-title {
  font-weight: 600;
  color: var(--title-color);
  margin-bottom: 15px;
  display: inline-block;
}
.video-details .video-content .share-links .tagcloud a {
  display: inline-block;
  background: var(--smoke-color);
  color: var(--body-color);
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 14px;
  margin: 0 8px 8px 0;
  transition: all 0.3s ease;
}
.video-details .video-content .share-links .tagcloud a:hover {
  background: var(--theme-color);
  color: var(--white-color);
}
.video-details .video-content .share-links .th-social.style2 a {
  width: 40px;
  height: 40px;
  line-height: 40px;
  text-align: center;
  border-radius: 50%;
  background: var(--smoke-color);
  color: var(--title-color);
  margin-right: 10px;
  transition: all 0.3s ease;
}
.video-details .video-content .share-links .th-social.style2 a:hover {
  background: var(--theme-color);
  color: var(--white-color);
  transform: translateY(-2px);
}
@media (max-width: 991px) {
  .video-details .video-content .share-links .row > div:first-child {
    margin-bottom: 20px;
  }
}

/* Related Videos Section */
.related-videos-section .sec-title {
  font-size: 28px;
  font-weight: 600;
  color: var(--title-color);
  margin-bottom: 40px;
  /* Small devices */
}
@media (max-width: 767px) {
  .related-videos-section .sec-title {
    font-size: 24px;
    margin-bottom: 30px;
  }
}
.related-videos-section .th-video.video-single {
  background: var(--white-color);
  border-radius: 12px;
  overflow: hidden;
  box-shadow: 0 5px 25px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
}
.related-videos-section .th-video.video-single:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
}
.related-videos-section .th-video .video-img img {
  height: 200px;
  /* Small devices */
}
@media (max-width: 767px) {
  .related-videos-section .th-video .video-img img {
    height: 180px;
  }
}
.related-videos-section .th-video .video-img .play-btn {
  --icon-size: 60px;
  --icon-font-size: 18px;
  z-index: 3;
}
.related-videos-section .th-video .video-img .video-duration {
  position: absolute;
  bottom: 15px;
  right: 15px;
  background: rgba(var(--black-color), 0.9);
  color: var(--white-color);
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 600;
  z-index: 3;
  border: 1px solid rgba(var(--white-color), 0.2);
  backdrop-filter: blur(4px);
  box-shadow: 0 2px 8px rgba(var(--black-color), 0.3);
}
.related-videos-section .th-video .video-content {
  padding: 20px;
}
.related-videos-section .th-video .video-content .video-title {
  font-size: 18px;
  margin-bottom: 12px;
  /* Small devices */
}
@media (max-width: 767px) {
  .related-videos-section .th-video .video-content .video-title {
    font-size: 16px;
  }
}
.related-videos-section .th-video .video-content .video-text {
  font-size: 14px;
  margin-bottom: 0;
}

/* Sidebar Enhancements for Video Details */
.sidebar-area .widget.widget_search .search-form input::placeholder {
  color: var(--body-color);
}
.sidebar-area .widget .recent-post .media-img {
  position: relative;
}
.sidebar-area .widget .recent-post .media-img .play-btn-small {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 30px;
  height: 30px;
  background: rgba(var(--theme-color), 0.9);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--white-color);
  font-size: 12px;
  transition: all 0.3s ease;
  text-decoration: none;
}
.sidebar-area .widget .recent-post .media-img .play-btn-small:hover {
  background: var(--theme-color);
  transform: translate(-50%, -50%) scale(1.1);
  color: var(--white-color);
}
.sidebar-area .widget .recent-post .media-img .play-btn-small .play-btn {
  display: none;
}
.sidebar-area .widget .recent-post .media-img .play-btn-small i {
  color: inherit;
}
.sidebar-area .widget.widget_banner .widget-banner {
  background-size: cover;
  background-position: center;
  border-radius: 12px;
  overflow: hidden;
  position: relative;
}
.sidebar-area .widget.widget_banner .widget-banner::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(var(--black-color), 0.6);
}
.sidebar-area .widget.widget_banner .widget-banner .widget-banner-content {
  position: relative;
  z-index: 1;
  padding: 40px 30px;
  text-align: center;
}
.sidebar-area .widget.widget_banner .widget-banner .widget-banner-content .title {
  color: var(--white-color);
  font-size: 20px;
  margin-bottom: 15px;
}
.sidebar-area .widget.widget_banner .widget-banner .widget-banner-content .text {
  color: rgba(var(--white-color), 0.9);
  margin-bottom: 20px;
}
.sidebar-area .widget.widget_banner .widget-banner .widget-banner-content .th-btn.style2 {
  background: var(--theme-color);
  color: var(--white-color);
  border-color: var(--theme-color);
}
.sidebar-area .widget.widget_banner .widget-banner .widget-banner-content .th-btn.style2:hover {
  background: var(--white-color);
  color: var(--theme-color);
}

/*------------------- 4.00. Client -------------------*/
/* Client Card -------------------------------------*/
.client-area-1 {
  padding-bottom: 538px;
  /* Medium devices */
}
@media (max-width: 991px) {
  .client-area-1 {
    padding-bottom: 498px;
  }
}

.client-slider1 {
  border-bottom: 1px solid #B7B7B7;
}
.client-slider1 .swiper-slide {
  border-right: 1px solid #B7B7B7;
}

.client-card {
  text-align: center;
  padding: 70px 0;
  display: block;
}

/*------------------- 4.00. Tab Menu -------------------*/
/* Tab Menu 1 ------------------------------------*/
.tab-menu1 {
  gap: 10px;
  justify-content: center;
}
.tab-menu1 .tab-btn {
  font-size: 14px;
  padding: 4px 12px;
  background-color: var(--smoke-color2);
  border: 1px solid var(--th-border-color);
  border-radius: 6px;
}
.tab-menu1 .tab-btn.active {
  background-color: var(--theme-color);
  border-color: var(--theme-color);
  color: var(--white-color);
}

/*------------------- 4.00. Marquee -------------------*/
/* Marquee Area ---------------------------------- */
.marquee-slider1 {
  margin: 0 -50px -3px;
}
.marquee-slider1 .swiper-wrapper {
  transition-timing-function: linear;
}
.marquee-slider1 .swiper-slide {
  width: auto;
}

.marquee-card {
  margin-bottom: 0;
  padding: 0;
  display: inline-flex;
  align-items: center;
  /* Large devices */
  /* Small devices */
  /* Extra small devices */
}
.marquee-card a {
  font-size: 56px;
  font-weight: 700;
  font-family: var(--title-font);
  color: var(--title-color);
  display: inline-block;
  line-height: 0.75em;
  margin-left: 40px;
}
.marquee-card a:hover {
  color: var(--theme-color);
}
@media (max-width: 1199px) {
  .marquee-card a {
    font-size: 50px;
  }
}
@media (max-width: 767px) {
  .marquee-card a {
    font-size: 40px;
  }
}
@media (max-width: 575px) {
  .marquee-card a {
    font-size: 24px;
  }
}

/*------------------- 4.00. Class -------------------*/
/* Class area 1 ---------------------------------- */
.class-card {
  border-radius: 30px;
  background: var(--black-color2);
  padding: 30px 30px 0;
  text-align: center;
  position: relative;
  z-index: 1;
  margin-bottom: 28px;
}
.class-card:before, .class-card:after {
  content: "";
  position: absolute;
  inset: 0;
  border-radius: 30px;
  background: linear-gradient(transparent, var(--th-border-color5));
  z-index: -2;
  transition: 0.4s;
}
.class-card:before {
  background: var(--black-color2);
  inset: 1px;
  z-index: -1;
}
.class-card_img {
  border-radius: 15px;
  overflow: hidden;
  margin-bottom: 30px;
}
.class-card_img img {
  width: 100%;
  transition: 0.4s;
}
.class-card .box-title {
  font-weight: 500;
  margin-bottom: 15px;
}
.class-card .box-title a {
  color: var(--white-color);
}
.class-card .box-title a:hover {
  color: var(--theme-color);
}
.class-card .class-card_text {
  color: var(--light-color);
}
.class-card .btn-wrap {
  justify-content: center;
  transform: translate(0, 50%);
  margin-top: -24px;
}
.class-card .icon-btn {
  border-radius: 50%;
  background: var(--th-border-color5);
  border-color: var(--th-border-color5);
  color: var(--white-color);
  position: relative;
}
.class-card .icon-btn:after {
  content: "";
  position: absolute;
  inset: -10px -10px 50%;
  background: var(--black-color2);
  z-index: -1;
  border-radius: 50% 50% 0 0/100% 100% 0 0;
  border: 1px solid var(--th-border-color5);
  border-bottom: 0;
}
.class-card .icon-btn:hover {
  background: var(--theme-color);
  border-color: var(--theme-color);
}
.class-card:hover:after {
  background: linear-gradient(transparent, var(--theme-color));
}
.class-card:hover .class-card_img img {
  transform: scale(1.05);
}
.class-card:hover .icon-btn:after {
  border-color: var(--theme-color);
}

/* Class area 2 ---------------------------------- */
.class-area-2 {
  padding-bottom: 30px;
}

.class-bg-shape {
  position: absolute;
  inset: 0;
}

.class-slider2 {
  padding: 0 130px;
  /* Extra small devices */
}
@media (max-width: 575px) {
  .class-slider2 {
    padding: 0;
    margin: 0 15px;
  }
}

.class-card2 {
  position: relative;
}
.class-card2 .class-card_img {
  border-radius: 30px;
  margin-bottom: 0;
}
.class-card2 .box-title {
  margin-bottom: 13px;
  font-weight: 500;
}
.class-card2 .box-title a {
  color: var(--white-color);
}
.class-card2 .box-title a:hover {
  color: var(--theme-color);
}
.class-card2 .class-card_text {
  color: var(--light-color);
  margin-bottom: 22px;
}
.class-card2 .class-card_content {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  border-radius: 30px;
  background: rgba(29, 34, 41, 0.7);
  border: 1px solid var(--th-border-color5);
  backdrop-filter: blur(5.1px);
  margin: 0 10px;
  text-align: center;
  padding: 30px;
  transition: 0.4s;
}
.class-card2 .class-card_content .btn-wrap {
  justify-content: center;
}
.class-card2 .class-card_content .class-card_text {
  margin-bottom: -0.4em;
}
.class-card2 .class-card_hover-content {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  border-radius: 30px;
  background: rgba(29, 34, 41, 0.7);
  border: 1px solid var(--th-border-color5);
  backdrop-filter: blur(5.1px);
  margin: 0 10px;
  text-align: center;
  padding: 30px;
  transition: 0.4s;
  opacity: 0;
  visibility: hidden;
  transform: scaleY(0);
}
.class-card2 .class-card_hover-content .btn-wrap {
  justify-content: center;
}
.class-card2:hover .class-card_content {
  opacity: 0;
  visibility: hidden;
}
.class-card2:hover .class-card_hover-content {
  opacity: 1;
  visibility: visible;
  transform: scaleY(1);
}

/*------------------- 4.00. Gallery -------------------*/
/* Gallery Area 1 -------------------------------*/
.gallery-card {
  position: relative;
  overflow: hidden;
  display: inline-block;
  /* Medium devices */
}
.gallery-card .gallery-img {
  position: relative;
  border-radius: 24px;
  overflow: hidden;
}
.gallery-card .gallery-img img {
  width: 100%;
}
.gallery-card .gallery-img:after {
  content: "";
  position: absolute;
  inset: 0px;
  background: var(--title-color);
  opacity: 0;
  border-radius: 0px;
  transition: 0.4s;
}
.gallery-card .icon-btn {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -20%);
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  color: var(--white-color);
  z-index: 1;
  opacity: 0;
  backdrop-filter: blur(5px);
  --btn-size: 60px;
  --btn-font-size: 24px;
}
.gallery-card .icon-btn:hover {
  background: var(--theme-color);
  border-color: var(--theme-color);
  color: var(--white-color);
}
@media (max-width: 991px) {
  .gallery-card .icon-btn {
    --btn-size: 50px;
    --btn-font-size: 24px;
  }
}

.gallery-slider1 .swiper-slide:hover .gallery-img:after {
  opacity: 0.7;
}
.gallery-slider1 .swiper-slide:hover .icon-btn {
  transform: translate(-50%, -50%);
  opacity: 1;
}
.gallery-slider1 .slider-pagination {
  height: 34px;
  /* Extra small devices */
}
@media (max-width: 575px) {
  .gallery-slider1 .slider-pagination {
    display: none;
  }
}

/* Gallery Area 2 -------------------------------*/
.gallery-card2 {
  position: relative;
  margin-bottom: 0;
}
.gallery-card2 img {
  width: 100%;
}
.gallery-card2 .icon-btn {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%) scale(0);
  width: auto;
  height: auto;
  background: transparent;
  font-size: 40px;
  z-index: 1;
}
.gallery-card2:after {
  content: "";
  position: absolute;
  inset: 0;
  background: var(--title-color);
  opacity: 0;
  transition: 0.4s;
}
.gallery-card2:hover .icon-btn {
  transform: translate(-50%, -50%) scale(1);
}
.gallery-card2:hover:after {
  opacity: 0.5;
}

/*------------------- 4.00. MegaMenu -------------------*/
/*Mega menu area*********************/
.main-menu ul.mega-menu {
  position: absolute;
  top: 100%;
  left: 0 !important;
  right: 0 !important;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
  display: flex;
  width: 1200px;
  max-width: max-content;
  max-height: 500px;
  overflow: overlay;
  opacity: 0;
  visibility: hidden;
  transform-origin: top center;
  transform: scaleY(0) translateZ(100px);
  transition: opacity 500ms ease, visibility 500ms ease, transform 700ms ease;
  z-index: 99;
  background-color: var(--white-color);
  padding: 20px 10px;
  /* Medium Large devices */
  /* Large devices */
  /***scroll-bar***/
  /* Medium Large devices */
  /* Large devices */
  /* Medium devices */
}
@media (max-width: 1599px) {
  .main-menu ul.mega-menu {
    width: 1170px;
    left: -10rem !important;
  }
}
@media (max-width: 1399px) {
  .main-menu ul.mega-menu {
    left: -23rem !important;
  }
}
@media (max-width: 1199px) {
  .main-menu ul.mega-menu {
    left: -18rem !important;
  }
}
.main-menu ul.mega-menu li {
  flex: 1 1 100%;
  width: 100%;
  position: relative;
}
.main-menu ul.mega-menu a {
  font-size: 16px;
  line-height: 30px;
}
.main-menu ul.mega-menu::-webkit-scrollbar {
  width: 5px;
  height: 5px;
  border-radius: 0px;
}
.main-menu ul.mega-menu::-webkit-scrollbar-track {
  background: rgb(255, 255, 255);
  box-shadow: inset 0 0 0px rgba(0, 0, 0, 0.3);
  border-radius: 0px;
}
.main-menu ul.mega-menu::-webkit-scrollbar-thumb {
  background-color: var(--theme-color);
  background-image: -webkit-linear-gradient(45deg, rgba(255, 255, 255, 0.3) 25%, transparent 20%, transparent 50%, rgba(255, 255, 255, 0.3) 50%, rgba(255, 255, 255, 0.3) 75%, transparent 75%, transparent);
  border-radius: 0px;
}
.main-menu ul.mega-menu li li {
  padding: 2px 0;
}
.main-menu ul.mega-menu li a {
  display: inline-block;
  text-transform: capitalize;
}
.main-menu ul.mega-menu > li > a {
  display: block;
  padding: 0;
  padding-bottom: 15px;
  margin-bottom: 10px;
  text-transform: capitalize;
  letter-spacing: 1px;
  font-weight: 700;
  color: var(--title-color);
  border-color: var(--theme-color);
}
.main-menu ul.mega-menu > li > a::after, .main-menu ul.mega-menu > li > a::before {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  width: 15px;
  height: 1px;
  background-color: var(--theme-color);
}
.main-menu ul.mega-menu > li > a::after {
  width: calc(100% - 20px);
  left: 20px;
}
.main-menu ul.mega-menu > li > a:hover {
  padding-left: 0;
}
@media (max-width: 1299px) {
  .main-menu ul.mega-menu {
    width: 1060px;
  }
}
@media (max-width: 1199px) {
  .main-menu ul.mega-menu {
    width: 900px;
  }
}
@media (max-width: 991px) {
  .main-menu ul.mega-menu {
    padding: 10px 0 !important;
  }
}
.main-menu ul.mega-menu .th-mobile-menu ul li ul li {
  padding-left: 0px;
}
.main-menu ul.mega-menu .th-mobile-menu ul li a:before {
  content: "\f105";
  font-family: var(--icon-font);
  position: absolute;
  left: 0;
  top: 12px;
  margin-right: 10px;
  display: none;
}

.mega-menu-box {
  box-shadow: 0px 0px 34px rgba(0, 0, 0, 0.08);
  border-radius: 5px;
  /* Medium devices */
}
.mega-menu-box .mega-menu-img {
  position: relative;
  border-radius: 5px 5px 0 0;
  overflow: hidden;
}
.mega-menu-box .mega-menu-img:after {
  content: "";
  position: absolute;
  inset: 0;
  background: rgba(0, 0, 0, 0);
}
.mega-menu-box .mega-menu-img img {
  transition: filter 500ms ease;
  filter: blur(0px);
}
.mega-menu-box .mega-menu-img .btn-wrap {
  z-index: 1;
  background: rgba(0, 0, 0, 0.5);
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  justify-content: center;
  flex-direction: column;
  align-items: center;
  gap: 10px;
  transform: scale(1, 0);
  transition: transform 500ms ease, opacity 600ms linear;
  transform-origin: bottom center;
  opacity: 0;
}
.mega-menu-box .mega-menu-img .btn-wrap .th-btn {
  padding: 6px 18px;
  font-size: 14px;
  color: var(--white-color);
  width: max-content;
}
.mega-menu-box:hover .btn-wrap {
  transform: scale(1, 1);
  opacity: 1;
  transform-origin: top center;
}
.mega-menu-box:hover .mega-menu-img img {
  filter: blur(2px);
}
.mega-menu-box .mega-menu-title {
  margin-bottom: 0;
  text-align: center;
  line-height: normal;
  display: flex;
  justify-content: center;
  padding: 14px 0;
}
.mega-menu-box .mega-menu-title span {
  color: var(--theme-color);
  text-transform: capitalize;
  font-size: 16px;
  margin-right: 5px;
}
.mega-menu-box .mega-menu-title span:after {
  display: none;
}
.mega-menu-box .mega-menu-title a {
  color: var(--black-color2);
  line-height: normal;
}
.mega-menu-box .mega-menu-title a:after {
  display: none;
}
.mega-menu-box .mega-menu-title a:hover {
  color: var(--title-color);
}
@media (max-width: 991px) {
  .mega-menu-box .mega-menu-title a {
    padding: 0;
  }
  .mega-menu-box .mega-menu-title a:before {
    display: none;
  }
}

/*Mega menu area end*********************/
/*------------------- 4.00. Award -------------------*/
/* Award 1 ---------------------------------- */
.award-title-wrap1 {
  max-width: 437px;
}

.award-list-card {
  display: flex;
  align-items: center;
  gap: 100px;
  position: relative;
  padding-bottom: 56px;
  /* Extra large devices */
  /* Medium Large devices */
  /* Medium Large devices */
  /* Large devices */
  /* Small devices */
}
.award-list-card:not(:last-child) {
  margin-bottom: 56px;
}
.award-list-card:after {
  content: "";
  position: absolute;
  bottom: 0;
  left: -31.8%;
  right: 0;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}
.award-list-card .box-number {
  margin-right: 40px;
  font-size: 56px;
  font-weight: 700;
  font-family: var(--title-font);
  color: transparent;
  -webkit-text-stroke: 1px var(--light-color);
  letter-spacing: 0.02em;
  line-height: 0.75em;
  flex: none;
  width: 85px;
  transition: 0.4s;
}
.award-list-card .box-img {
  flex: none;
}
.award-list-card .box-content {
  flex: 1;
  padding-right: 38px;
}
.award-list-card .box-title {
  font-size: 28px;
  font-weight: 600;
  margin-bottom: 12px;
}
.award-list-card .box-title a {
  color: var(--white-color);
}
.award-list-card .box-title a:hover {
  color: var(--theme-color);
}
.award-list-card .box-text {
  font-size: 18px;
  color: var(--light-color);
}
.award-list-card .th-btn {
  margin-top: 40px;
}
.award-list-card:hover .box-number {
  color: var(--white-color);
}
@media (max-width: 1500px) {
  .award-list-card {
    gap: 60px;
  }
  .award-list-card .box-number {
    margin-right: 0;
  }
}
@media (max-width: 1399px) {
  .award-list-card {
    gap: 40px;
    padding-bottom: 40px;
  }
  .award-list-card:not(:last-child) {
    margin-bottom: 40px;
  }
  .award-list-card .box-number {
    margin-right: 0;
  }
  .award-list-card .box-title {
    font-size: 24px;
  }
  .award-list-card .box-text {
    font-size: 16px;
  }
}
@media (max-width: 1299px) {
  .award-list-card .box-img {
    flex: 0.5;
  }
}
@media (max-width: 1199px) {
  .award-list-card {
    padding-bottom: 0;
  }
  .award-list-card:not(:last-child) {
    margin-bottom: 60px;
  }
  .award-list-card:after {
    display: none;
  }
}
@media (max-width: 767px) {
  .award-list-card {
    flex-wrap: wrap;
  }
  .award-list-card .box-number {
    flex: none;
    order: 2;
    width: -webkit-fill-available;
    font-size: 40px;
  }
  .award-list-card .box-img {
    flex: none;
    width: 100%;
    order: 1;
  }
  .award-list-card .box-content {
    order: 3;
  }
}

/* Award 2 ---------------------------------- */
.award-list-card.style2 .box-number {
  -webkit-text-stroke: 1px var(--theme-color);
}
.award-list-card.style2 .box-title a {
  color: var(--title-color);
}
.award-list-card.style2 .box-title a:hover {
  color: var(--theme-color);
}
.award-list-card.style2 .box-text {
  color: var(--body-color);
}
.award-list-card.style2:after {
  background: var(--body-color);
  opacity: 0.2;
  left: 0;
}
.award-list-card.style2:hover .box-number {
  color: var(--theme-color);
}

/*------------------- 4.00. Experience -------------------*/
/* Experience Area 1---------------------------------- */
.experience-card-wrap {
  --card-space: 60px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  padding-bottom: var(--card-space);
  margin-bottom: var(--card-space);
  /* Medium devices */
  /* Extra small devices */
}
.experience-card-wrap:nth-last-child(2), .experience-card-wrap:last-child {
  margin-bottom: 0;
}
.experience-card-wrap:nth-child(2), .experience-card-wrap:first-child {
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  padding-top: var(--card-space);
}
@media (max-width: 991px) {
  .experience-card-wrap:nth-child(2) {
    border-top: 0;
    padding-top: 0;
  }
  .experience-card-wrap:nth-last-child(2) {
    margin-bottom: var(--card-space);
  }
}
@media (max-width: 575px) {
  .experience-card-wrap {
    --card-space: 45px;
  }
}

.experience-card {
  max-width: 685px;
  margin-right: 30px;
  /* Large devices */
  /* Medium devices */
}
.experience-card .experience-meta {
  font-size: 18px;
  font-weight: 500;
  margin-top: -0.3em;
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 42px;
}
.experience-card .experience-meta span,
.experience-card .experience-meta a {
  color: var(--light-color);
  position: relative;
  margin-right: 16px;
}
.experience-card .experience-meta span:after,
.experience-card .experience-meta a:after {
  content: "";
  display: inline-block;
  height: 13px;
  width: 3px;
  background-color: var(--theme-color);
  margin-left: 16px;
  position: relative;
  top: 0px;
}
.experience-card .experience-meta span:last-child,
.experience-card .experience-meta a:last-child {
  margin-right: 0;
}
.experience-card .experience-meta span:last-child:after,
.experience-card .experience-meta a:last-child:after {
  display: none;
}
.experience-card .experience-meta a:hover {
  color: var(--theme-color);
}
.experience-card .box-title {
  color: var(--white-color);
  font-size: 28px;
  font-weight: 600;
}
.experience-card .box-text {
  color: var(--light-color);
}
@media (max-width: 1199px) {
  .experience-card .experience-meta {
    margin-bottom: 30px;
  }
  .experience-card .experience-meta span,
  .experience-card .experience-meta a {
    font-size: 16px;
  }
  .experience-card .box-title {
    font-size: 24px;
  }
}
@media (max-width: 991px) {
  .experience-card {
    max-width: none;
    margin-right: 0;
  }
}

/*------------------- 4.00. Work -------------------*/
/* Work Area 1---------------------------------- */
.work-slider1 {
  margin-right: -140px;
  /* Hight Resoulation devices */
  /* Extra large devices */
}
@media (min-width: 1922px) {
  .work-slider1 {
    margin-right: 0;
  }
}
@media (max-width: 1500px) {
  .work-slider1 {
    margin-right: 0;
  }
}

.work-card {
  background: var(--title-color);
  padding: 40px;
  position: relative;
  overflow: hidden;
  z-index: 1;
  /* Large devices */
  /* Medium devices */
  /* Extra small devices */
}
.work-card .card-bg-shape {
  position: absolute;
  inset: 0;
  transition: 0.4s;
  opacity: 0;
  transform: scale(0.6);
  z-index: -1;
}
.work-card .box-icon {
  display: inline-block;
  margin-bottom: 56px;
  position: relative;
}
.work-card .box-icon:after {
  content: "";
  position: absolute;
  inset: 0;
  border-radius: 8px;
  border: 1px solid transparent;
  background: linear-gradient(0, transparent 0%, var(--theme-color) 100%) border-box;
  -webkit-mask: -webkit-linear-gradient(#fff 0 0) padding-box, -webkit-linear-gradient(#fff 0 0);
  -webkit-mask-composite: destination-out;
  mask-composite: exclude;
}
.work-card .box-icon img {
  border-radius: 8px;
}
.work-card .box-title {
  color: var(--white-color);
  font-size: 40px;
  font-weight: 600;
  margin-top: -0.5em;
  margin-bottom: 10px;
}
.work-card .box-text {
  color: var(--light-color);
}
.work-card .th-btn {
  margin-top: 56px;
}
.work-card:hover .card-bg-shape {
  transform: scale(1);
  opacity: 1;
}
@media (max-width: 1199px) {
  .work-card .box-title {
    font-size: 30px;
  }
}
@media (max-width: 991px) {
  .work-card .box-icon {
    margin-bottom: 40px;
  }
  .work-card .box-title {
    font-size: 24px;
  }
  .work-card .th-btn {
    margin-top: 40px;
  }
}
@media (max-width: 375px) {
  .work-card {
    padding: 30px;
  }
}

/* Work Area 2---------------------------------- */
.work-thumb2-1 {
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  width: 32%;
  /* Large devices */
  /* Medium devices */
}
.work-thumb2-1 img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
@media (max-width: 1199px) {
  .work-thumb2-1 {
    position: relative;
    right: 0;
    width: 100%;
    top: -150px;
  }
}
@media (max-width: 991px) {
  .work-thumb2-1 {
    top: -80px;
  }
}

.work-card-wrap {
  --space-x: 45px;
  --space-y: 50px;
  --th-border-color: rgba(206, 208, 211, 0.5);
  /* Extra large devices */
  /* Medium Large devices */
  /* Large devices */
  /* Medium devices */
  /* Small devices */
}
.work-card-wrap:not(:nth-child(3n)) {
  border-right: unset;
}
.work-card-wrap:not(:nth-last-child(-n+2)) {
  padding-bottom: var(--space-y);
}
.work-card-wrap:not(:nth-child(-n+2)) {
  padding-top: var(--space-y);
  border-top: 1px solid var(--th-border-color);
}
.work-card-wrap:nth-child(odd) {
  padding-right: var(--space-x);
}
.work-card-wrap:nth-child(even) {
  padding-left: var(--space-x);
  border-left: 1px solid var(--th-border-color);
}
.work-card-wrap:not(:nth-last-child(-n+4)) {
  border-top: 0;
}
@media (max-width: 1500px) {
  .work-card-wrap {
    --space-x: 40px;
  }
}
@media (max-width: 1299px) {
  .work-card-wrap {
    --space-x: 30px;
    --space-y: 30px;
  }
}
@media (max-width: 1199px) {
  .work-card-wrap {
    --space-x: 40px;
    --space-y: 40px;
  }
}
@media (max-width: 991px) {
  .work-card-wrap {
    --space-x: 30px;
    --space-y: 30px;
  }
}
@media (max-width: 767px) {
  .work-card-wrap:nth-child(even) {
    padding-left: 12px;
    border-left: 0;
  }
  .work-card-wrap:not(:nth-child(-n+2)) {
    border-top: 0;
    padding-top: 0;
    padding-bottom: var(--space-x);
  }
  .work-card-wrap:nth-child(odd) {
    padding-left: 12px;
    padding-right: 12px;
    padding-top: 0;
  }
  .work-card-wrap:last-child {
    padding-bottom: 0;
  }
}

.work-card2 {
  /* Extra small devices */
}
.work-card2 .box-content {
  display: flex;
  gap: 16px;
  align-items: center;
}
.work-card2 .box-icon {
  width: 64px;
  height: 64px;
  border-radius: 8px;
  background: var(--title-color);
  display: inline-flex;
  align-items: center;
  justify-content: center;
  flex: none;
  margin: 9px 0 0 9px;
  position: relative;
}
.work-card2 .box-icon:after {
  content: "";
  position: absolute;
  inset: -9px 9px 9px -9px;
  border: 1px dashed var(--title-color);
  border-radius: 8px;
}
.work-card2 .box-title {
  margin-bottom: -0.3em;
  font-size: 24px;
  font-weight: 600;
}
.work-card2 .box-text {
  margin-top: 16px;
}
@media (max-width: 575px) {
  .work-card2 .box-title {
    font-size: 20px;
  }
}

/* Work Area 3---------------------------------- */
.work-card-wrap3 {
  position: relative;
}
.work-card-wrap3 .center-line {
  position: absolute;
  height: 100%;
  width: 1px;
  background: rgba(255, 255, 255, 0.2);
  top: 0;
  left: 50%;
  opacity: 0;
  animation: heightanim 1.3s forwards 1;
  /* Medium devices */
}
@media (max-width: 991px) {
  .work-card-wrap3 .center-line {
    display: none;
  }
}

@keyframes heightanim {
  0% {
    height: 0;
    opacity: 0;
  }
  100% {
    height: 100%;
    opacity: 1;
  }
}
.work-card3 {
  max-width: 495px;
  margin: 0 auto;
}
.work-card3 .box-title {
  color: var(--white-color);
  font-size: 40px;
  font-weight: 600;
  /* Extra small devices */
}
@media (max-width: 575px) {
  .work-card3 .box-title {
    font-size: 30px;
  }
}
.work-card3 .box-text {
  color: var(--light-color);
  margin-top: 16px;
}
.work-card3 .work-tools-wrap {
  display: inline-flex;
  gap: 40px;
  margin-top: 40px;
  flex-wrap: wrap;
  justify-content: center;
}

.work-grid-content-wrap {
  position: relative;
}
.work-grid-content-wrap .top-line {
  position: absolute;
  height: 1px;
  width: 100%;
  background: rgba(255, 255, 255, 0.2);
  top: 0;
  left: 50%;
  opacity: 0;
  animation: widthanim 1.3s forwards 1;
}

@keyframes widthanim {
  0% {
    width: 0;
    opacity: 0;
  }
  100% {
    width: 100%;
    opacity: 1;
    left: 0;
  }
}
.work-grid-content {
  display: flex;
  justify-content: center;
  gap: 160px;
  /* Extra large devices */
  /* Medium devices */
}
@media (max-width: 1500px) {
  .work-grid-content {
    gap: 30px;
  }
}
@media (max-width: 991px) {
  .work-grid-content {
    flex-wrap: wrap;
    gap: 0 30px;
    padding: 25px 0;
  }
}
.work-grid-content .work-grid-card {
  display: flex;
  gap: 28px;
  align-items: center;
  padding: 33px 0;
  position: relative;
  /* Medium devices */
}
.work-grid-content .work-grid-card:after {
  content: "";
  position: absolute;
  height: 130px;
  width: 1px;
  background: rgba(255, 255, 255, 0.2);
  right: -80px;
  /* Extra large devices */
}
@media (max-width: 1500px) {
  .work-grid-content .work-grid-card:after {
    display: none;
  }
}
.work-grid-content .work-grid-card .box-icon {
  width: 64px;
  height: 64px;
  border-radius: 50%;
  border: 1px solid var(--light-color);
  display: flex;
  align-items: center;
  justify-content: center;
  flex: none;
  /* Medium Large devices */
}
@media (max-width: 1299px) {
  .work-grid-content .work-grid-card .box-icon {
    width: 55px;
    height: 55px;
  }
}
.work-grid-content .work-grid-card .box-title {
  color: var(--white-color);
  margin-bottom: -0.32em;
  font-size: 24px;
  font-weight: 600;
  /* Medium Large devices */
}
@media (max-width: 1399px) {
  .work-grid-content .work-grid-card .box-title {
    font-size: 20px;
  }
}
.work-grid-content .work-grid-card:last-child:after {
  display: none;
}
@media (max-width: 991px) {
  .work-grid-content .work-grid-card {
    display: block;
    text-align: center;
    padding: 15px 0;
  }
  .work-grid-content .work-grid-card .box-icon {
    display: inline-flex;
    margin-bottom: 25px;
  }
}

/*------------------- 4.00. Event -------------------*/
/* Event Area 1 ---------------------------------- */
.event-card {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 36px 48px;
  flex-wrap: wrap;
  /* Medium Large devices */
  /* Extra small devices */
}
.event-card:not(:last-child) {
  border-bottom: 1px solid var(--light-color);
  padding-bottom: 40px;
  margin-bottom: 40px;
}
.event-card .event-card-img {
  width: 260px;
  height: 120px;
  transition: 0.4s;
}
.event-card .event-card-img img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}
.event-card .event-title-wrap {
  margin-right: auto;
}
.event-card .event-speaker-wrap {
  margin-right: auto;
}
.event-card .event-location-wrap {
  margin-right: auto;
}
.event-card .event-card-title {
  font-size: 24px;
  font-weight: 600;
  margin-top: -0.3em;
  margin-bottom: 5px;
}
.event-card .event-card-title a {
  color: var(--title-color);
}
.event-card .event-card-title a:hover {
  color: var(--theme-color);
}
.event-card .event-card-date {
  font-size: 18px;
  font-family: var(--title-font);
  color: var(--title-color);
  margin-bottom: -0.3em;
}
.event-card .event-card-speaker {
  font-size: 18px;
  font-weight: 600;
  font-family: var(--title-font);
  margin-top: -0.3em;
  margin-bottom: 5px;
}
.event-card .event-card-time {
  font-size: 16px;
  font-weight: 400;
  font-family: var(--title-font);
  color: var(--body-color);
  margin-bottom: -0.3em;
  display: block;
}
.event-card .event-card-location {
  font-size: 18px;
  font-weight: 600;
  font-family: var(--title-font);
  margin-top: -0.3em;
  margin-bottom: 5px;
}
.event-card .event-card-location-tag {
  font-size: 16px;
  font-weight: 400;
  font-family: var(--title-font);
  color: var(--body-color);
  margin-bottom: -0.3em;
  display: block;
}
.event-card .btn-wrap {
  flex: none;
}
.event-card:hover .event-card-img {
  transform: rotate(-8deg) scale(1.1);
}
@media (max-width: 1299px) {
  .event-card {
    gap: 30px 30px;
  }
}
@media (max-width: 575px) {
  .event-card .event-card-img {
    width: 100%;
    height: 200px;
  }
  .event-card:hover .event-card-img {
    transform: none;
  }
}

/*------------------- 4.00. Story -------------------*/
/* Story Area 1 ---------------------------------- */
.story-list-wrap {
  /* Extra small devices */
}
.story-list-wrap .single-story-list {
  display: flex;
  gap: 68px;
}
.story-list-wrap .single-story-list:not(:last-child) {
  margin-bottom: 100px;
}
.story-list-wrap .single-story-list .story-date {
  flex: none;
  font-size: 16px;
  font-weight: 600;
  color: var(--title-color);
  margin-top: -0.3em;
  position: relative;
  padding-right: 64px;
  align-self: start;
}
.story-list-wrap .single-story-list .story-date:before, .story-list-wrap .single-story-list .story-date:after {
  content: "";
  position: absolute;
  width: 48px;
  height: 2px;
  background: var(--light-color);
  right: 0;
  top: 50%;
  transform: translate(0, -50%);
}
.story-list-wrap .single-story-list .story-date:before {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: var(--theme-color);
  z-index: 1;
}
.story-list-wrap .single-story-list .box-title {
  font-size: 28px;
  font-weight: 600;
  margin-bottom: 15px;
}
@media (max-width: 575px) {
  .story-list-wrap .single-story-list {
    display: block;
  }
  .story-list-wrap .single-story-list .story-date {
    display: inline-block;
    margin-bottom: 24px;
  }
  .story-list-wrap .single-story-list .box-title {
    font-size: 24px;
  }
  .story-list-wrap .single-story-list:not(:last-child) {
    margin-bottom: 50px;
  }
}

/*=================================
    05. Spacing
==================================*/
/*-- Padding Left And Right --*/
.px-5 {
  padding-right: 5px;
  padding-left: 5px;
}

.px-10 {
  padding-right: 10px;
  padding-left: 10px;
}

.px-15 {
  padding-right: 15px;
  padding-left: 15px;
}

.px-20 {
  padding-right: 20px;
  padding-left: 20px;
}

.px-25 {
  padding-right: 25px;
  padding-left: 25px;
}

.px-30 {
  padding-right: 30px;
  padding-left: 30px;
}

.px-35 {
  padding-right: 35px;
  padding-left: 35px;
}

.px-40 {
  padding-right: 40px;
  padding-left: 40px;
}

.px-45 {
  padding-right: 45px;
  padding-left: 45px;
}

.px-50 {
  padding-right: 50px;
  padding-left: 50px;
}

/*-- Padding Top And Bottom --*/
.py-5 {
  padding-top: 5px;
  padding-bottom: 5px;
}

.py-10 {
  padding-top: 10px;
  padding-bottom: 10px;
}

.py-15 {
  padding-top: 15px;
  padding-bottom: 15px;
}

.py-20 {
  padding-top: 20px;
  padding-bottom: 20px;
}

.py-25 {
  padding-top: 25px;
  padding-bottom: 25px;
}

.py-30 {
  padding-top: 30px;
  padding-bottom: 30px;
}

.py-35 {
  padding-top: 35px;
  padding-bottom: 35px;
}

.py-40 {
  padding-top: 40px;
  padding-bottom: 40px;
}

.py-45 {
  padding-top: 45px;
  padding-bottom: 45px;
}

.py-50 {
  padding-top: 50px;
  padding-bottom: 50px;
}

/*-- Padding Top --*/
.pt-5 {
  padding-top: 5px;
}

.pt-10 {
  padding-top: 10px;
}

.pt-15 {
  padding-top: 15px;
}

.pt-20 {
  padding-top: 20px;
}

.pt-25 {
  padding-top: 25px;
}

.pt-30 {
  padding-top: 30px;
}

.pt-35 {
  padding-top: 35px;
}

.pt-40 {
  padding-top: 40px;
}

.pt-45 {
  padding-top: 45px;
}

.pt-50 {
  padding-top: 50px;
}

/*-- Padding Bottom --*/
.pb-5 {
  padding-bottom: 5px;
}

.pb-10 {
  padding-bottom: 10px;
}

.pb-15 {
  padding-bottom: 15px;
}

.pb-20 {
  padding-bottom: 20px;
}

.pb-25 {
  padding-bottom: 25px;
}

.pb-30 {
  padding-bottom: 30px;
}

.pb-35 {
  padding-bottom: 35px;
}

.pb-40 {
  padding-bottom: 40px;
}

.pb-45 {
  padding-bottom: 45px;
}

.pb-50 {
  padding-bottom: 50px;
}

/*-- Padding Left --*/
.pl-5 {
  padding-left: 5px;
}

.pl-10 {
  padding-left: 10px;
}

.pl-15 {
  padding-left: 15px;
}

.pl-20 {
  padding-left: 20px;
}

.pl-25 {
  padding-left: 25px;
}

.pl-30 {
  padding-left: 30px;
}

.pl-35 {
  padding-left: 35px;
}

.pl-40 {
  padding-left: 40px;
}

.pl-45 {
  padding-left: 45px;
}

.pl-50 {
  padding-left: 50px;
}

/*-- Padding Right --*/
.pr-5 {
  padding-right: 5px;
}

.pr-10 {
  padding-right: 10px;
}

.pr-15 {
  padding-right: 15px;
}

.pr-20 {
  padding-right: 20px;
}

.pr-25 {
  padding-right: 25px;
}

.pr-30 {
  padding-right: 30px;
}

.pr-35 {
  padding-right: 35px;
}

.pr-40 {
  padding-right: 40px;
}

.pr-45 {
  padding-right: 45px;
}

.pr-50 {
  padding-right: 50px;
}

.pb-60 {
  padding-bottom: 60px;
}

.pt-60 {
  padding-top: 60px;
}

/*-- margin Left And Right --*/
.mx-5 {
  margin-right: 5px;
  margin-left: 5px;
}

.mx-10 {
  margin-right: 10px;
  margin-left: 10px;
}

.mx-15 {
  margin-right: 15px;
  margin-left: 15px;
}

.mx-20 {
  margin-right: 20px;
  margin-left: 20px;
}

.mx-25 {
  margin-right: 25px;
  margin-left: 25px;
}

.mx-30 {
  margin-right: 30px;
  margin-left: 30px;
}

.mx-35 {
  margin-right: 35px;
  margin-left: 35px;
}

.mx-40 {
  margin-right: 40px;
  margin-left: 40px;
}

.mx-45 {
  margin-right: 45px;
  margin-left: 45px;
}

.mx-50 {
  margin-right: 50px;
  margin-left: 50px;
}

/*-- margin Top And Bottom --*/
.my-5 {
  margin-top: 5px;
  margin-bottom: 5px;
}

.my-10 {
  margin-top: 10px;
  margin-bottom: 10px;
}

.my-15 {
  margin-top: 15px;
  margin-bottom: 15px;
}

.my-20 {
  margin-top: 20px;
  margin-bottom: 20px;
}

.my-25 {
  margin-top: 25px;
  margin-bottom: 25px;
}

.my-30 {
  margin-top: 30px;
  margin-bottom: 30px;
}

.my-35 {
  margin-top: 35px;
  margin-bottom: 35px;
}

.my-40 {
  margin-top: 40px;
  margin-bottom: 40px;
}

.my-45 {
  margin-top: 45px;
  margin-bottom: 45px;
}

.my-50 {
  margin-top: 50px;
  margin-bottom: 50px;
}

/*-- margin Top --*/
.mt-5 {
  margin-top: 5px;
}

.mt-10 {
  margin-top: 10px;
}

.mt-15 {
  margin-top: 15px;
}

.mt-20 {
  margin-top: 20px;
}

.mt-25 {
  margin-top: 25px;
}

.mt-30 {
  margin-top: 30px;
}

.mt-35 {
  margin-top: 35px;
}

.mt-40 {
  margin-top: 40px;
}

.mt-45 {
  margin-top: 45px;
}

.mt-50 {
  margin-top: 50px;
}

/*-- margin Bottom --*/
.mb-5 {
  margin-bottom: 5px;
}

.mb-10 {
  margin-bottom: 10px;
}

.mb-15 {
  margin-bottom: 15px;
}

.mb-20 {
  margin-bottom: 20px;
}

.mb-25 {
  margin-bottom: 25px;
}

.mb-30 {
  margin-bottom: 30px;
}

.mb-35 {
  margin-bottom: 35px;
}

.mb-40 {
  margin-bottom: 40px;
}

.mb-45 {
  margin-bottom: 45px;
}

.mb-50 {
  margin-bottom: 50px;
}

/*-- margin Left --*/
.ml-5 {
  margin-left: 5px;
}

.ml-10 {
  margin-left: 10px;
}

.ml-15 {
  margin-left: 15px;
}

.ml-20 {
  margin-left: 20px;
}

.ml-25 {
  margin-left: 25px;
}

.ml-30 {
  margin-left: 30px;
}

.ml-35 {
  margin-left: 35px;
}

.ml-40 {
  margin-left: 40px;
}

.ml-45 {
  margin-left: 45px;
}

.ml-50 {
  margin-left: 50px;
}

/*-- margin Right --*/
.mr-5 {
  margin-right: 5px;
}

.mr-10 {
  margin-right: 10px;
}

.mr-15 {
  margin-right: 15px;
}

.mr-20 {
  margin-right: 20px;
}

.mr-25 {
  margin-right: 25px;
}

.mr-30 {
  margin-right: 30px;
}

.mr-35 {
  margin-right: 35px;
}

.mr-40 {
  margin-right: 40px;
}

.mr-45 {
  margin-right: 45px;
}

.mr-50 {
  margin-right: 50px;
}

.mt-60 {
  margin-top: 60px;
}

.mt-70 {
  margin-top: 70px;
}

.mb-28 {
  margin-bottom: 28px;
}

.mb-33 {
  margin-bottom: 33px;
}

.mb-55 {
  margin-bottom: 55px;
}

.mb-60 {
  margin-bottom: 60px;
}

.mb-65 {
  margin-bottom: 65px;
}

.mb-70 {
  margin-bottom: 70px;
}

.mb-80 {
  margin-bottom: 80px;
  /* Large devices */
}
@media (max-width: 1199px) {
  .mb-80 {
    margin-bottom: 60px;
  }
}

.mt-n1 {
  margin-top: -0.25rem;
}

.mt-n2 {
  margin-top: -0.45rem;
}

.mt-n3 {
  margin-top: -0.8rem;
}

.mt-n4 {
  margin-top: -1.5rem;
}

.mt-n5 {
  margin-top: -3rem;
}

.mb-n1 {
  margin-bottom: -0.25rem;
}

.mb-n2 {
  margin-bottom: -0.45rem;
}

.mb-n3 {
  margin-bottom: -0.8rem;
}

.mb-n4 {
  margin-bottom: -1.5rem;
}

.mb-n5 {
  margin-bottom: -3rem;
}

.space,
.space-top {
  padding-top: var(--section-space);
}

.space,
.space-bottom {
  padding-bottom: var(--section-space);
}

.space-extra,
.space-extra-top {
  padding-top: calc(var(--section-space) - 30px);
}

.space-extra,
.space-extra-bottom {
  padding-bottom: calc(var(--section-space) - 30px);
}

.space-extra2,
.space-extra2-top {
  padding-top: calc(var(--section-space) - 40px);
}

.space-extra2,
.space-extra2-bottom {
  padding-bottom: calc(var(--section-space) - 40px);
}

/* Medium devices */
@media (max-width: 991px) {
  .space,
  .space-top {
    padding-top: var(--section-space-mobile);
  }
  .space,
  .space-bottom {
    padding-bottom: var(--section-space-mobile);
  }
  .space-extra,
  .space-extra-top {
    padding-top: calc(var(--section-space-mobile) - 30px);
  }
  .space-extra,
  .space-extra-bottom {
    padding-bottom: calc(var(--section-space-mobile) - 30px);
  }
  .space-top-md-none {
    padding-top: 0;
  }
  .space-extra2,
  .space-extra2-top {
    padding-top: calc(var(--section-space-mobile) - 30px);
  }
  .space-extra2,
  .space-extra2-bottom {
    padding-bottom: calc(var(--section-space-mobile) - 30px);
  }
}
/*------------------- RTL -------------------*/
/*------------------- Global -------------------*/
/* rtl Support */
[dir=rtl] {
  /*-- margin Right --*/
  /*-- Padding Left --*/
  /*-- Padding Right --*/
  /****header-default****/
  /****header-layout1****/
  /****header-layout2****/
  /******footer layout1******/
  /* Extra small devices */
  /* Team Card 2---------------------------------- */
  /* Testimonial Area 2---------------------------------- */
  /* Event Area 1---------------------------------- */
  /* Process Card ---------------------------------- */
}
[dir=rtl] .ml-5 {
  margin-right: 5px;
  margin-left: 0;
}
[dir=rtl] .ml-10 {
  margin-right: 10px;
  margin-left: 0;
}
[dir=rtl] .ml-15 {
  margin-right: 15px;
  margin-left: 0;
}
[dir=rtl] .ml-20 {
  margin-right: 20px;
  margin-left: 0;
}
[dir=rtl] .ml-25 {
  margin-right: 25px;
  margin-left: 0;
}
[dir=rtl] .ml-30 {
  margin-right: 30px;
  margin-left: 0;
}
[dir=rtl] .ml-35 {
  margin-right: 35px;
  margin-left: 0;
}
[dir=rtl] .ml-40 {
  margin-right: 40px;
  margin-left: 0;
}
[dir=rtl] .ml-45 {
  margin-right: 45px;
  margin-left: 0;
}
[dir=rtl] .ml-50 {
  margin-right: 50px;
  margin-left: 0;
}
[dir=rtl] .mr-5 {
  margin-left: 5px;
  margin-right: 0;
}
[dir=rtl] .mr-10 {
  margin-left: 10px;
  margin-right: 0;
}
[dir=rtl] .mr-15 {
  margin-left: 15px;
  margin-right: 0;
}
[dir=rtl] .mr-20 {
  margin-left: 20px;
  margin-right: 0;
}
[dir=rtl] .mr-25 {
  margin-left: 25px;
  margin-right: 0;
}
[dir=rtl] .mr-30 {
  margin-left: 30px;
  margin-right: 0;
}
[dir=rtl] .mr-35 {
  margin-left: 35px;
  margin-right: 0;
}
[dir=rtl] .mr-40 {
  margin-left: 40px;
  margin-right: 0;
}
[dir=rtl] .mr-45 {
  margin-left: 45px;
  margin-right: 0;
}
[dir=rtl] .mr-50 {
  margin-left: 50px;
  margin-right: 0;
}
[dir=rtl] .pl-5 {
  padding-left: 0;
  padding-right: 5px;
}
[dir=rtl] .pl-10 {
  padding-left: 0;
  padding-right: 10px;
}
[dir=rtl] .pl-15 {
  padding-left: 0;
  padding-right: 15px;
}
[dir=rtl] .pl-20 {
  padding-left: 0;
  padding-right: 20px;
}
[dir=rtl] .pl-25 {
  padding-left: 0;
  padding-right: 25px;
}
[dir=rtl] .pl-30 {
  padding-left: 0;
  padding-right: 30px;
}
[dir=rtl] .pl-35 {
  padding-left: 0;
  padding-right: 35px;
}
[dir=rtl] .pl-40 {
  padding-left: 0;
  padding-right: 40px;
}
[dir=rtl] .pl-45 {
  padding-left: 0;
  padding-right: 45px;
}
[dir=rtl] .pl-50 {
  padding-left: 0;
  padding-right: 50px;
}
[dir=rtl] .pr-5 {
  padding-right: 0;
  padding-left: 5px;
}
[dir=rtl] .pr-10 {
  padding-right: 0;
  padding-left: 10px;
}
[dir=rtl] .pr-15 {
  padding-right: 0;
  padding-left: 15px;
}
[dir=rtl] .pr-20 {
  padding-right: 0;
  padding-left: 20px;
}
[dir=rtl] .pr-25 {
  padding-right: 0;
  padding-left: 25px;
}
[dir=rtl] .pr-30 {
  padding-right: 0;
  padding-left: 30px;
}
[dir=rtl] .pr-35 {
  padding-right: 0;
  padding-left: 35px;
}
[dir=rtl] .pr-40 {
  padding-right: 0;
  padding-left: 40px;
}
[dir=rtl] .pr-45 {
  padding-right: 0;
  padding-left: 45px;
}
[dir=rtl] .pr-50 {
  padding-right: 0;
  padding-left: 50px;
}
[dir=rtl] .link-btn:before {
  left: auto;
  right: 0;
}
[dir=rtl] .th-btn i.fa-arrow-up-right,
[dir=rtl] .link-btn i.fa-arrow-up-right {
  transform: rotateY(180deg);
}
[dir=rtl] .th-btn:hover i.fa-arrow-up-right, [dir=rtl] .th-btn:active i.fa-arrow-up-right,
[dir=rtl] .link-btn:hover i.fa-arrow-up-right,
[dir=rtl] .link-btn:active i.fa-arrow-up-right {
  transform: rotate(-45deg) rotateY(180deg);
}
[dir=rtl] select, [dir=rtl] .form-control, [dir=rtl] .form-select, [dir=rtl] textarea, [dir=rtl] input {
  direction: ltr;
}
[dir=rtl] .checklist ul {
  padding: 0;
  text-align: right;
}
[dir=rtl] .th-menu-wrapper {
  direction: ltr;
}
[dir=rtl] .sidemenu-wrapper .closeButton {
  left: 20px;
  right: auto;
}
[dir=rtl] .preloader {
  direction: ltr;
}
[dir=rtl] .icon-box .slider-arrow:not(:last-child) {
  margin-left: 8px;
  margin-right: 0;
}
[dir=rtl] .th-social a {
  margin-left: 7px;
  margin-right: 0;
}
[dir=rtl] .th-social a:last-child {
  margin-left: 0;
}
[dir=rtl] .header-default .header-notice {
  margin-right: auto;
  margin-left: 0;
}
[dir=rtl] .main-menu ul li.menu-item-has-children > a:after,
[dir=rtl] .main-menu ul li:has(.mega-menu) > a:after,
[dir=rtl] .main-menu ul li:has(.sub-menu) > a:after {
  margin-left: 0px;
  margin-right: 5px;
  transform: rotate(0deg);
}
[dir=rtl] .main-menu ul li.menu-item-has-children > a:hover:after,
[dir=rtl] .main-menu ul li:has(.mega-menu) > a:hover:after,
[dir=rtl] .main-menu ul li:has(.sub-menu) > a:hover:after {
  transform: rotate(360deg);
}
[dir=rtl] .main-menu > ul > li:first-child {
  margin-left: 15px !important;
  margin-right: 0;
}
[dir=rtl] .main-menu > ul > li:last-child {
  margin-right: 15px !important;
  margin-left: 0;
}
[dir=rtl] .header-default .menu-area .menu-area-wrap {
  padding: 0 30px 0 18px;
  /* Extra small devices */
}
@media (max-width: 375px) {
  [dir=rtl] .header-default .menu-area .menu-area-wrap {
    padding: 0;
  }
}
[dir=rtl] .info-card-wrap {
  /* Large devices */
  /* Medium devices */
}
[dir=rtl] .info-card-wrap .info-card {
  border-right: 2px solid var(--th-border-color);
  padding-right: 40px;
  padding-left: 0;
  border-left: 0;
  /* Large devices */
}
[dir=rtl] .info-card-wrap .info-card:first-child {
  border-right: 0;
  padding-right: 0;
}
@media (max-width: 1199px) {
  [dir=rtl] .info-card-wrap .info-card {
    border: 0 !important;
    padding-right: 0;
  }
}
@media (max-width: 1199px) {
  [dir=rtl] .info-card-wrap {
    gap: 20px;
  }
}
@media (max-width: 991px) {
  [dir=rtl] .info-card-wrap .info-card .box-title {
    font-size: 16px;
  }
}
[dir=rtl] .header-layout1 .header-notice {
  margin-right: auto;
  margin-left: 0;
}
[dir=rtl] .header-layout1 .header-logo {
  margin-left: 80px;
  margin-right: 0;
}
[dir=rtl] .header-layout1 .menu-area .menu-area-wrap {
  padding: 0 30px 0 18px;
  /* Extra large devices */
}
@media (max-width: 1500px) {
  [dir=rtl] .header-layout1 .menu-area .menu-area-wrap {
    padding: 0;
  }
}
[dir=rtl] .video-bg-shape2-1 {
  transform: rotateY(180deg);
}
[dir=rtl] .header-layout2 .header-notice {
  margin-right: auto;
  margin-left: 0;
}
[dir=rtl] .header-layout2 .header-logo {
  margin-left: 80px;
  margin-right: 0;
  /* Medium Large devices */
  /* Extra small devices */
}
@media (max-width: 1299px) {
  [dir=rtl] .header-layout2 .header-logo {
    margin-left: 30px;
  }
}
@media (max-width: 575px) {
  [dir=rtl] .header-layout2 .header-logo {
    margin-left: 0;
  }
}
[dir=rtl] .header-layout2 .header-button {
  margin-right: 10px;
  margin-left: 0;
  /* Medium Large devices */
}
@media (max-width: 1299px) {
  [dir=rtl] .header-layout2 .header-button {
    margin-right: 0;
  }
}
[dir=rtl] .header-layout2 .menu-area .menu-area-wrap {
  padding: 0 30px 0 18px;
  /* Extra large devices */
}
@media (max-width: 1500px) {
  [dir=rtl] .header-layout2 .menu-area .menu-area-wrap {
    padding: 0;
  }
}
[dir=rtl] .footer-widget .widget_title:before, [dir=rtl] .footer-widget .widget_title:after {
  left: auto;
  right: 0;
}
[dir=rtl] .footer-links li {
  margin-left: 20px;
  margin-right: 0;
}
[dir=rtl] .footer-links li:last-child {
  margin-left: 0;
}
[dir=rtl] .footer-widget.widget_nav_menu a,
[dir=rtl] .footer-widget.widget_categories a,
[dir=rtl] .footer-widget.widget_archive a,
[dir=rtl] .footer-widget.widget_pages a,
[dir=rtl] .footer-widget.widget_meta a {
  padding: 0 24px 0 0px;
}
[dir=rtl] .footer-widget.widget_nav_menu a:before,
[dir=rtl] .footer-widget.widget_categories a:before,
[dir=rtl] .footer-widget.widget_archive a:before,
[dir=rtl] .footer-widget.widget_pages a:before,
[dir=rtl] .footer-widget.widget_meta a:before {
  left: auto;
  right: 0;
  transform: rotateY(180deg);
}
[dir=rtl] .footer-widget.widget_nav_menu a:hover:before,
[dir=rtl] .footer-widget.widget_categories a:hover:before,
[dir=rtl] .footer-widget.widget_archive a:hover:before,
[dir=rtl] .footer-widget.widget_pages a:hover:before,
[dir=rtl] .footer-widget.widget_meta a:hover:before {
  transform: rotateY(180deg) rotate(45deg);
}
[dir=rtl] .th-widget-contact .th-social a {
  margin: 0;
}
[dir=rtl] .widget_shopping_cart .th-btn {
  margin-left: 15px;
  margin-right: 0;
}
[dir=rtl] .widget_shopping_cart .quantity {
  flex-direction: row-reverse;
}
[dir=rtl] .blog-meta span,
[dir=rtl] .blog-meta a {
  margin-left: 30px;
  margin-right: 0;
}
[dir=rtl] .blog-meta span i,
[dir=rtl] .blog-meta a i {
  margin-left: 10px;
  margin-right: 0;
}
[dir=rtl] .blog-meta span:last-child,
[dir=rtl] .blog-meta a:last-child {
  margin-left: 0;
  padding-left: 0;
}
[dir=rtl] .blog-meta .author img {
  margin-left: 6px;
  margin-right: 0;
}
[dir=rtl] .img-box1 .img1 {
  transform: rotateY(180deg);
}
[dir=rtl] .img-box1 .img1 img {
  transform: rotateY(180deg);
}
[dir=rtl] .img-box1 .about-shape1-1 {
  right: -186px;
  left: auto;
  /* Extra large devices */
}
[dir=rtl] .img-box1 .about-shape1-1 img {
  transform: rotateY(180deg);
}
@media (max-width: 1500px) {
  [dir=rtl] .img-box1 .about-shape1-1 {
    right: 0;
  }
}
[dir=rtl] .story-card .quote-icon {
  left: 35px;
  right: auto;
}
[dir=rtl] .story-img-box1 .year-counter .year-counter_number {
  text-align: left;
}
[dir=rtl] .team-card .th-social a:first-child {
  transform: translate(15px, -35px);
}
[dir=rtl] .team-card .th-social a:nth-child(2) {
  transform: translate(25px, -5px);
}
[dir=rtl] .team-card .th-social a:nth-child(3) {
  transform: translate(-25px, -5px);
}
[dir=rtl] .team-card .th-social a:nth-child(4) {
  transform: translate(-15px, -35px);
}
[dir=rtl] .counter-card-wrap {
  --space-x: 60px;
  --space-y: 40px;
  --th-border-color: rgba(255,255,255,0.1);
  padding: 0;
  /* Extra large devices */
  /* Medium Large devices */
  /* Small devices */
  /* Extra small devices */
}
[dir=rtl] .counter-card-wrap:not(:nth-child(3n)) {
  border-left: unset;
}
[dir=rtl] .counter-card-wrap:not(:nth-last-child(-n+2)) {
  padding-bottom: var(--space-y);
}
[dir=rtl] .counter-card-wrap:not(:nth-child(-n+2)) {
  padding-top: var(--space-y);
  border-top: 2px solid var(--th-border-color);
}
[dir=rtl] .counter-card-wrap:nth-child(odd) {
  padding-left: var(--space-x);
}
[dir=rtl] .counter-card-wrap:nth-child(even) {
  padding-right: var(--space-x);
  border-right: 2px solid var(--th-border-color);
}
[dir=rtl] .counter-card-wrap:not(:nth-last-child(-n+4)) {
  border-top: 0;
}
@media (max-width: 1500px) {
  [dir=rtl] .counter-card-wrap {
    --space-x: 40px;
  }
}
@media (max-width: 1299px) {
  [dir=rtl] .counter-card-wrap {
    --space-x: 30px;
    --space-y: 30px;
  }
}
@media (max-width: 767px) {
  [dir=rtl] .counter-card-wrap:nth-child(even) {
    padding-right: 12px;
    border-right: 0;
  }
  [dir=rtl] .counter-card-wrap:not(:nth-child(-n+2)) {
    border-top: 0;
    padding-top: 0;
  }
  [dir=rtl] .counter-card-wrap:nth-child(odd) {
    padding-right: 12px;
    padding-left: 12px;
    padding-top: 0;
  }
}
@media (max-width: 575px) {
  [dir=rtl] .counter-card-wrap:not(:nth-child(-n+2)) {
    padding-bottom: var(--space-x);
  }
  [dir=rtl] .counter-card-wrap:last-child {
    padding-bottom: 0;
  }
}
[dir=rtl] .video-thumb1-1 {
  margin: -120px 0 -120px -315px;
  /* Extra large devices */
  /* Medium Large devices */
  /* Medium devices */
}
[dir=rtl] .video-thumb1-1 .play-btn {
  left: auto;
  right: 0;
  transform: translate(50%, -50%);
}
@media (max-width: 1500px) {
  [dir=rtl] .video-thumb1-1 {
    margin-left: -100px;
  }
}
@media (max-width: 1299px) {
  [dir=rtl] .video-thumb1-1 {
    margin-left: -50px;
  }
}
@media (max-width: 991px) {
  [dir=rtl] .video-thumb1-1 {
    margin: 0;
  }
  [dir=rtl] .video-thumb1-1 .play-btn {
    right: 50%;
  }
}
[dir=rtl] .testi-bg-shape1-2 img {
  transform: rotateY(180deg);
}
[dir=rtl] .testi-box-img {
  border-radius: 0 50px 300px 300px;
  /* Medium devices */
}
[dir=rtl] .testi-box-img .testi-card_review {
  right: 0;
  left: auto;
  border-radius: 0px 0 50px 50px;
}
@media (max-width: 991px) {
  [dir=rtl] .testi-box-img {
    border-radius: 300px 300px 50px 50px;
  }
  [dir=rtl] .testi-box-img .testi-card_review {
    border-radius: 50px 50px 0px 0px;
  }
}
[dir=rtl] .testi-slider1 {
  margin-right: -70px;
  margin-left: 0;
  /* Medium Large devices */
  /* Medium devices */
  /* Small devices */
  /* Extra small devices */
}
[dir=rtl] .testi-slider1 .swiper-slide {
  border-radius: 50px 0 0 50px;
}
[dir=rtl] .testi-slider1 .slider-pagination.swiper-pagination-progressbar {
  margin: 85px auto 0 30px;
}
[dir=rtl] .testi-slider1 .slider-pagination2 {
  left: 0;
  right: auto;
}
[dir=rtl] .testi-slider1 .slider-pagination2 .current-slide {
  margin-left: 270px;
  margin-right: 0;
}
[dir=rtl] .testi-slider1 .icon-box {
  margin-right: 70px;
  margin-left: 0;
}
@media (max-width: 1299px) {
  [dir=rtl] .testi-slider1 {
    margin-right: -120px;
    margin-left: 0;
  }
  [dir=rtl] .testi-slider1 .icon-box {
    margin-right: 120px;
    margin-left: 0;
  }
}
@media (max-width: 991px) {
  [dir=rtl] .testi-slider1 {
    margin-right: 0;
  }
  [dir=rtl] .testi-slider1 .icon-box {
    margin-right: 0;
  }
}
@media (max-width: 767px) {
  [dir=rtl] .testi-slider1 .slider-pagination.swiper-pagination-progressbar {
    margin: 65px auto 0 30px;
  }
  [dir=rtl] .testi-slider1 .slider-pagination2 .current-slide {
    margin-left: 220px;
    margin-right: 0;
  }
}
@media (max-width: 575px) {
  [dir=rtl] .testi-slider1 .slider-pagination.swiper-pagination-progressbar {
    margin: 40px 40px 20px 30px;
  }
  [dir=rtl] .testi-slider1 .slider-pagination2 {
    left: 0;
    right: 0;
  }
  [dir=rtl] .testi-slider1 .slider-pagination2 .current-slide {
    margin-right: 0;
    margin-left: 0;
  }
}
[dir=rtl] .testi-card {
  padding: 80px 150px 80px 80px;
  /* Medium Large devices */
  /* Large devices */
  /* Medium devices */
  /* Extra small devices */
}
[dir=rtl] .testi-card .quote-icon {
  left: 80px;
  right: auto;
  transform: rotateY(180deg);
}
@media (max-width: 1299px) {
  [dir=rtl] .testi-card {
    padding: 60px 180px 60px 60px;
  }
  [dir=rtl] .testi-card .quote-icon {
    left: 60px;
  }
}
@media (max-width: 1199px) {
  [dir=rtl] .testi-card {
    padding: 40px 160px 40px 40px;
  }
  [dir=rtl] .testi-card .quote-icon {
    left: 40px;
  }
}
@media (max-width: 991px) {
  [dir=rtl] .testi-card {
    padding: 40px;
  }
}
@media (max-width: 575px) {
  [dir=rtl] .testi-card {
    padding: 30px;
  }
  [dir=rtl] .testi-card .quote-icon {
    left: 30px;
  }
}
[dir=rtl] .project-card {
  /* Medium devices */
}
[dir=rtl] .project-card .project-content {
  right: -20px;
  left: 0;
  margin-left: 55px;
  margin-right: 0;
}
[dir=rtl] .project-card .project-content .project-card-bg-shape {
  transform: rotateY(180deg);
}
[dir=rtl] .project-card:hover .project-content {
  right: 0;
}
@media (max-width: 991px) {
  [dir=rtl] .project-card .project-content {
    margin-left: 0;
  }
}
[dir=rtl] .faq-img-box1 {
  margin-right: -345px;
  margin-left: 0;
  padding: 0 0px 67px 110px;
  /* Large devices */
  /* Small devices */
}
[dir=rtl] .faq-img-box1 .img2 {
  left: 0;
  right: auto;
}
[dir=rtl] .faq-img-box1 .img3 {
  left: 0;
  right: auto;
}
[dir=rtl] .faq-img-box1 .mask-shape {
  left: 109px;
  right: auto;
  transform: rotateY(180deg);
}
@media (max-width: 1199px) {
  [dir=rtl] .faq-img-box1 {
    margin-right: 0;
  }
}
@media (max-width: 767px) {
  [dir=rtl] .faq-img-box1 {
    padding: 0 0 40px;
  }
  [dir=rtl] .faq-img-box1 .mask-shape {
    left: 0;
  }
}
[dir=rtl] .accordion-card {
  text-align: right;
}
[dir=rtl] .accordion-card .accordion-button {
  padding: 24px 30px 24px 55px;
  text-align: right;
}
[dir=rtl] .accordion-card .accordion-button:after {
  left: 30px;
  right: auto;
}
[dir=rtl] .accordion-card .accordion-button:before {
  right: 30px;
  left: 30px;
}
[dir=rtl] .accordion-card .accordion-body {
  padding: 24px 0 30px;
}
@media (max-width: 575px) {
  [dir=rtl] .accordion-card .accordion-button {
    padding: 19px 25px 19px 55px;
  }
}
[dir=rtl] .hero-shape-2-8 {
  transform: rotateY(180deg);
}
[dir=rtl] .img-box2 {
  padding: 0 0 56px 200px;
  /* Medium Large devices */
  /* Medium Large devices */
  /* Large devices */
  /* Small devices */
  /* Extra small devices */
}
[dir=rtl] .img-box2 .img2 {
  left: 0;
  right: auto;
}
[dir=rtl] .img-box2 .img3 {
  left: 0;
  right: auto;
}
[dir=rtl] .img-box2 .about-shape2-1 {
  right: -212px;
  left: auto;
  /* Extra large devices */
}
@media (max-width: 1500px) {
  [dir=rtl] .img-box2 .about-shape2-1 {
    right: -70px;
  }
}
[dir=rtl] .img-box2 .about-shape2-2 {
  right: -50px;
  left: auto;
  /* Medium Large devices */
}
@media (max-width: 1399px) {
  [dir=rtl] .img-box2 .about-shape2-2 {
    right: 0;
  }
}
@media (max-width: 1399px) {
  [dir=rtl] .img-box2 {
    padding: 0 0px 50px 150px;
  }
}
@media (max-width: 1299px) {
  [dir=rtl] .img-box2 {
    padding: 0 0 120px 100px;
  }
}
@media (max-width: 1199px) {
  [dir=rtl] .img-box2 {
    padding: 0 0 0px 200px;
  }
}
@media (max-width: 767px) {
  [dir=rtl] .img-box2 {
    padding: 0 0 0px 70px;
  }
}
@media (max-width: 575px) {
  [dir=rtl] .img-box2 {
    padding: 0;
  }
  [dir=rtl] .img-box2 .about-shape2-2 {
    right: auto;
    left: 0;
  }
}
[dir=rtl] .video-thumb2-1 {
  margin: -120px 0 -120px -315px;
  /* Extra large devices */
  /* Medium Large devices */
  /* Medium devices */
}
@media (max-width: 1500px) {
  [dir=rtl] .video-thumb2-1 {
    margin-left: -100px;
  }
}
@media (max-width: 1299px) {
  [dir=rtl] .video-thumb2-1 {
    margin-left: -50px;
  }
}
@media (max-width: 991px) {
  [dir=rtl] .video-thumb2-1 {
    margin: 0;
  }
}
[dir=rtl] .cigmaion-card.style2 .cigmaion-card_progress-wrap .progress .progress-bar .progress-value {
  left: 0;
  right: auto;
  transform: translate(-50%, -100%);
}
[dir=rtl] .team-card2 .th-social a:last-child {
  border-bottom: 0;
}
[dir=rtl] .team-card2 .th-social a:first-child {
  transform: translate(25px, 0px);
}
[dir=rtl] .team-card2 .th-social a:nth-child(2) {
  transform: translate(25px, 0px);
}
[dir=rtl] .team-card2 .th-social a:nth-child(3) {
  transform: translate(-25px, 0px);
}
[dir=rtl] .team-card2 .th-social a:nth-child(4) {
  transform: translate(-25px, 0px);
}
[dir=rtl] .testi-thumb-slider-wrap2 .testi-box-img {
  border-radius: 0;
}
[dir=rtl] .event-card {
  direction: ltr;
}
[dir=rtl] .process-card-wrap .process-card:after {
  left: -10px;
  right: auto;
  transform: translate(-50%, 0);
}
[dir=rtl] .process-card-wrap:nth-child(2) .process-card:after {
  transform: translate(-50%, 0) rotate(20deg);
}
[dir=rtl] .hero-3 .hero-bg-3-1 {
  transform: rotateY(180deg);
  inset: 0 310px 0 0;
}
[dir=rtl] .feature-area-1 .feature-bg-wrap {
  transform: rotateY(180deg);
}
[dir=rtl] .img-box3 {
  padding: 148px 0 0 60px;
  /* Medium Large devices */
  /* Medium Large devices */
  /* Medium devices */
  /* Extra small devices */
}
[dir=rtl] .img-box3 .img2 {
  left: -10px;
  right: auto;
}
[dir=rtl] .img-box3 .about-shape3-1 {
  left: auto;
  right: -250px;
  z-index: -1;
  animation: none !important;
  transform: rotateY(180deg);
  /* Extra large devices */
}
@media (max-width: 1500px) {
  [dir=rtl] .img-box3 .about-shape3-1 {
    right: 0;
  }
}
[dir=rtl] .img-box3 .year-counter {
  left: -160px;
  right: auto;
}
@media (max-width: 1399px) {
  [dir=rtl] .img-box3 .year-counter {
    left: -140px;
  }
}
@media (max-width: 1299px) {
  [dir=rtl] .img-box3 .year-counter {
    left: -70px;
  }
}
@media (max-width: 991px) {
  [dir=rtl] .img-box3 .year-counter {
    left: 0;
  }
}
@media (max-width: 575px) {
  [dir=rtl] .img-box3 {
    padding: 0;
  }
}
[dir=rtl] .about-wrap3 {
  margin-right: 140px;
  margin-left: 0;
  /* Medium Large devices */
  /* Large devices */
}
@media (max-width: 1299px) {
  [dir=rtl] .about-wrap3 {
    margin-right: 120px;
  }
}
@media (max-width: 1199px) {
  [dir=rtl] .about-wrap3 {
    margin-right: 0;
  }
}
[dir=rtl] .service-bg-shape2-1 {
  transform: rotateY(180deg);
}
[dir=rtl] .service-bg-shape2-2 {
  margin-right: -315px;
  margin-left: 0;
  transform: rotateY(180deg);
}
[dir=rtl] .service-bg-shape2-2 img {
  width: 100%;
}
[dir=rtl] .service-bg-shape2-2 .service-bg-shape2-3 img {
  width: auto;
}
[dir=rtl] .service-card2 .icon-btn {
  left: 20px;
  right: auto;
}
[dir=rtl] .cigmaion-card.style3 .box-thumb .cigmaion-card-shape {
  left: 0;
  right: auto;
  transform: rotateY(180deg);
}
[dir=rtl] .cigmaion-card.style3 .box-thumb .cigmaion-card-tag {
  left: auto;
  right: 10px;
}
[dir=rtl] .why-img-box1 {
  margin: 0 -315px 0 0;
  /* Extra large devices */
  /* Medium Large devices */
  /* Large devices */
}
[dir=rtl] .why-img-box1 .why-img-shape {
  right: auto;
  left: 0;
  transform: rotateY(180deg);
}
@media (max-width: 1500px) {
  [dir=rtl] .why-img-box1 {
    margin-right: -100px;
  }
}
@media (max-width: 1299px) {
  [dir=rtl] .why-img-box1 {
    margin-right: -50px;
  }
}
@media (max-width: 1199px) {
  [dir=rtl] .why-img-box1 {
    margin: 0;
  }
}
[dir=rtl] .progress-bar-wrap .progress .progress-value {
  left: 0;
  right: auto;
}
[dir=rtl] .team-card3 .th-social a {
  margin-left: 16px;
  margin-right: 0;
}
[dir=rtl] .team-card3 .th-social a:last-child {
  margin-left: 0;
}
[dir=rtl] .price-card2 .price-card-title-wrap {
  border-radius: 100px 30px 30px 0;
}
[dir=rtl] .price-card2 .price-card-price-wrap {
  border-radius: 50% 50% 0 50%;
}
[dir=rtl] .price-card2 .price-card_content {
  border-radius: 30px 100px 100px 30px;
  padding: 40px 80px 40px 40px;
  /* Large devices */
}
@media (max-width: 1199px) {
  [dir=rtl] .price-card2 .price-card_content {
    padding: 0;
    border-radius: 0;
  }
}
[dir=rtl] .faq-wrap2 {
  margin-left: -15px;
  margin-right: 0;
  /* Medium Large devices */
}
@media (max-width: 1399px) {
  [dir=rtl] .faq-wrap2 {
    margin-left: 0;
  }
}
[dir=rtl] .faq-img-box2 {
  margin: -120px 0 -120px -315px;
  /* Extra large devices */
  /* Medium Large devices */
  /* Large devices */
}
[dir=rtl] .faq-img-box2 .faq-img-shape {
  right: -1px;
  left: auto;
  transform: rotateY(180deg);
}
@media (max-width: 1500px) {
  [dir=rtl] .faq-img-box2 {
    margin-left: -100px;
  }
}
@media (max-width: 1299px) {
  [dir=rtl] .faq-img-box2 {
    margin-left: -50px;
  }
}
@media (max-width: 1199px) {
  [dir=rtl] .faq-img-box2 {
    margin: 0;
  }
}
[dir=rtl] .testi-card3 {
  /* Medium Large devices */
  /* Large devices */
  /* Medium devices */
  /* Extra small devices */
}
[dir=rtl] .testi-card3 .testi-card_review {
  left: 0;
  right: auto;
  border-radius: 0px 50% 50px 50px;
}
[dir=rtl] .testi-card3 .testi-card_profile .box-thumb .quote-icon {
  right: -14px;
  left: auto;
}
[dir=rtl] .testi-card3 .testi-card_profile .testi-card_name {
  font-size: 30px;
  font-weight: 700;
  margin-bottom: 0;
}
[dir=rtl] .testi-card3 .testi-card_profile .testi-card_desig {
  font-weight: 600;
  margin-bottom: -0.3em;
}
[dir=rtl] .testi-card3 .testi-card_text {
  font-weight: 600;
  margin-bottom: -0.4em;
}
@media (max-width: 1299px) {
  [dir=rtl] .testi-card3 .testi-card_profile .testi-card_name {
    font-size: 24px;
  }
}
@media (max-width: 1199px) {
  [dir=rtl] .testi-card3 {
    padding: 60px 40px 90px;
  }
}
@media (max-width: 991px) {
  [dir=rtl] .testi-card3 {
    padding: 60px 60px 100px;
  }
}
@media (max-width: 575px) {
  [dir=rtl] .testi-card3 {
    padding: 40px 40px 80px;
  }
  [dir=rtl] .testi-card3 .testi-card_profile {
    display: block;
  }
  [dir=rtl] .testi-card3 .box-thumb {
    margin-bottom: 20px;
  }
}

/*# sourceMappingURL=style.css.map */
