/* Video Details Styles */
.th-video-wrapper {
    &.video-details {
        .th-video {
            &.video-single {
                background: transparent;
                box-shadow: none;
                border-radius: 0;
                
                &:hover {
                    transform: none;
                    box-shadow: none;
                }
            }
        }
    }
}

/* Video Player Wrapper */
.video-player-wrapper {
    margin-bottom: 40px;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.15);
    
    .video-player {
        position: relative;
        width: 100%;
        height: 0;
        padding-bottom: 56.25%; /* 16:9 aspect ratio */
        
        iframe {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            border: none;
            border-radius: 16px;
        }
        
        @include sm {
            padding-bottom: 60%; /* Slightly different ratio for mobile */
        }
    }
}

/* Video Details Content */
.video-details {
    .video-content {
        padding: 0;
        
        .video-meta {
            margin-bottom: 20px;
            padding-bottom: 20px;
            border-bottom: 1px solid $border-color;
            
            a {
                color: $body-color;
                font-size: 15px;
                margin-right: 25px;
                font-weight: 500;
                transition: color 0.3s ease;
                
                &:hover {
                    color: $theme-color;
                }
                
                i {
                    margin-right: 8px;
                    color: $theme-color;
                }
            }
            
            @include sm {
                a {
                    margin-right: 20px;
                    font-size: 14px;
                }
            }
        }
        
        .video-title {
            font-size: 32px;
            font-weight: 700;
            line-height: 1.3;
            margin-bottom: 25px;
            color: $title-color;
            
            @include lg {
                font-size: 28px;
            }
            
            @include md {
                font-size: 24px;
            }
            
            @include sm {
                font-size: 20px;
                margin-bottom: 20px;
            }
        }
        
        .page-subtitle {
            color: $theme-color;
            font-size: 14px;
            font-weight: 600;
            text-transform: uppercase;
            letter-spacing: 1px;
            margin-bottom: 10px;
        }
        
        .page-title {
            font-size: 24px;
            font-weight: 600;
            color: $title-color;
            margin-bottom: 40px;
            
            @include sm {
                font-size: 20px;
                margin-bottom: 30px;
            }
        }
        
        .fs-20 {
            font-size: 18px;
            line-height: 1.7;
            color: $body-color;
            
            &.title-font {
                font-family: $title-font;
                font-weight: 500;
            }
            
            @include sm {
                font-size: 16px;
            }
        }
        
        .checklist {
            ul {
                list-style: none;
                padding: 0;
                margin: 0;
                
                li {
                    position: relative;
                    padding-left: 30px;
                    margin-bottom: 12px;
                    color: $body-color;
                    font-size: 16px;
                    line-height: 1.6;
                    
                    &::before {
                        content: '\f058';
                        font-family: $icon-font;
                        position: absolute;
                        left: 0;
                        top: 0;
                        color: $theme-color;
                        font-size: 16px;
                    }
                    
                    @include sm {
                        font-size: 15px;
                        padding-left: 25px;
                    }
                }
            }
        }
        
        blockquote {
            background: $smoke-color;
            border-left: 4px solid $theme-color;
            padding: 30px;
            margin: 40px 0;
            border-radius: 8px;
            
            p {
                font-size: 18px;
                line-height: 1.7;
                color: $title-color;
                font-style: italic;
                margin-bottom: 20px;
                
                @include sm {
                    font-size: 16px;
                }
            }
            
            cite {
                font-size: 16px;
                font-weight: 600;
                color: $theme-color;
                font-style: normal;
                
                &::before {
                    content: '— ';
                }
            }
            
            @include sm {
                padding: 20px;
                margin: 30px 0;
            }
        }
        
        .blog-radius-img {
            border-radius: 12px;
            overflow: hidden;
            
            img {
                width: 100%;
                height: 250px;
                object-fit: cover;
                transition: transform 0.3s ease;
                
                &:hover {
                    transform: scale(1.05);
                }
                
                @include sm {
                    height: 200px;
                }
            }
        }
        
        .share-links {
            margin-top: 50px;
            padding-top: 30px;
            border-top: 1px solid $border-color;
            
            .share-links-title {
                font-weight: 600;
                color: $title-color;
                margin-bottom: 15px;
                display: inline-block;
            }
            
            .tagcloud {
                a {
                    display: inline-block;
                    background: $smoke-color;
                    color: $body-color;
                    padding: 8px 16px;
                    border-radius: 20px;
                    font-size: 14px;
                    margin: 0 8px 8px 0;
                    transition: all 0.3s ease;
                    
                    &:hover {
                        background: $theme-color;
                        color: $white-color;
                    }
                }
            }
            
            .th-social {
                &.style2 {
                    a {
                        width: 40px;
                        height: 40px;
                        line-height: 40px;
                        text-align: center;
                        border-radius: 50%;
                        background: $smoke-color;
                        color: $title-color;
                        margin-right: 10px;
                        transition: all 0.3s ease;
                        
                        &:hover {
                            background: $theme-color;
                            color: $white-color;
                            transform: translateY(-2px);
                        }
                    }
                }
            }
            
            @include md {
                .row {
                    > div {
                        &:first-child {
                            margin-bottom: 20px;
                        }
                    }
                }
            }
        }
    }
}

/* Related Videos Section */
.related-videos-section {
    .sec-title {
        font-size: 28px;
        font-weight: 600;
        color: $title-color;
        margin-bottom: 40px;
        
        @include sm {
            font-size: 24px;
            margin-bottom: 30px;
        }
    }
    
    .th-video {
        &.video-single {
            background: $white-color;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 5px 25px rgba(0, 0, 0, 0.08);
            transition: all 0.3s ease;
            
            &:hover {
                transform: translateY(-5px);
                box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
            }
        }
        
        .video-img {
            img {
                height: 200px;

                @include sm {
                    height: 180px;
                }
            }

            .play-btn {
                // 使用原项目的播放按钮样式
                --icon-size: 60px;
                --icon-font-size: 18px;
                z-index: 3; // 确保在遮罩层之上
            }

            .video-duration {
                position: absolute;
                bottom: 15px;
                right: 15px;
                background: rgba($black-color, 0.9);
                color: $white-color;
                padding: 6px 12px;
                border-radius: 6px;
                font-size: 12px;
                font-weight: 600;
                z-index: 3; // 确保在遮罩层之上
                border: 1px solid rgba($white-color, 0.2);
                backdrop-filter: blur(4px);
                box-shadow: 0 2px 8px rgba($black-color, 0.3);
            }
        }
        
        .video-content {
            padding: 20px;
            
            .video-title {
                font-size: 18px;
                margin-bottom: 12px;
                
                @include sm {
                    font-size: 16px;
                }
            }
            
            .video-text {
                font-size: 14px;
                margin-bottom: 0;
            }
        }
    }
}

/* Sidebar Enhancements for Video Details */
.sidebar-area {
    .widget {
        &.widget_search {
            .search-form {
                input {
                    &::placeholder {
                        color: $body-color;
                    }
                }
            }
        }
        
        .recent-post {
            .media-img {
                position: relative;
                
                .play-btn-small {
                    position: absolute;
                    top: 50%;
                    left: 50%;
                    transform: translate(-50%, -50%);
                    width: 30px;
                    height: 30px;
                    background: rgba($theme-color, 0.9);
                    border-radius: 50%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    color: $white-color;
                    font-size: 12px;
                    transition: all 0.3s ease;
                    text-decoration: none;
                    z-index: 3; // 确保在遮罩层之上

                    &:hover {
                        background: $theme-color;
                        transform: translate(-50%, -50%) scale(1.1);
                        color: $white-color;
                    }

                    .play-btn {
                        display: none; // 隐藏嵌套的播放按钮
                    }

                    i {
                        color: inherit;
                    }
                }
            }
        }
        
        &.widget_banner {
            .widget-banner {
                background-size: cover;
                background-position: center;
                border-radius: 12px;
                overflow: hidden;
                position: relative;
                
                &::before {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: 0;
                    right: 0;
                    bottom: 0;
                    background: rgba($black-color, 0.6);
                }
                
                .widget-banner-content {
                    position: relative;
                    z-index: 1;
                    padding: 40px 30px;
                    text-align: center;
                    
                    .title {
                        color: $white-color;
                        font-size: 20px;
                        margin-bottom: 15px;
                    }
                    
                    .text {
                        color: rgba($white-color, 0.9);
                        margin-bottom: 20px;
                    }
                    
                    .th-btn {
                        &.style2 {
                            background: $theme-color;
                            color: $white-color;
                            border-color: $theme-color;
                            
                            &:hover {
                                background: $white-color;
                                color: $theme-color;
                            }
                        }
                    }
                }
            }
        }
    }
}
