/* Video Gallery Styles */
.th-video-wrapper {
    position: relative;
    
    .filter-menu {
        text-align: center;
        margin-bottom: 60px;
        
        .th-btn {
            margin: 0 10px 15px;
            padding: 12px 30px;
            font-size: 14px;
            font-weight: 500;
            border: 1px solid $border-color;
            background: transparent;
            color: $title-color;
            transition: all 0.3s ease;
            
            &:hover,
            &.active {
                background: $theme-color;
                color: $white-color;
                border-color: $theme-color;
            }
        }
        
        @include sm {
            .th-btn {
                margin: 0 5px 10px;
                padding: 10px 20px;
                font-size: 13px;
            }
        }
    }
}

/* Video Card Styles */
.th-video {
    &.video-single {
        background: $white-color;
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 5px 25px rgba(0, 0, 0, 0.08);
        transition: all 0.3s ease;
        
        &:hover {
            transform: translateY(-5px);
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
        }
    }
}

.video-img {
    position: relative;
    overflow: hidden;
    border-radius: 12px 12px 0 0;
    
    img {
        width: 100%;
        height: 250px;
        object-fit: cover;
        transition: transform 0.3s ease;
    }
    
    .play-btn {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 70px;
        height: 70px;
        background: rgba($theme-color, 0.9);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: $white-color;
        font-size: 20px;
        transition: all 0.3s ease;
        z-index: 2;
        
        &:hover {
            background: $theme-color;
            transform: translate(-50%, -50%) scale(1.1);
            color: $white-color;
        }
        
        &::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 100px;
            height: 100px;
            border: 2px solid rgba($white-color, 0.3);
            border-radius: 50%;
            animation: ripple 2s infinite;
        }
        
        @include sm {
            width: 60px;
            height: 60px;
            font-size: 18px;
            
            &::before {
                width: 80px;
                height: 80px;
            }
        }
    }
    
    .video-duration {
        position: absolute;
        bottom: 15px;
        right: 15px;
        background: rgba($black-color, 0.8);
        color: $white-color;
        padding: 5px 10px;
        border-radius: 4px;
        font-size: 12px;
        font-weight: 500;
    }
    
    &:hover img {
        transform: scale(1.05);
    }
}

.video-content {
    padding: 25px;
    
    .video-meta {
        margin-bottom: 15px;
        
        a {
            color: $body-color;
            font-size: 14px;
            margin-right: 20px;
            transition: color 0.3s ease;
            
            &:hover {
                color: $theme-color;
            }
            
            i {
                margin-right: 5px;
                color: $theme-color;
            }
        }
        
        @include sm {
            a {
                margin-right: 15px;
                font-size: 13px;
            }
        }
    }
    
    .video-title {
        font-size: 20px;
        font-weight: 600;
        line-height: 1.4;
        margin-bottom: 15px;
        
        a {
            color: $title-color;
            transition: color 0.3s ease;
            
            &:hover {
                color: $theme-color;
            }
        }
        
        @include sm {
            font-size: 18px;
        }
    }
    
    .video-text {
        color: $body-color;
        line-height: 1.6;
        margin-bottom: 20px;
    }
    
    .th-btn {
        &.style2 {
            &.btn-sm {
                padding: 10px 25px;
                font-size: 14px;
            }
        }
    }
}

/* Featured Video Card */
.featured-video-card {
    background: $white-color;
    border-radius: 16px;
    overflow: hidden;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1);
    
    .video-img {
        border-radius: 16px 16px 0 0;
        
        img {
            height: 400px;
            
            @include lg {
                height: 350px;
            }
            
            @include md {
                height: 300px;
            }
            
            @include sm {
                height: 250px;
            }
        }
        
        .play-btn {
            width: 90px;
            height: 90px;
            font-size: 24px;
            
            &::before {
                width: 120px;
                height: 120px;
            }
            
            @include sm {
                width: 70px;
                height: 70px;
                font-size: 20px;
                
                &::before {
                    width: 100px;
                    height: 100px;
                }
            }
        }
    }
    
    .video-content {
        padding: 35px;
        
        @include sm {
            padding: 25px;
        }
        
        .video-title {
            font-size: 28px;
            margin-bottom: 20px;
            
            @include lg {
                font-size: 24px;
            }
            
            @include sm {
                font-size: 20px;
            }
        }
        
        .video-text {
            font-size: 16px;
            margin-bottom: 25px;
        }
    }
}

/* Video Card for Grid */
.video-card {
    background: $white-color;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 5px 25px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    
    &:hover {
        transform: translateY(-5px);
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.15);
    }
    
    .video-img {
        img {
            height: 200px;
            
            @include sm {
                height: 180px;
            }
        }
        
        .play-btn {
            width: 60px;
            height: 60px;
            font-size: 18px;
            
            &::before {
                width: 80px;
                height: 80px;
            }
        }
    }
    
    .video-content {
        padding: 20px;
        
        .video-title {
            font-size: 18px;
            margin-bottom: 12px;
            
            @include sm {
                font-size: 16px;
            }
        }
        
        .video-text {
            font-size: 14px;
            margin-bottom: 15px;
        }
    }
}

/* Ripple Animation */
@keyframes ripple {
    0% {
        transform: translate(-50%, -50%) scale(1);
        opacity: 1;
    }
    100% {
        transform: translate(-50%, -50%) scale(1.5);
        opacity: 0;
    }
}

/* Responsive Adjustments */
@include lg {
    .th-video-wrapper {
        .filter-menu {
            margin-bottom: 50px;
        }
    }
}

@include md {
    .th-video-wrapper {
        .filter-menu {
            margin-bottom: 40px;
        }
    }
}

@include sm {
    .th-video-wrapper {
        .filter-menu {
            margin-bottom: 30px;
        }
    }
}
